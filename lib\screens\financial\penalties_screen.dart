// lib/screens/financial/penalties_screen.dart

import 'package:flutter/material.dart';
import '../../models/db_helper.dart';
import '../../models/financial_models.dart';
import '../../measurements/measurement_styles.dart';
import '../../utils/number_utils.dart';
import '../../services/financial_state_manager.dart';
import 'widgets/financial_status_card.dart';

class PenaltiesScreen extends StatefulWidget {
  final int tailorId;
  final String tailorName;

  const PenaltiesScreen({
    super.key,
    required this.tailorId,
    required this.tailorName,
  });

  @override
  State<PenaltiesScreen> createState() => _PenaltiesScreenState();
}

class _PenaltiesScreenState extends State<PenaltiesScreen> {
  List<Penalty> _penalties = [];
  bool _isLoading = false;
  final FinancialStateManager _financialManager = FinancialStateManager();

  @override
  void initState() {
    super.initState();
    _loadPenalties();
    // تهيئة مدير الحالة المالية
    _financialManager.setCurrentWorkshop(widget.tailorId);
  }

  Future<void> _loadPenalties() async {
    setState(() => _isLoading = true);

    try {
      final penaltiesData = await DBHelper.getPenaltiesByTailorId(widget.tailorId);
      _penalties = penaltiesData.map((data) => Penalty.fromMap(data)).toList();
    } catch (e) {
      _showErrorSnackBar('خطأ في تحميل البيانات: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _addPenalty() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddEditPenaltyScreen(
          tailorId: widget.tailorId,
          tailorName: widget.tailorName,
        ),
      ),
    );

    if (result == true) {
      _loadPenalties();
      // تحديث فوري للحالة المالية
      _financialManager.refresh();
    }
  }

  Future<void> _editPenalty(Penalty penalty) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddEditPenaltyScreen(
          tailorId: widget.tailorId,
          tailorName: widget.tailorName,
          penalty: penalty,
        ),
      ),
    );

    if (result == true) {
      _loadPenalties();
      // تحديث فوري للحالة المالية
      _financialManager.refresh();
    }
  }

  Future<void> _deletePenalty(Penalty penalty) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف الغرامة "${penalty.penaltyDescription}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: MeasurementStyles.errorColor,
              foregroundColor: MeasurementStyles.whiteColor,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await DBHelper.deletePenalty(penalty.id!);
        _showSuccessSnackBar('تم حذف الغرامة بنجاح');
        _loadPenalties();
        // تحديث فوري للحالة المالية
        _financialManager.refresh();
      } catch (e) {
        _showErrorSnackBar('خطأ في حذف الغرامة: $e');
      }
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: MeasurementStyles.successColor,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: MeasurementStyles.errorColor,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: MeasurementStyles.backgroundColor,
      appBar: AppBar(
        title: Text(
          'الغرامات - ${widget.tailorName}',
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 18,
          ),
        ),
        backgroundColor: MeasurementStyles.primaryColor,
        foregroundColor: MeasurementStyles.whiteColor,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _addPenalty,
            icon: const Icon(Icons.add_rounded),
            tooltip: 'إضافة غرامة',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // كارد الحالة المالية
                FinancialStatusCard(
                  workshopId: widget.tailorId,
                  onTap: () {
                    // يمكن إضافة عملية الانتقال لصفحة الإحصائيات
                  },
                ),

                // ملخص الغرامات
                _buildPenaltiesSummary(),

                // قائمة الغرامات
                Expanded(
                  child: _penalties.isEmpty
                      ? _buildEmptyState()
                      : ListView.builder(
                          padding: const EdgeInsets.all(16),
                          itemCount: _penalties.length,
                          itemBuilder: (context, index) {
                            return _buildPenaltyCard(_penalties[index]);
                          },
                        ),
                ),
              ],
            ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _addPenalty,
        backgroundColor: MeasurementStyles.errorColor,
        foregroundColor: MeasurementStyles.whiteColor,
        icon: const Icon(Icons.warning_rounded),
        label: const Text('إضافة غرامة'),
      ),
    );
  }

  Widget _buildPenaltiesSummary() {
    final totalPenalties = _penalties.fold(0.0, (sum, penalty) => sum + penalty.penaltyAmount);

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: MeasurementStyles.errorColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: MeasurementStyles.errorColor.withOpacity(0.3),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: MeasurementStyles.errorColor,
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.warning_rounded,
              color: MeasurementStyles.whiteColor,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'إجمالي الغرامات',
                  style: MeasurementStyles.cardSubtitleStyle,
                ),
                Text(
                  '${NumberUtils.formatIntegerDisplay(totalPenalties)} ريال',
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w700,
                    color: MeasurementStyles.errorColor,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: MeasurementStyles.errorColor,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              '${_penalties.length} غرامة',
              style: const TextStyle(
                color: MeasurementStyles.whiteColor,
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.check_circle_outline_rounded,
            size: 80,
            color: MeasurementStyles.successColor.withOpacity(0.5),
          ),
          const SizedBox(height: 16),
          const Text(
            'لا توجد غرامات',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: MeasurementStyles.textSecondaryColor,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'لم يتم تسجيل أي غرامات لهذا الخياط',
            style: MeasurementStyles.cardSubtitleStyle,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPenaltyCard(Penalty penalty) {
    return MeasurementStyles.modernCard(
      margin: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // رأس البطاقة
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: MeasurementStyles.errorColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.warning_rounded,
                  color: MeasurementStyles.errorColor,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      penalty.penaltyDescription,
                      style: MeasurementStyles.cardTitleStyle,
                    ),
                    Text(
                      penalty.formattedDate,
                      style: MeasurementStyles.cardSubtitleStyle,
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: MeasurementStyles.errorColor,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${NumberUtils.formatIntegerDisplay(penalty.penaltyAmount)} ريال',
                  style: const TextStyle(
                    color: MeasurementStyles.whiteColor,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // أزرار التحكم
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _editPenalty(penalty),
                  icon: const Icon(Icons.edit_rounded, size: 18),
                  label: const Text('تعديل'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: MeasurementStyles.primaryColor,
                    foregroundColor: MeasurementStyles.whiteColor,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _deletePenalty(penalty),
                  icon: const Icon(Icons.delete_rounded, size: 18),
                  label: const Text('حذف'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: MeasurementStyles.errorColor,
                    foregroundColor: MeasurementStyles.whiteColor,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

// شاشة إضافة/تعديل الغرامة
class AddEditPenaltyScreen extends StatefulWidget {
  final int tailorId;
  final String tailorName;
  final Penalty? penalty;

  const AddEditPenaltyScreen({
    super.key,
    required this.tailorId,
    required this.tailorName,
    this.penalty,
  });

  @override
  State<AddEditPenaltyScreen> createState() => _AddEditPenaltyScreenState();
}

class _AddEditPenaltyScreenState extends State<AddEditPenaltyScreen> {
  final _formKey = GlobalKey<FormState>();
  final _descriptionController = TextEditingController();
  final _amountController = TextEditingController();

  DateTime _selectedDate = DateTime.now();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (widget.penalty != null) {
      _descriptionController.text = widget.penalty!.penaltyDescription;
      _amountController.text = NumberUtils.formatForInput(widget.penalty!.penaltyAmount);
      _selectedDate = widget.penalty!.dateTime;
    }
  }

  @override
  void dispose() {
    _descriptionController.dispose();
    _amountController.dispose();
    super.dispose();
  }

  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );

    if (date != null) {
      setState(() => _selectedDate = date);
    }
  }

  Future<void> _savePenalty() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final penaltyData = {
        'tailorId': widget.tailorId,
        'penaltyDescription': _descriptionController.text.trim(),
        'penaltyAmount': NumberUtils.parseDouble(_amountController.text),
        'penaltyDate': _selectedDate.toIso8601String(),
      };

      if (widget.penalty != null) {
        // تحديث الغرامة الموجودة
        await DBHelper.updatePenalty(widget.penalty!.id!, penaltyData);
        _showSuccessSnackBar('تم تحديث الغرامة بنجاح');
      } else {
        // إضافة غرامة جديدة
        await DBHelper.insertPenalty(penaltyData);
        _showSuccessSnackBar('تم إضافة الغرامة بنجاح');
      }

      Navigator.pop(context, true);
    } catch (e) {
      _showErrorSnackBar('خطأ في حفظ البيانات: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: MeasurementStyles.successColor,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: MeasurementStyles.errorColor,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isEdit = widget.penalty != null;

    return Scaffold(
      backgroundColor: MeasurementStyles.backgroundColor,
      appBar: AppBar(
        title: Text(
          '${isEdit ? 'تعديل' : 'إضافة'} غرامة - ${widget.tailorName}',
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 18,
          ),
        ),
        backgroundColor: MeasurementStyles.primaryColor,
        foregroundColor: MeasurementStyles.whiteColor,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: MeasurementStyles.modernCard(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                MeasurementStyles.sectionTitle(
                  'بيانات الغرامة',
                  icon: Icons.warning_rounded,
                ),

                const SizedBox(height: 16),

                // وصف الغرامة
                TextFormField(
                  controller: _descriptionController,
                  keyboardType: TextInputType.text,
                  textAlign: TextAlign.right,
                  style: MeasurementStyles.normalTextStyle,
                  decoration: MeasurementStyles.inputDecoration(
                    'وصف الغرامة',
                    icon: Icons.description_rounded,
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'يرجى إدخال وصف الغرامة';
                    }
                    return null;
                  },
                ),

                const SizedBox(height: 16),

                // مبلغ الغرامة
                TextFormField(
                  controller: _amountController,
                  keyboardType: TextInputType.number,
                  inputFormatters: NumberUtils.integerInputFormatters,
                  textAlign: TextAlign.center,
                  style: MeasurementStyles.normalTextStyle,
                  decoration: MeasurementStyles.inputDecoration(
                    'مبلغ الغرامة (ريال)',
                    icon: Icons.attach_money_rounded,
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'يرجى إدخال مبلغ الغرامة';
                    }
                    final amount = NumberUtils.parseInteger(value);
                    if (amount <= 0) {
                      return 'يرجى إدخال مبلغ صحيح';
                    }
                    return null;
                  },
                ),

                const SizedBox(height: 16),

                // تاريخ الغرامة
                InkWell(
                  onTap: _selectDate,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                    decoration: BoxDecoration(
                      color: MeasurementStyles.surfaceColor,
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(color: const Color(0xFFE2E8F0)),
                    ),
                    child: Row(
                      children: [
                        const Icon(
                          Icons.calendar_today_rounded,
                          color: MeasurementStyles.textSecondaryColor,
                          size: 20,
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'تاريخ الغرامة',
                                style: TextStyle(
                                  color: MeasurementStyles.textSecondaryColor,
                                  fontSize: 14,
                                ),
                              ),
                              Text(
                                '${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}',
                                style: MeasurementStyles.normalTextStyle,
                              ),
                            ],
                          ),
                        ),
                        const Icon(
                          Icons.arrow_drop_down_rounded,
                          color: MeasurementStyles.textSecondaryColor,
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 20),

                // زر الحفظ
                MeasurementStyles.saveButton(
                  isEdit ? 'تحديث الغرامة' : 'إضافة الغرامة',
                  _savePenalty,
                  isLoading: _isLoading,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
