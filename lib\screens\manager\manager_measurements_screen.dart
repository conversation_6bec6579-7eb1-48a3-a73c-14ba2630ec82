import 'package:flutter/material.dart';
import '../../models/db_helper.dart';
import 'add_manager_measurement_screen.dart';
import 'manager_measurement_details_screen.dart';
import '../../widgets/export_print_buttons.dart';

class ManagerMeasurementsScreen extends StatefulWidget {
  const ManagerMeasurementsScreen({super.key});

  @override
  State<ManagerMeasurementsScreen> createState() => _ManagerMeasurementsScreenState();
}

class _ManagerMeasurementsScreenState extends State<ManagerMeasurementsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  List<Map<String, dynamic>> _clothingMeasurements = [];
  List<Map<String, dynamic>> _suitMeasurements = [];
  List<Map<String, dynamic>> _womenMeasurements = [];

  List<Map<String, dynamic>> _filteredClothingMeasurements = [];
  List<Map<String, dynamic>> _filteredSuitMeasurements = [];
  List<Map<String, dynamic>> _filteredWomenMeasurements = [];

  bool _isLoading = true;
  final _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadAllMeasurements();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      _filteredClothingMeasurements = _clothingMeasurements.where((measurement) {
        final customerName = (measurement['customerName'] ?? '').toString().toLowerCase();
        final phoneNumber = (measurement['phoneNumber'] ?? '').toString().toLowerCase();
        final billNumber = (measurement['billNumber'] ?? '').toString().toLowerCase();
        return customerName.contains(query) ||
               phoneNumber.contains(query) ||
               billNumber.contains(query);
      }).toList();

      _filteredSuitMeasurements = _suitMeasurements.where((measurement) {
        final customerName = (measurement['customerName'] ?? '').toString().toLowerCase();
        final phoneNumber = (measurement['phoneNumber'] ?? '').toString().toLowerCase();
        final billNumber = (measurement['billNumber'] ?? '').toString().toLowerCase();
        return customerName.contains(query) ||
               phoneNumber.contains(query) ||
               billNumber.contains(query);
      }).toList();

      _filteredWomenMeasurements = _womenMeasurements.where((measurement) {
        final customerName = (measurement['customerName'] ?? '').toString().toLowerCase();
        final phoneNumber = (measurement['phoneNumber'] ?? '').toString().toLowerCase();
        final billNumber = (measurement['billNumber'] ?? '').toString().toLowerCase();
        return customerName.contains(query) ||
               phoneNumber.contains(query) ||
               billNumber.contains(query);
      }).toList();
    });
  }

  Future<void> _loadAllMeasurements() async {
    setState(() => _isLoading = true);
    try {
      final clothingResults = await DBHelper.getAllManagerClothingMeasurements();
      final suitResults = await DBHelper.getAllManagerSuitMeasurements();
      final womenResults = await DBHelper.getAllManagerWomenMeasurements();

      setState(() {
        _clothingMeasurements = clothingResults;
        _suitMeasurements = suitResults;
        _womenMeasurements = womenResults;

        _filteredClothingMeasurements = List.from(clothingResults);
        _filteredSuitMeasurements = List.from(suitResults);
        _filteredWomenMeasurements = List.from(womenResults);

        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل المقاسات: $e'),
            backgroundColor: Colors.red[600],
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: Colors.grey[50],
        body: Column(
          children: [
            // عنوان الصفحة
            _buildPageHeader(),

            // شريط البحث
            if (_hasAnyMeasurements()) _buildSearchBar(),

            // علامات التبويب
            _buildTabBar(),

            // محتوى التبويبات
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : TabBarView(
                      controller: _tabController,
                      children: [
                        _buildMeasurementsList(_filteredClothingMeasurements, 'clothing'),
                        _buildMeasurementsList(_filteredSuitMeasurements, 'suits'),
                        _buildMeasurementsList(_filteredWomenMeasurements, 'women'),
                      ],
                    ),
            ),
          ],
        ),
        floatingActionButton: FloatingActionButton.extended(
          onPressed: _addNewMeasurement,
          backgroundColor: Colors.purple[600],
          foregroundColor: Colors.white,
          icon: const Icon(Icons.add),
          label: const Text('إضافة مقاس'),
          heroTag: 'manager_measurements_fab',
        ),
      ),
    );
  }

  Widget _buildPageHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      decoration: BoxDecoration(
        color: Colors.purple[700],
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
      ),
      child: SafeArea(
        child: Row(
          children: [
            const Expanded(
              child: Text(
                'إدارة المقاسات',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
            IconButton(
              icon: const Icon(Icons.refresh, color: Colors.white),
              onPressed: _loadAllMeasurements,
              tooltip: 'تحديث',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'البحث باسم العميل أو رقم الهاتف أو رقم الفاتورة...',
          prefixIcon: Icon(Icons.search, color: Colors.purple[600]),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.grey[300]!),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.purple[600]!, width: 2),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.grey[300]!),
          ),
          filled: true,
          fillColor: Colors.white,
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        ),
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: Colors.white,
      child: TabBar(
        controller: _tabController,
        labelColor: Colors.purple[700],
        unselectedLabelColor: Colors.grey[600],
        indicatorColor: Colors.purple[700],
        indicatorWeight: 3,
        tabs: const [
          Tab(
            icon: Icon(Icons.checkroom),
            text: 'الثياب',
          ),
          Tab(
            icon: Icon(Icons.business_center),
            text: 'البدلات',
          ),
          Tab(
            icon: Icon(Icons.woman),
            text: 'النسائية',
          ),
        ],
      ),
    );
  }

  Widget _buildMeasurementsList(List<Map<String, dynamic>> measurements, String type) {
    if (measurements.isEmpty) {
      return _buildEmptyState(type);
    }

    return Column(
      children: [
        // أزرار التصدير للمقاسات
        Container(
          margin: const EdgeInsets.all(16),
          child: ExportPrintButtons(
            data: measurements,
            type: 'measurements',
            title: 'مقاسات $type - نظام المدير',
            showIndividual: false,
            showBulk: true,
          ),
        ),

        // قائمة المقاسات
        Expanded(
          child: RefreshIndicator(
            onRefresh: _loadAllMeasurements,
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: measurements.length,
              itemBuilder: (context, index) {
                final measurement = measurements[index];
                return _buildMeasurementCard(measurement, type);
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState(String type) {
    String typeName;
    IconData icon;

    switch (type) {
      case 'clothing':
        typeName = 'الثياب';
        icon = Icons.checkroom;
        break;
      case 'suits':
        typeName = 'البدلات';
        icon = Icons.business_center;
        break;
      case 'women':
        typeName = 'النسائية';
        icon = Icons.woman;
        break;
      default:
        typeName = 'المقاسات';
        icon = Icons.straighten;
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد مقاسات $typeName',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'اضغط على "إضافة مقاس" لبدء التسجيل',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildMeasurementCard(Map<String, dynamic> measurement, String type) {
    final createdAt = DateTime.tryParse(measurement['createdAt'] ?? '') ?? DateTime.now();
    final formattedDate = '${createdAt.day}/${createdAt.month}/${createdAt.year}';
    final price = measurement['price'] ?? 0;
    final paid = measurement['paid'] ?? 0;
    final remaining = measurement['remaining'] ?? 0;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: remaining > 0 ? Colors.orange[200]! : Colors.green[200]!,
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () => _viewMeasurementDetails(measurement, type),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: remaining > 0 ? Colors.orange[100] : Colors.green[100],
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        remaining > 0 ? 'متبقي مبلغ' : 'مكتمل الدفع',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: remaining > 0 ? Colors.orange[700] : Colors.green[700],
                        ),
                      ),
                    ),
                    const Spacer(),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          formattedDate,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                        if (measurement['dayName'] != null)
                          Text(
                            measurement['dayName'],
                            style: TextStyle(
                              fontSize: 11,
                              color: Colors.purple[600],
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                      ],
                    ),
                    const SizedBox(width: 8),
                    PopupMenuButton<String>(
                      onSelected: (value) {
                        if (value == 'edit') {
                          _editMeasurement(measurement, type);
                        } else if (value == 'delete') {
                          _deleteMeasurement(measurement, type);
                        } else if (value == 'details') {
                          _viewMeasurementDetails(measurement, type);
                        }
                      },
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'details',
                          child: Row(
                            children: [
                              Icon(Icons.info, size: 18),
                              SizedBox(width: 8),
                              Text('تفاصيل'),
                            ],
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'edit',
                          child: Row(
                            children: [
                              Icon(Icons.edit, size: 18),
                              SizedBox(width: 8),
                              Text('تعديل'),
                            ],
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'delete',
                          child: Row(
                            children: [
                              Icon(Icons.delete, size: 18, color: Colors.red),
                              SizedBox(width: 8),
                              Text('حذف', style: TextStyle(color: Colors.red)),
                            ],
                          ),
                        ),
                      ],
                      child: Icon(Icons.more_vert, color: Colors.grey[600]),
                    ),
                  ],
                ),

                const SizedBox(height: 12),

                Row(
                  children: [
                    Icon(Icons.receipt, color: Colors.purple[600], size: 20),
                    const SizedBox(width: 8),
                    Text(
                      'رقم الفاتورة: ${measurement['billNumber'] ?? 'غير محدد'}',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 8),

                Row(
                  children: [
                    Icon(Icons.person, color: Colors.grey[600], size: 18),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'العميل: ${measurement['customerName'] ?? ''}',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[700],
                        ),
                      ),
                    ),
                  ],
                ),

                if (measurement['phoneNumber'] != null && measurement['phoneNumber'].toString().isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(top: 4),
                    child: Row(
                      children: [
                        Icon(Icons.phone, color: Colors.grey[600], size: 18),
                        const SizedBox(width: 8),
                        Text(
                          'الهاتف: ${measurement['phoneNumber']}',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[700],
                          ),
                        ),
                      ],
                    ),
                  ),

                const SizedBox(height: 8),

                Row(
                  children: [
                    Expanded(
                      child: _buildPriceInfo('المبلغ', price, Colors.blue),
                    ),
                    Expanded(
                      child: _buildPriceInfo('المدفوع', paid, Colors.green),
                    ),
                    Expanded(
                      child: _buildPriceInfo('المتبقي', remaining, Colors.orange),
                    ),
                  ],
                ),

                const SizedBox(height: 12),

                // أزرار التصدير للمقاس
                ExportPrintButtons(
                  data: measurement,
                  type: 'measurements',
                  title: 'مقاس ${measurement['customerName']} - $type',
                  showIndividual: true,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPriceInfo(String label, dynamic value, Color color) {
    return Column(
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
        const SizedBox(height: 2),
        Text(
          '${value ?? 0} ريال',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  bool _hasAnyMeasurements() {
    return _clothingMeasurements.isNotEmpty ||
           _suitMeasurements.isNotEmpty ||
           _womenMeasurements.isNotEmpty;
  }

  Future<void> _addNewMeasurement() async {
    final currentTab = _tabController.index;
    String measurementType;

    switch (currentTab) {
      case 0:
        measurementType = 'clothing';
        break;
      case 1:
        measurementType = 'suits';
        break;
      case 2:
        measurementType = 'women';
        break;
      default:
        measurementType = 'clothing';
    }

    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => AddManagerMeasurementScreen(measurementType: measurementType),
      ),
    );

    if (result == true) {
      _loadAllMeasurements();
    }
  }

  Future<void> _editMeasurement(Map<String, dynamic> measurement, String type) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => AddManagerMeasurementScreen(
          measurementType: type,
          measurementToEdit: measurement,
        ),
      ),
    );

    if (result == true) {
      _loadAllMeasurements();
    }
  }

  Future<void> _viewMeasurementDetails(Map<String, dynamic> measurement, String type) async {
    await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => ManagerMeasurementDetailsScreen(
          measurement: measurement,
          measurementType: type,
        ),
      ),
    );
  }

  Future<void> _deleteMeasurement(Map<String, dynamic> measurement, String type) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف مقاس "${measurement['customerName']}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await DBHelper.deleteManagerMeasurement(measurement['id'], type);
        _loadAllMeasurements();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم حذف المقاس بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في حذف المقاس: $e'),
              backgroundColor: Colors.red[600],
            ),
          );
        }
      }
    }
  }
}
