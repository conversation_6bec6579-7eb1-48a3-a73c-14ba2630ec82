// ملف اختبار للخدمة البديلة (بدون خطوط خارجية)
import 'package:flutter/material.dart';
import 'widgets/export_print_buttons.dart';

class TestFallbackPDF extends StatelessWidget {
  const TestFallbackPDF({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'اختبار PDF البديل',
      theme: ThemeData(
        primarySwatch: Colors.orange,
        fontFamily: 'Cairo',
      ),
      home: const TestFallbackPDFScreen(),
    );
  }
}

class TestFallbackPDFScreen extends StatelessWidget {
  const TestFallbackPDFScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار PDF البديل'),
        backgroundColor: Colors.orange[600],
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Icon(
              Icons.backup,
              size: 80,
              color: Colors.orange,
            ),
            const SizedBox(height: 20),
            const Text(
              'اختبار نظام PDF البديل',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.orange,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 10),
            const Text(
              'يعمل بدون الحاجة لملفات خطوط خارجية',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.green[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.green[200]!),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '✅ مميزات الخدمة البديلة:',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    '• يعمل فوراً بدون إعداد إضافي',
                    style: TextStyle(fontSize: 14, color: Colors.green),
                  ),
                  Text(
                    '• لا يحتاج ملفات خطوط خارجية',
                    style: TextStyle(fontSize: 14, color: Colors.green),
                  ),
                  Text(
                    '• نصوص ثنائية اللغة (عربي + إنجليزي)',
                    style: TextStyle(fontSize: 14, color: Colors.green),
                  ),
                  Text(
                    '• حل مؤقت حتى إضافة الخطوط العربية',
                    style: TextStyle(fontSize: 14, color: Colors.green),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 30),
            
            // اختبار معمل
            _buildTestCard(
              'اختبار معمل',
              'تصدير معمل بنصوص ثنائية',
              Icons.business,
              Colors.indigo,
              () => _testWorkshop(context),
            ),
            
            const SizedBox(height: 16),
            
            // اختبار أعمال
            _buildTestCard(
              'اختبار أعمال',
              'تصدير أعمال بنصوص ثنائية',
              Icons.work,
              Colors.green,
              () => _testWorks(context),
            ),
            
            const SizedBox(height: 16),
            
            // اختبار إحصائيات
            _buildTestCard(
              'اختبار إحصائيات',
              'تصدير إحصائيات بنصوص ثنائية',
              Icons.analytics,
              Colors.purple,
              () => _testStatistics(context),
            ),
            
            const SizedBox(height: 30),
            
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: const Column(
                children: [
                  Icon(Icons.info, color: Colors.blue, size: 32),
                  SizedBox(height: 8),
                  Text(
                    'حل مؤقت فعال',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    'هذا الحل يعمل فوراً ويحل مشكلة النصوص العربية مؤقتاً حتى إضافة الخطوط العربية',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.blue,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTestCard(String title, String description, IconData icon, Color color, VoidCallback onTap) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: color.withOpacity(0.3)),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(icon, color: color, size: 24),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              Icon(Icons.arrow_forward_ios, color: color, size: 16),
            ],
          ),
        ),
      ),
    );
  }

  void _testWorkshop(BuildContext context) {
    final testWorkshop = {
      'id': 1,
      'name': 'معمل الخياطة المتقدم',
      'ownerPhone': '0501234567',
      'workType': 'ثياب رجالية وبدل',
      'pricePerPiece': 50,
      'isQassas': 1,
      'qassasPrice': 25,
      'otherTypeName': 'بدل نسائية',
      'otherTypePrice': 75,
      'dayName': 'الأحد',
      'createdAt': DateTime.now().toIso8601String(),
    };

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختبار معمل'),
        content: ExportPrintButtons(
          data: testWorkshop,
          type: 'workshop',
          title: 'معمل الخياطة المتقدم',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _testWorks(BuildContext context) {
    final testWorks = [
      {
        'id': 1,
        'workshopName': 'معمل الثياب الرجالية',
        'pieceCount': 10,
        'dailyExpenses': 20,
        'pricePerPiece': 50,
        'totalPrice': 500,
        'createdAt': DateTime.now().toIso8601String(),
      },
      {
        'id': 2,
        'workshopName': 'معمل البدل النسائية',
        'pieceCount': 15,
        'dailyExpenses': 30,
        'pricePerPiece': 60,
        'totalPrice': 900,
        'createdAt': DateTime.now().subtract(const Duration(days: 1)).toIso8601String(),
      },
      {
        'id': 3,
        'workshopName': 'معمل الثياب التراثية',
        'pieceCount': 8,
        'dailyExpenses': 15,
        'pricePerPiece': 70,
        'totalPrice': 560,
        'createdAt': DateTime.now().subtract(const Duration(days: 2)).toIso8601String(),
      },
    ];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختبار أعمال'),
        content: ExportPrintButtons(
          data: testWorks,
          type: 'works',
          title: 'تقرير الأعمال الشامل',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _testStatistics(BuildContext context) {
    final testStats = {
      'totalEarnings': 8500,
      'totalExpenses': 2200,
      'netProfit': 6300,
      'totalWorks': 35,
      'totalWorkshops': 8,
      'totalInvoices': 18,
      'totalMeasurements': 12,
    };

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختبار إحصائيات'),
        content: ExportPrintButtons(
          data: testStats,
          type: 'statistics',
          title: 'الإحصائيات المالية الشاملة',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }
}

void main() {
  runApp(const TestFallbackPDF());
}
