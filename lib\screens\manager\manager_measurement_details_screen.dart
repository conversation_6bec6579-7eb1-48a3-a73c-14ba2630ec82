import 'package:flutter/material.dart';
import '../../measurements/measurement_styles.dart';

class ManagerMeasurementDetailsScreen extends StatelessWidget {
  final Map<String, dynamic> measurement;
  final String measurementType; // 'clothing', 'suits', 'women'

  const ManagerMeasurementDetailsScreen({
    super.key,
    required this.measurement,
    required this.measurementType,
  });

  @override
  Widget build(BuildContext context) {
    String title;
    switch (measurementType) {
      case 'clothing':
        title = 'تفاصيل مقاس الثوب';
        break;
      case 'suits':
        title = 'تفاصيل مقاس البدلة';
        break;
      case 'women':
        title = 'تفاصيل المقاس النسائي';
        break;
      default:
        title = 'تفاصيل المقاس';
    }

    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: MeasurementStyles.backgroundColor,
        appBar: AppBar(
          title: Text(
            title,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          backgroundColor: Colors.purple[700],
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.white),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // بطاقة معلومات العميل
              _buildCustomerInfoCard(),

              const SizedBox(height: 16),

              // بطاقة المعلومات المالية
              _buildFinancialInfoCard(),

              const SizedBox(height: 16),

              // بطاقة تفاصيل المقاسات
              _buildMeasurementsCard(),

              const SizedBox(height: 16),

              // بطاقة معلومات إضافية
              if (measurementType == 'clothing') _buildClothingOptionsCard(),

              const SizedBox(height: 16),

              // بطاقة الملاحظات والتواريخ
              _buildNotesAndDatesCard(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCustomerInfoCard() {
    return MeasurementStyles.modernCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.person, color: Colors.purple[600], size: 24),
              const SizedBox(width: 12),
              const Text(
                'معلومات العميل',
                style: MeasurementStyles.sectionTitleStyle,
              ),
            ],
          ),
          const SizedBox(height: 16),

          _buildDetailRow('رقم الفاتورة', measurement['billNumber'] ?? 'غير محدد'),
          _buildDetailRow('اسم العميل', measurement['customerName'] ?? '-'),
          if (measurement['phoneNumber'] != null && measurement['phoneNumber'].toString().isNotEmpty)
            _buildDetailRow('رقم الهاتف', measurement['phoneNumber']),
          if (measurement['fabricType'] != null && measurement['fabricType'].toString().isNotEmpty)
            _buildDetailRow('نوع القماش', measurement['fabricType']),
          if (measurement['quantity'] != null)
            _buildDetailRow('الكمية', measurement['quantity'].toString()),
        ],
      ),
    );
  }

  Widget _buildFinancialInfoCard() {
    final price = measurement['price'] ?? 0;
    final paid = measurement['paid'] ?? 0;
    final remaining = measurement['remaining'] ?? 0;

    return MeasurementStyles.modernCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.monetization_on, color: Colors.purple[600], size: 24),
              const SizedBox(width: 12),
              const Text(
                'المعلومات المالية',
                style: MeasurementStyles.sectionTitleStyle,
              ),
            ],
          ),
          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: _buildPriceCard('المبلغ الإجمالي', price, Colors.blue),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildPriceCard('المبلغ المدفوع', paid, Colors.green),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildPriceCard('المبلغ المتبقي', remaining, Colors.orange),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // حالة الدفع
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: remaining > 0 ? Colors.orange[50] : Colors.green[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: remaining > 0 ? Colors.orange[200]! : Colors.green[200]!,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  remaining > 0 ? Icons.warning : Icons.check_circle,
                  color: remaining > 0 ? Colors.orange[700] : Colors.green[700],
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    remaining > 0 ? 'يوجد مبلغ متبقي' : 'تم سداد المبلغ بالكامل',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: remaining > 0 ? Colors.orange[700] : Colors.green[700],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPriceCard(String label, dynamic value, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            '${value ?? 0} ريال',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildMeasurementsCard() {
    return MeasurementStyles.modernCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.straighten, color: Colors.purple[600], size: 24),
              const SizedBox(width: 12),
              const Text(
                'تفاصيل المقاسات',
                style: MeasurementStyles.sectionTitleStyle,
              ),
            ],
          ),
          const SizedBox(height: 16),

          if (measurementType == 'clothing') _buildClothingMeasurements(),
          if (measurementType == 'suits') _buildSuitsMeasurements(),
          if (measurementType == 'women') _buildWomenMeasurements(),
        ],
      ),
    );
  }

  Widget _buildClothingMeasurements() {
    return Column(
      children: [
        _buildMeasurementRow('الطول', measurement['height']),
        _buildMeasurementRow('الكتف', measurement['shoulder']),
        _buildMeasurementRow('طول الكم', measurement['sleeveLength']),
        _buildMeasurementRow('الصدر', measurement['chest']),
        _buildMeasurementRow('الرقبة', measurement['neck']),
        _buildMeasurementRow('نزول اليد', measurement['handDrop']),
        _buildMeasurementRow('وسط اليد', measurement['middleHand']),
        _buildMeasurementRow('طول الكف', measurement['cuffLength']),
        _buildMeasurementRow('ارتفاع الكف', measurement['cuffHeight']),
        _buildMeasurementRow('الخطوة', measurement['step']),
      ],
    );
  }

  Widget _buildSuitsMeasurements() {
    return Column(
      children: [
        // قياسات القميص
        _buildSectionTitle('قياسات القميص'),
        _buildMeasurementRow('طول القميص', measurement['shirtHeight']),
        _buildMeasurementRow('كتف القميص', measurement['shirtShoulder']),
        _buildMeasurementRow('كم القميص', measurement['shirtSleeveLength']),
        _buildMeasurementRow('صدر القميص', measurement['shirtChest']),
        _buildMeasurementRow('بطن القميص', measurement['shirtStomach']),
        _buildMeasurementRow('رقبة القميص', measurement['shirtNeck']),
        _buildMeasurementRow('نزول يد القميص', measurement['shirtHandDrop']),
        _buildMeasurementRow('طول كف القميص', measurement['shirtCuffLength']),
        _buildMeasurementRow('عرض كف القميص', measurement['shirtCuffWidth']),

        const SizedBox(height: 16),

        // قياسات البنطلون
        _buildSectionTitle('قياسات البنطلون'),
        _buildMeasurementRow('طول البنطلون', measurement['pantsHeight']),
        _buildMeasurementRow('حزام البنطلون', measurement['pantsBelt']),
        _buildMeasurementRow('ورك البنطلون', measurement['pantsHip']),
        _buildMeasurementRow('فخذ البنطلون', measurement['pantsThigh']),
        _buildMeasurementRow('ركبة البنطلون', measurement['pantsKnee']),
        _buildMeasurementRow('فتحة البنطلون', measurement['pantsOpening']),

        const SizedBox(height: 16),

        // قياسات الكوت
        _buildSectionTitle('قياسات الكوت'),
        _buildMeasurementRow('طول الكوت', measurement['coatHeight']),
        _buildMeasurementRow('كتف الكوت', measurement['coatShoulder']),
        _buildMeasurementRow('طول يد الكوت', measurement['coatHandLength']),
        _buildMeasurementRow('صدر الكوت', measurement['coatChest']),
        _buildMeasurementRow('بطن الكوت', measurement['coatStomach']),
        _buildMeasurementRow('عرض يد الكوت', measurement['coatHandWidth']),
        _buildMeasurementRow('وسط يد الكوت', measurement['coatMiddleHand']),
        _buildMeasurementRow('فتحة يد الكوت', measurement['coatHandOpening']),
      ],
    );
  }

  Widget _buildWomenMeasurements() {
    return Column(
      children: [
        _buildMeasurementRow('الطول', measurement['height']),
        _buildMeasurementRow('الكتف', measurement['shoulder']),
        _buildMeasurementRow('الكم', measurement['sleeve']),
        _buildMeasurementRow('الصدر', measurement['chest']),
        _buildMeasurementRow('الوسط', measurement['waist']),
        _buildMeasurementRow('الورك', measurement['hip']),
        _buildMeasurementRow('طول الثوب', measurement['dressLength']),
        _buildMeasurementRow('فتحة الرقبة', measurement['neckOpening']),
        _buildMeasurementRow('عرض الكم', measurement['sleeveWidth']),
        _buildMeasurementRow('طول الكم من الكتف', measurement['sleeveLengthFromShoulder']),
        _buildMeasurementRow('محيط الذراع', measurement['armCircumference']),
        _buildMeasurementRow('الساق فتحه الرجل', measurement['legOpening']),
      ],
    );
  }

  Widget _buildClothingOptionsCard() {
    final hasOptions = measurement['neckType'] != null ||
                      measurement['zipperType'] != null ||
                      measurement['handType'] != null ||
                      measurement['pocketType'] != null ||
                      measurement['styleType'] != null ||
                      measurement['buttonType'] != null ||
                      measurement['cuffType'] != null;

    if (!hasOptions) return const SizedBox.shrink();

    return MeasurementStyles.modernCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.settings, color: Colors.purple[600], size: 24),
              const SizedBox(width: 12),
              const Text(
                'خيارات التفصيل',
                style: MeasurementStyles.sectionTitleStyle,
              ),
            ],
          ),
          const SizedBox(height: 16),

          if (measurement['neckType'] != null)
            _buildDetailRow('نوع الرقبة', measurement['neckType']),
          if (measurement['zipperType'] != null)
            _buildDetailRow('نوع السحاب', measurement['zipperType']),
          if (measurement['handType'] != null)
            _buildDetailRow('نوع اليد', measurement['handType']),
          if (measurement['pocketType'] != null)
            _buildDetailRow('نوع الجيوب', measurement['pocketType']),
          if (measurement['styleType'] != null)
            _buildDetailRow('نوع الطراز', measurement['styleType']),
          if (measurement['buttonType'] != null)
            _buildDetailRow('نوع الأزرار', measurement['buttonType']),
          if (measurement['cuffType'] != null)
            _buildDetailRow('نوع الكبك', measurement['cuffType']),
        ],
      ),
    );
  }

  Widget _buildNotesAndDatesCard() {
    return MeasurementStyles.modernCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.event_note, color: Colors.purple[600], size: 24),
              const SizedBox(width: 12),
              const Text(
                'التواريخ والملاحظات',
                style: MeasurementStyles.sectionTitleStyle,
              ),
            ],
          ),
          const SizedBox(height: 16),

          if (measurement['receivedDate'] != null && measurement['receivedDate'].toString().isNotEmpty)
            _buildDetailRow('تاريخ الاستلام', measurement['receivedDate']),
          if (measurement['deliveryDate'] != null && measurement['deliveryDate'].toString().isNotEmpty)
            _buildDetailRow('موعد التسليم', measurement['deliveryDate']),

          const SizedBox(height: 12),

          _buildSectionTitle('الملاحظات'),
          const SizedBox(height: 8),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey[200]!),
            ),
            child: Text(
              measurement['notes']?.toString().isNotEmpty == true
                  ? measurement['notes']
                  : 'لا توجد ملاحظات',
              style: TextStyle(
                fontSize: 16,
                color: measurement['notes']?.toString().isNotEmpty == true
                    ? Colors.grey[800]
                    : Colors.grey[500],
                fontStyle: measurement['notes']?.toString().isNotEmpty == true
                    ? FontStyle.normal
                    : FontStyle.italic,
              ),
            ),
          ),

          const SizedBox(height: 16),

          // تاريخ الإنشاء
          if (measurement['createdAt'] != null)
            _buildCreatedAtInfo(),
        ],
      ),
    );
  }

  Widget _buildCreatedAtInfo() {
    final createdAt = DateTime.tryParse(measurement['createdAt'] ?? '');
    if (createdAt == null) return const SizedBox.shrink();

    final formattedDate = '${createdAt.day}/${createdAt.month}/${createdAt.year}';
    final formattedTime = '${createdAt.hour.toString().padLeft(2, '0')}:${createdAt.minute.toString().padLeft(2, '0')}';

    return Column(
      children: [
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.blue[50],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.blue[200]!),
          ),
          child: Row(
            children: [
              Icon(Icons.access_time, color: Colors.blue[600], size: 20),
              const SizedBox(width: 8),
              Text(
                'تم الإنشاء في: $formattedDate الساعة $formattedTime',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.blue[700],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
        if (measurement['dayName'] != null) ...[
          const SizedBox(height: 8),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.green[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.green[200]!),
            ),
            child: Row(
              children: [
                Icon(Icons.today, color: Colors.green[600], size: 20),
                const SizedBox(width: 8),
                Text(
                  'يوم الإضافة: ${measurement['dayName']}',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.green[700],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildDetailRow(String label, String? value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value?.isNotEmpty == true ? value! : '-',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMeasurementRow(String label, dynamic value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[700],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Text(
                value?.toString().isNotEmpty == true ? value.toString() : '-',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: Colors.purple[700],
        ),
      ),
    );
  }
}
