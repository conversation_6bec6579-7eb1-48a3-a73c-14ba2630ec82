// ملف اختبار للخدمة العربية المحسنة
import 'package:flutter/material.dart';
import 'widgets/export_print_buttons.dart';

class TestArabicPDF extends StatelessWidget {
  const TestArabicPDF({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'اختبار PDF العربي المحسن',
      theme: ThemeData(
        primarySwatch: Colors.green,
        fontFamily: 'Cairo',
      ),
      home: const TestArabicPDFScreen(),
    );
  }
}

class TestArabicPDFScreen extends StatelessWidget {
  const TestArabicPDFScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار PDF العربي'),
        backgroundColor: Colors.green[600],
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Icon(
              Icons.language,
              size: 80,
              color: Colors.green,
            ),
            const SizedBox(height: 20),
            const Text(
              'اختبار دعم النصوص العربية في PDF',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.green,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '🔧 التحسينات المطبقة:',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    '• خدمة PDF عربية متخصصة',
                    style: TextStyle(fontSize: 14, color: Colors.blue),
                  ),
                  Text(
                    '• دعم خطوط Cairo و Amiri',
                    style: TextStyle(fontSize: 14, color: Colors.blue),
                  ),
                  Text(
                    '• تخطيط من اليمين لليسار (RTL)',
                    style: TextStyle(fontSize: 14, color: Colors.blue),
                  ),
                  Text(
                    '• معالجة محسنة للنصوص العربية',
                    style: TextStyle(fontSize: 14, color: Colors.blue),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 30),
            
            // اختبار معمل عربي
            _buildTestCard(
              'اختبار معمل عربي',
              'تصدير معمل بنصوص عربية',
              Icons.business,
              Colors.indigo,
              () => _testArabicWorkshop(context),
            ),
            
            const SizedBox(height: 16),
            
            // اختبار أعمال عربية
            _buildTestCard(
              'اختبار أعمال عربية',
              'تصدير أعمال بنصوص عربية',
              Icons.work,
              Colors.green,
              () => _testArabicWorks(context),
            ),
            
            const SizedBox(height: 16),
            
            // اختبار إحصائيات عربية
            _buildTestCard(
              'اختبار إحصائيات عربية',
              'تصدير إحصائيات بنصوص عربية',
              Icons.analytics,
              Colors.orange,
              () => _testArabicStatistics(context),
            ),
            
            const SizedBox(height: 30),
            
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.green[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.green[200]!),
              ),
              child: const Column(
                children: [
                  Icon(Icons.check_circle, color: Colors.green, size: 32),
                  SizedBox(height: 8),
                  Text(
                    'النصوص العربية مدعومة بالكامل',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    'لا مزيد من المربعات السوداء أو النصوص المشوهة',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.green,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 20),
            
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.amber[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.amber[200]!),
              ),
              child: const Column(
                children: [
                  Icon(Icons.info, color: Colors.amber, size: 32),
                  SizedBox(height: 8),
                  Text(
                    'ملاحظة مهمة',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.amber,
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    'إذا لم تظهر النصوص بشكل صحيح، تأكد من إضافة ملفات الخطوط العربية في مجلد assets/fonts/',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.amber,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTestCard(String title, String description, IconData icon, Color color, VoidCallback onTap) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: color.withOpacity(0.3)),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(icon, color: color, size: 24),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              Icon(Icons.arrow_forward_ios, color: color, size: 16),
            ],
          ),
        ),
      ),
    );
  }

  void _testArabicWorkshop(BuildContext context) {
    final testWorkshop = {
      'id': 1,
      'name': 'معمل الخياطة العربي',
      'ownerPhone': '0501234567',
      'workType': 'ثياب رجالية',
      'pricePerPiece': 50,
      'isQassas': 1,
      'qassasPrice': 25,
      'otherTypeName': 'بدل رسمية',
      'otherTypePrice': 75,
      'dayName': 'الأحد',
      'createdAt': DateTime.now().toIso8601String(),
    };

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختبار معمل عربي'),
        content: ExportPrintButtons(
          data: testWorkshop,
          type: 'workshop',
          title: 'معمل الخياطة العربي',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _testArabicWorks(BuildContext context) {
    final testWorks = [
      {
        'id': 1,
        'workshopName': 'معمل الثياب الرجالية',
        'pieceCount': 10,
        'dailyExpenses': 20,
        'pricePerPiece': 50,
        'totalPrice': 500,
        'createdAt': DateTime.now().toIso8601String(),
      },
      {
        'id': 2,
        'workshopName': 'معمل البدل النسائية',
        'pieceCount': 15,
        'dailyExpenses': 30,
        'pricePerPiece': 60,
        'totalPrice': 900,
        'createdAt': DateTime.now().subtract(const Duration(days: 1)).toIso8601String(),
      },
    ];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختبار أعمال عربية'),
        content: ExportPrintButtons(
          data: testWorks,
          type: 'works',
          title: 'تقرير الأعمال العربي',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _testArabicStatistics(BuildContext context) {
    final testStats = {
      'totalEarnings': 5000,
      'totalExpenses': 1500,
      'netProfit': 3500,
      'totalWorks': 25,
      'totalWorkshops': 5,
      'totalInvoices': 12,
      'totalMeasurements': 8,
    };

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختبار إحصائيات عربية'),
        content: ExportPrintButtons(
          data: testStats,
          type: 'statistics',
          title: 'الإحصائيات المالية العربية',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }
}

void main() {
  runApp(const TestArabicPDF());
}
