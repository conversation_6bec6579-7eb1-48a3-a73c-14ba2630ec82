// صفحة التصفية الشهرية المحسنة مع شرح مفصل
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../models/db_helper.dart';
import '../../models/financial_models.dart';
import '../../measurements/measurement_styles.dart';
import '../../services/financial_state_manager.dart';
import 'widgets/financial_status_card.dart';
import 'create_settlement_wizard.dart';

class EnhancedMonthlySettlementScreen extends StatefulWidget {
  final int tailorId;
  final String tailorName;

  const EnhancedMonthlySettlementScreen({
    super.key,
    required this.tailorId,
    required this.tailorName,
  });

  @override
  State<EnhancedMonthlySettlementScreen> createState() => _EnhancedMonthlySettlementScreenState();
}

class _EnhancedMonthlySettlementScreenState extends State<EnhancedMonthlySettlementScreen>
    with TickerProviderStateMixin {
  List<MonthlySettlement> _settlements = [];
  bool _isLoading = false;
  bool _showExplanation = true;

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  final FinancialStateManager _financialManager = FinancialStateManager();

  @override
  void initState() {
    super.initState();
    _loadSettlements();
    _financialManager.setCurrentWorkshop(widget.tailorId);

    // إعداد الأنيميشن
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadSettlements() async {
    setState(() => _isLoading = true);

    try {
      final settlementsData = await DBHelper.getMonthlySettlementsByTailorId(widget.tailorId);
      _settlements = settlementsData.map((data) => MonthlySettlement.fromMap(data)).toList();

      // ترتيب التصفيات حسب التاريخ (الأحدث أولاً)
      _settlements.sort((a, b) => b.settlementDate.compareTo(a.settlementDate));
    } catch (e) {
      _showErrorSnackBar('خطأ في تحميل البيانات: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _createNewSettlement() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CreateSettlementWizard(
          tailorId: widget.tailorId,
          tailorName: widget.tailorName,
        ),
      ),
    );

    if (result == true) {
      _loadSettlements();
      await _financialManager.refresh();

      // إظهار رسالة نجاح
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Row(
              children: [
                Icon(Icons.check_circle, color: Colors.white),
                SizedBox(width: 12),
                Text('تم إنشاء التصفية بنجاح وترحيل الرصيد'),
              ],
            ),
            backgroundColor: Colors.green[600],
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: MeasurementStyles.errorColor,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: Colors.grey[50],
        body: FadeTransition(
          opacity: _fadeAnimation,
          child: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                  child: Column(
                    children: [
                      // هيدر محسن
                      _buildEnhancedHeader(),

                      // المحتوى الرئيسي
                      Column(
                        children: [
                          // كارد الحالة المالية
                          FinancialStatusCard(
                            workshopId: widget.tailorId,
                            onTap: () {
                              // يمكن إضافة الانتقال للإحصائيات
                            },
                          ),

                          // شرح مفصل للتصفية الشهرية
                          if (_showExplanation) _buildExplanationCard(),

                          // قائمة التصفيات
                          _settlements.isEmpty
                              ? _buildEmptyState()
                              : _buildSettlementsList(),
                        ],
                      ),
                    ],
                  ),
                ),
        ),

        // زر إضافة تصفية جديدة
        floatingActionButton: FloatingActionButton.extended(
          onPressed: _createNewSettlement,
          backgroundColor: Colors.purple[600],
          foregroundColor: Colors.white,
          icon: const Icon(Icons.add_business),
          label: const Text('إضافة تصفية جديدة'),
          heroTag: 'settlement_fab',
        ),
      ),
    );
  }

  Widget _buildEnhancedHeader() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.purple[700]!, Colors.purple[500]!],
          begin: Alignment.topRight,
          end: Alignment.bottomLeft,
        ),
        borderRadius: const BorderRadius.vertical(bottom: Radius.circular(30)),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              Row(
                children: [
                  GestureDetector(
                    onTap: () => Navigator.pop(context),
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(Icons.arrow_back, color: Colors.white),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Text(
                      'التصفية الشهرية',
                      style: GoogleFonts.cairo(
                        color: Colors.white,
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () {
                      setState(() {
                        _showExplanation = !_showExplanation;
                      });
                    },
                    icon: Icon(
                      _showExplanation ? Icons.visibility_off : Icons.help_outline,
                      color: Colors.white,
                    ),
                    tooltip: _showExplanation ? 'إخفاء الشرح' : 'إظهار الشرح',
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Text(
                'خياط: ${widget.tailorName}',
                style: GoogleFonts.cairo(
                  color: Colors.white.withOpacity(0.9),
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'إدارة التصفيات الشهرية وترحيل الأرصدة',
                style: GoogleFonts.cairo(
                  color: Colors.white.withOpacity(0.8),
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildExplanationCard() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.blue[50]!, Colors.blue[100]!],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withOpacity(0.2),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue[600],
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.school,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  'ما هي التصفية الشهرية؟',
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue[800],
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          _buildExplanationStep(
            '1',
            'حساب الأرباح',
            'يتم جمع جميع الأرباح من الأعمال والقطع المنتجة خلال الشهر',
            Icons.trending_up,
            Colors.green,
          ),

          const SizedBox(height: 12),

          _buildExplanationStep(
            '2',
            'حساب المصروفات',
            'يتم جمع جميع المصروفات والغرامات والأعمال الإضافية',
            Icons.trending_down,
            Colors.red,
          ),

          const SizedBox(height: 12),

          _buildExplanationStep(
            '3',
            'حساب الرصيد النهائي',
            'الرصيد النهائي = الأرباح - المصروفات + الرصيد المرحل',
            Icons.calculate,
            Colors.purple,
          ),

          const SizedBox(height: 12),

          _buildExplanationStep(
            '4',
            'ترحيل الرصيد',
            'يتم ترحيل الرصيد النهائي ليصبح رصيد مرحل للشهر التالي',
            Icons.forward,
            Colors.orange,
          ),

          const SizedBox(height: 20),

          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.amber[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.amber[200]!),
            ),
            child: Row(
              children: [
                Icon(Icons.lightbulb, color: Colors.amber[700], size: 24),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'نصيحة: قم بعمل تصفية شهرية في نهاية كل شهر لمتابعة وضعك المالي بدقة',
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color: Colors.amber[800],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExplanationStep(String number, String title, String description, IconData icon, Color color) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
          child: Center(
            child: Text(
              number,
              style: GoogleFonts.cairo(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(icon, color: color, size: 20),
                  const SizedBox(width: 8),
                  Text(
                    title,
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey[800],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(40),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.purple[50],
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.receipt_long,
              size: 64,
              color: Colors.purple[300],
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'لا توجد تصفيات شهرية',
            style: GoogleFonts.cairo(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.grey[700],
            ),
          ),
          const SizedBox(height: 12),
          Text(
            'ابدأ بإنشاء أول تصفية شهرية لتتبع وضعك المالي',
            style: GoogleFonts.cairo(
              fontSize: 16,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _createNewSettlement,
            icon: const Icon(Icons.add),
            label: const Text('إنشاء تصفية جديدة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.purple[600],
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettlementsList() {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.all(16),
      itemCount: _settlements.length,
      itemBuilder: (context, index) {
        final settlement = _settlements[index];
        return _buildSettlementCard(settlement, index);
      },
    );
  }

  Widget _buildSettlementCard(MonthlySettlement settlement, int index) {
    final isPositive = settlement.finalBalance >= 0;
    final monthNames = [
      'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];

    final date = settlement.settlementDateTime;
    final monthName = monthNames[date.month - 1];
    final year = date.year;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: isPositive ? Colors.green[200]! : Colors.red[200]!,
          width: 2,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () => _viewSettlementDetails(settlement),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // رأس الكارد
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: isPositive ? Colors.green[100] : Colors.red[100],
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        isPositive ? Icons.trending_up : Icons.trending_down,
                        color: isPositive ? Colors.green[700] : Colors.red[700],
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'تصفية $monthName $year',
                            style: GoogleFonts.cairo(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.grey[800],
                            ),
                          ),
                          Text(
                            'تاريخ التصفية: ${date.day}/$monthName/$year',
                            style: GoogleFonts.cairo(
                              fontSize: 14,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: isPositive ? Colors.green[50] : Colors.red[50],
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Text(
                        isPositive ? 'ربح' : 'خسارة',
                        style: GoogleFonts.cairo(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: isPositive ? Colors.green[700] : Colors.red[700],
                        ),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 20),

                // تفاصيل مالية
                Row(
                  children: [
                    Expanded(
                      child: _buildFinancialInfo(
                        'الأرباح',
                        settlement.totalIncome,
                        Icons.add_circle,
                        Colors.green,
                      ),
                    ),
                    Expanded(
                      child: _buildFinancialInfo(
                        'المصروفات',
                        settlement.totalExpenses,
                        Icons.remove_circle,
                        Colors.red,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // الرصيد النهائي
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: isPositive
                        ? [Colors.green[50]!, Colors.green[100]!]
                        : [Colors.red[50]!, Colors.red[100]!],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'الرصيد النهائي:',
                        style: GoogleFonts.cairo(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.grey[700],
                        ),
                      ),
                      Text(
                        '${isPositive ? '+' : '-'}${settlement.finalBalance.abs().toStringAsFixed(0)} ريال',
                        style: GoogleFonts.cairo(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: isPositive ? Colors.green[700] : Colors.red[700],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFinancialInfo(String label, double amount, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 8),
        Text(
          label,
          style: GoogleFonts.cairo(
            fontSize: 14,
            color: Colors.grey[600],
          ),
        ),
        const SizedBox(height: 4),
        Text(
          '${amount.toStringAsFixed(0)} ريال',
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  void _viewSettlementDetails(MonthlySettlement settlement) {
    // يمكن إضافة صفحة تفاصيل التصفية هنا
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تفاصيل التصفية'),
        content: const Text('سيتم إضافة صفحة تفاصيل التصفية قريباً'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }
}

// صفحة إنشاء تصفية جديدة محسنة (ستحتاج لإنشائها)
class EnhancedCreateSettlementScreen extends StatelessWidget {
  final int tailorId;
  final String tailorName;

  const EnhancedCreateSettlementScreen({
    super.key,
    required this.tailorId,
    required this.tailorName,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إنشاء تصفية جديدة'),
      ),
      body: const Center(
        child: Text('صفحة إنشاء التصفية - قيد التطوير'),
      ),
    );
  }
}
