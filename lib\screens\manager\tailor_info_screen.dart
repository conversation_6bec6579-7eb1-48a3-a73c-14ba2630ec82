import 'package:flutter/material.dart';

class TailorInfoScreen extends StatelessWidget {
  final Map<String, dynamic> tailor;

  const TailorInfoScreen({super.key, required this.tailor});

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('معلومات الخياط'),
          backgroundColor: Colors.purple.shade700,
          foregroundColor: Colors.white,
        ),
        body: const Center(
          child: Text(
            'صفحة معلومات الخياط\nسيتم إضافتها لاحقاً',
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: 18),
          ),
        ),
      ),
    );
  }
}
