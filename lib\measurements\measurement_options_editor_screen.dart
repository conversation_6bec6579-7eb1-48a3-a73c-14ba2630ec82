// import 'package:flutter/material.dart';
// import 'package:sqflite/sqflite.dart';
// import 'package:path/path.dart';

// class MeasurementOptionsEditorScreen extends StatefulWidget {
//   const MeasurementOptionsEditorScreen({super.key});

//   @override
//   State<MeasurementOptionsEditorScreen> createState() => _MeasurementOptionsEditorScreenState();
// }

// class _MeasurementOptionsEditorScreenState extends State<MeasurementOptionsEditorScreen> {
//   final Map<String, List<String>> options = {
//     'نوع القماش': [],
//     'الرقبة': [],
//     'الجبزور': [],
//     'اليد': [],
//     'الجيوب': [],
//     'نوع التفصيل': [],
//     'الزرارات': [],
//   };

//   late Database db;

//   @override
//   void initState() {
//     super.initState();
//     _initDatabase();
//   }

//   Future<void> _initDatabase() async {
//     db = await openDatabase(
//       join(await getDatabasesPath(), 'measurement_options.db'),
//       onCreate: (db, version) {
//         return db.execute('''
//           CREATE TABLE options (
//             id INTEGER PRIMARY KEY AUTOINCREMENT,
//             category TEXT,
//             value TEXT
//           );
//         ''');
//       },
//       version: 1,
//     );

//     await _loadOptions();
//   }

//   Future<void> _loadOptions() async {
//     final all = await db.query('options');
//     final Map<String, List<String>> loaded = {
//       for (var key in options.keys) key: [],
//     };
//     for (var row in all) {
//       loaded[row['category'] as String]?.add(row['value'] as String);
//     }
//     setState(() {
//       options.clear();
//       options.addAll(loaded);
//     });
//   }

//   Future<void> _addOption(String category, String value) async {
//     if (value.trim().isEmpty) return;
//     await db.insert('options', {'category': category, 'value': value});
//     await _loadOptions();
//   }

//   Future<void> _deleteOption(String category, String value) async {
//     await db.delete(
//       'options',
//       where: 'category = ? AND value = ?',
//       whereArgs: [category, value],
//     );
//     await _loadOptions();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(title: const Text("إدارة خيارات المقاسات")),
//       body: ListView(
//         padding: const EdgeInsets.all(16),
//         children: options.entries.map((entry) {
//           final category = entry.key;
//           final values = entry.value;
//           final controller = TextEditingController();

//           return Card(
//             margin: const EdgeInsets.only(bottom: 16),
//             child: Padding(
//               padding: const EdgeInsets.all(12),
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   Text(category, style: const TextStyle(fontWeight: FontWeight.bold)),
//                   const SizedBox(height: 10),
//                   Wrap(
//                     spacing: 8,
//                     runSpacing: 4,
//                     children: values.map((v) {
//                       return Chip(
//                         label: Text(v),
//                         onDeleted: () => _deleteOption(category, v),
//                       );
//                     }).toList(),
//                   ),
//                   Row(
//                     children: [
//                       Expanded(child: TextField(controller: controller, decoration: const InputDecoration(hintText: "إضافة جديد"))),
//                       IconButton(
//                         icon: const Icon(Icons.add),
//                         onPressed: () => _addOption(category, controller.text),
//                       ),
//                     ],
//                   )
//                 ],
//               ),
//             ),
//           );
//         }).toList(),
//       ),
//     );
//   }
// }
