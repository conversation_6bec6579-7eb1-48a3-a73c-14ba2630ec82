import 'package:flutter/material.dart';
import '../../models/db_helper.dart';
import '../../utils/date_utils.dart';

class AddManagerInvoiceScreen extends StatefulWidget {
  final Map<String, dynamic>? invoiceToEdit;

  const AddManagerInvoiceScreen({
    super.key,
    this.invoiceToEdit,
  });

  @override
  State<AddManagerInvoiceScreen> createState() => _AddManagerInvoiceScreenState();
}

class _AddManagerInvoiceScreenState extends State<AddManagerInvoiceScreen> {
  final _formKey = GlobalKey<FormState>();

  // Controllers للحقول
  final _shopNameController = TextEditingController();
  final _invoiceNumberController = TextEditingController();
  final _customerNameController = TextEditingController();
  final _clothesCountController = TextEditingController();

  bool _isLoading = false;
  bool _isEdit = false;
  bool _isReady = false;

  @override
  void initState() {
    super.initState();
    if (widget.invoiceToEdit != null) {
      _isEdit = true;
      _loadInvoiceData();
    }
  }

  void _loadInvoiceData() {
    final invoice = widget.invoiceToEdit!;
    _shopNameController.text = invoice['shopName'] ?? '';
    _invoiceNumberController.text = invoice['invoiceNumber'] ?? '';
    _customerNameController.text = invoice['customerName'] ?? '';
    _clothesCountController.text = (invoice['clothesCount'] ?? 0).toString();
    _isReady = (invoice['isReady'] ?? 0) == 1;
  }

  @override
  void dispose() {
    _shopNameController.dispose();
    _invoiceNumberController.dispose();
    _customerNameController.dispose();
    _clothesCountController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: Colors.grey[50],
        appBar: AppBar(
          title: Text(
            _isEdit ? 'تعديل الفاتورة' : 'إضافة فاتورة جديدة',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          backgroundColor: Colors.purple[700],
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.white),
            onPressed: () {
              Navigator.of(context).pop();
            },
          ),
        ),
        body: Form(
          key: _formKey,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // بطاقة معلومات الفاتورة
                _buildInvoiceInfoCard(),

                const SizedBox(height: 16),

                // بطاقة معلومات الزبون
                _buildCustomerInfoCard(),

                const SizedBox(height: 16),

                // بطاقة حالة الفاتورة
                if (_isEdit) _buildStatusCard(),

                const SizedBox(height: 24),

                // زر الحفظ
                _buildSaveButton(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInvoiceInfoCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.receipt, color: Colors.purple[600], size: 24),
                const SizedBox(width: 12),
                Text(
                  'معلومات الفاتورة',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[800],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // اسم المحل
            _buildTextField(
              controller: _shopNameController,
              label: 'اسم المحل',
              hint: 'مثال: محل الأناقة للخياطة',
              icon: Icons.store,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال اسم المحل';
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            // رقم الفاتورة
            _buildTextField(
              controller: _invoiceNumberController,
              label: 'رقم الفاتورة',
              hint: 'مثال: INV-001',
              icon: Icons.numbers,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال رقم الفاتورة';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomerInfoCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.person, color: Colors.purple[600], size: 24),
                const SizedBox(width: 12),
                Text(
                  'معلومات الزبون',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[800],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // اسم الزبون
            _buildTextField(
              controller: _customerNameController,
              label: 'اسم الزبون',
              hint: 'مثال: أحمد محمد',
              icon: Icons.person_outline,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال اسم الزبون';
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            // عدد الثياب
            _buildTextField(
              controller: _clothesCountController,
              label: 'عدد الثياب',
              hint: '1',
              icon: Icons.checkroom,
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال عدد الثياب';
                }
                final count = int.tryParse(value.trim());
                if (count == null || count <= 0) {
                  return 'يرجى إدخال عدد صحيح';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info, color: Colors.purple[600], size: 24),
                const SizedBox(width: 12),
                Text(
                  'حالة الفاتورة',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[800],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            Row(
              children: [
                Checkbox(
                  value: _isReady,
                  onChanged: (value) {
                    setState(() {
                      _isReady = value ?? false;
                    });
                  },
                  activeColor: Colors.green[600],
                ),
                const SizedBox(width: 8),
                const Expanded(
                  child: Text(
                    'الفاتورة جاهزة',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    TextInputType keyboardType = TextInputType.text,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      validator: validator,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixIcon: Icon(icon, color: Colors.purple[600]),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey[300]!),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.purple[600]!, width: 2),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey[300]!),
        ),
        filled: true,
        fillColor: Colors.grey[50],
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      ),
    );
  }

  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _saveInvoice,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.purple[600],
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          elevation: 4,
        ),
        child: _isLoading
            ? const SizedBox(
                height: 24,
                width: 24,
                child: CircularProgressIndicator(
                  color: Colors.white,
                  strokeWidth: 2,
                ),
              )
            : Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(_isEdit ? Icons.update : Icons.save, size: 24),
                  const SizedBox(width: 12),
                  Text(
                    _isEdit ? 'تحديث الفاتورة' : 'حفظ الفاتورة',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  Future<void> _saveInvoice() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final clothesCount = int.parse(_clothesCountController.text.trim());

      final data = {
        'shopName': _shopNameController.text.trim(),
        'invoiceNumber': _invoiceNumberController.text.trim(),
        'customerName': _customerNameController.text.trim(),
        'clothesCount': clothesCount,
        'isReady': _isReady ? 1 : 0,
        'dayName': AppDateUtils.getCurrentDayName(), // إضافة اسم اليوم
        'createdAt': DateTime.now().toIso8601String(),
      };

      if (_isEdit) {
        await DBHelper.updateManagerInvoice(widget.invoiceToEdit!['id'], data);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.white),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(AppDateUtils.getInvoiceUpdatedMessage()),
                  ),
                ],
              ),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(10)),
              ),
            ),
          );
        }
      } else {
        await DBHelper.insertManagerInvoice(data);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.white),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(AppDateUtils.getInvoiceAddedMessage()),
                  ),
                ],
              ),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(10)),
              ),
            ),
          );
        }
      }

      if (mounted) {
        Navigator.of(context).pop(true); // إرجاع true للإشارة إلى نجاح العملية
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(
                  child: Text('خطأ في ${_isEdit ? 'تحديث' : 'إضافة'} الفاتورة: $e'),
                ),
              ],
            ),
            backgroundColor: Colors.red[600],
            behavior: SnackBarBehavior.floating,
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.all(Radius.circular(10)),
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
