// ملف اختبار للتأكد من حل الأخطاء
import 'package:flutter/material.dart';
import 'services/financial_state_manager.dart';
import 'screens/financial/enhanced_initial_balance_screen.dart';
import 'screens/financial/financial_main_screen.dart';

class TestErrorsFixed extends StatelessWidget {
  const TestErrorsFixed({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'اختبار حل الأخطاء',
      theme: ThemeData(
        primarySwatch: Colors.green,
        fontFamily: 'Cairo',
      ),
      home: const TestErrorsScreen(),
    );
  }
}

class TestErrorsScreen extends StatefulWidget {
  const TestErrorsScreen({super.key});

  @override
  State<TestErrorsScreen> createState() => _TestErrorsScreenState();
}

class _TestErrorsScreenState extends State<TestErrorsScreen> {
  final FinancialStateManager _financialManager = FinancialStateManager();

  @override
  void initState() {
    super.initState();
    // اختبار تهيئة مدير الحالة المالية
    _financialManager.setCurrentWorkshop(1);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار حل الأخطاء'),
        backgroundColor: Colors.green[600],
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Icon(
              Icons.check_circle,
              size: 80,
              color: Colors.green,
            ),
            const SizedBox(height: 20),
            const Text(
              'تم حل جميع الأخطاء!',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.green,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            const Text(
              'الأخطاء التي تم حلها:',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            _buildFixedError('✅ إصلاح دالة updateInitialBalance'),
            _buildFixedError('✅ إصلاح أسماء دوال قاعدة البيانات'),
            _buildFixedError('✅ إصلاح معاملات الدوال'),
            _buildFixedError('✅ إصلاح أسماء الحقول'),
            _buildFixedError('✅ إصلاح مدير الحالة المالية'),
            const SizedBox(height: 30),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const FinancialMainScreen(
                      tailorId: 1,
                      tailorName: 'خياط تجريبي - بدون أخطاء',
                    ),
                  ),
                );
              },
              icon: const Icon(Icons.account_balance),
              label: const Text('اختبار النظام المالي'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green[600],
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const EnhancedInitialBalanceScreen(
                      tailorId: 1,
                      tailorName: 'خياط تجريبي - الرصيد المرحل',
                    ),
                  ),
                );
              },
              icon: const Icon(Icons.account_balance_wallet),
              label: const Text('اختبار الرصيد المرحل'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.cyan[600],
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFixedError(String error) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              error,
              style: const TextStyle(fontSize: 16),
            ),
          ),
        ],
      ),
    );
  }
}

void main() {
  runApp(const TestErrorsFixed());
}
