class UserProfileModel {
  final String uid;
  final String fullName;
  final String phone;
  final String profession;
  final List<String> skills;

  UserProfileModel({
    required this.uid,
    required this.fullName,
    required this.phone,
    required this.profession,
    required this.skills,
  });

  factory UserProfileModel.fromMap(Map<String, dynamic> map, String uid) {
    return UserProfileModel(
      uid: uid,
      fullName: map['fullName'] as String? ?? '',
      phone: map['phone'] as String? ?? '',
      profession: map['profession'] as String? ?? '',
      skills: List<String>.from(map['skills'] as List<dynamic>? ?? []),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'fullName': fullName,
      'phone': phone,
      'profession': profession,
      'skills': skills,
    };
  }
}
