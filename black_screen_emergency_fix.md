# إصلاح طارئ للشاشة السوداء

## 🚨 المشكلة:
الشاشة سوداء بالكامل عند تشغيل التطبيق.

## ✅ الحل المطبق:

### 1. **استبدال main.dart بنسخة مبسطة**
- ❌ **حذفت الاستيراد المشكوك فيه**: `import 'screens/role_selection_screen.dart';`
- ✅ **أنشأت TestScreen بسيط** للتأكد من عمل التطبيق
- ✅ **أنشأت RoleSelectionScreen مبسط** داخل نفس الملف

### 2. **الكود الجديد آمن 100%**
- ✅ **لا يستورد ملفات خارجية** قد تحتوي على أخطاء
- ✅ **كود Flutter أساسي** مضمون العمل
- ✅ **واجهة بسيطة وواضحة**

## 🚀 جرب الآن:

### الخطوة 1: Hot Restart
```
Shift + R
```

### الخطوة 2: إذا لم يعمل
```bash
flutter run
```

## 🎯 النتيجة المتوقعة:

### الشاشة الأولى:
```
┌─────────────────────────────┐
│                             │
│         ✅ (أيقونة)         │
│                             │
│    التطبيق يعمل بنجاح!      │
│                             │
│   [اذهب لاختيار الدور]      │
│                             │
└─────────────────────────────┘
```

### بعد الضغط على الزر:
```
┌─────────────────────────────┐
│     اختيار نوع المستخدم      │
│                             │
│    اختر نوع المستخدم        │
│                             │
│    [👤 هل أنت خياط؟]       │
│                             │
│    [👥 هل أنت مدير؟]       │
│                             │
└─────────────────────────────┘
```

## 🔍 إذا استمرت المشكلة:

### أرسل لي النص الكامل من Console:
```bash
flutter run --verbose
```

### أو جرب:
```bash
flutter clean
flutter pub get
flutter run
```

## 🎉 بعد نجاح الاختبار:

عندما يعمل التطبيق بنجاح، سنعيد ربط الصفحات الأصلية تدريجياً.

**جرب الآن وأخبرني بالنتيجة!** 🚀
