// ملف اختبار للتأكد من حل مشكلة Overflow نهائياً
import 'package:flutter/material.dart';
import 'screens/financial/enhanced_statistics_screen.dart';
import 'screens/financial/financial_main_screen.dart';

class TestFinalOverflowFix extends StatelessWidget {
  const TestFinalOverflowFix({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'اختبار حل Overflow نهائياً',
      theme: ThemeData(
        primarySwatch: Colors.green,
        fontFamily: 'Cairo',
      ),
      home: const TestFinalFixScreen(),
    );
  }
}

class TestFinalFixScreen extends StatelessWidget {
  const TestFinalFixScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار الحل النهائي'),
        backgroundColor: Colors.green[600],
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Icon(
              Icons.check_circle,
              size: 80,
              color: Colors.green,
            ),
            const SizedBox(height: 20),
            const Text(
              'تم حل Overflow نهائياً!',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.green,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            const Text(
              'التحسينات النهائية:',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            _buildFixedItem('✅ ارتفاع الهيدر: 140 بكسل'),
            _buildFixedItem('✅ الحشو: 12 بكسل'),
            _buildFixedItem('✅ حجم العنوان: 18 بكسل'),
            _buildFixedItem('✅ حجم النص: 14 بكسل'),
            _buildFixedItem('✅ حجم الوصف: 12 بكسل'),
            _buildFixedItem('✅ حجم المؤشر: 10 بكسل'),
            _buildFixedItem('✅ مسافات محسنة بين العناصر'),
            const SizedBox(height: 30),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const EnhancedStatisticsScreen(
                      workshopId: 1,
                      workshopName: 'معمل تجريبي - بدون Overflow نهائياً',
                    ),
                  ),
                );
              },
              icon: const Icon(Icons.analytics),
              label: const Text('اختبار صفحة الإحصائيات'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.teal[600],
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const FinancialMainScreen(
                      tailorId: 1,
                      tailorName: 'خياط تجريبي - النظام المحسن',
                    ),
                  ),
                );
              },
              icon: const Icon(Icons.account_balance),
              label: const Text('اختبار النظام المالي'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.indigo[600],
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
            const SizedBox(height: 20),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.green[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.green[200]!),
              ),
              child: const Column(
                children: [
                  Icon(Icons.verified, color: Colors.green, size: 32),
                  SizedBox(height: 8),
                  Text(
                    'النظام محسن بالكامل',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    'لا مزيد من مشاكل Overflow أو الأخطاء',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.green,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFixedItem(String fix) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              fix,
              style: const TextStyle(fontSize: 16),
            ),
          ),
        ],
      ),
    );
  }
}

void main() {
  runApp(const TestFinalOverflowFix());
}
