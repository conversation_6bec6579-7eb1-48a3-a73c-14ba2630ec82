import 'dart:io';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:path_provider/path_provider.dart';
import 'package:intl/intl.dart';
import '../utils/date_utils.dart';

class SimplePDFService {
  
  /// إنشاء PDF بسيط للمعامل
  static Future<File> generateWorkshopPDF(Map<String, dynamic> workshop) async {
    try {
      final pdf = pw.Document();

      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          textDirection: pw.TextDirection.rtl,
          build: (pw.Context context) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // العنوان الرئيسي
                pw.Container(
                  width: double.infinity,
                  padding: const pw.EdgeInsets.all(20),
                  decoration: pw.BoxDecoration(
                    color: PdfColor.fromHex('#E3F2FD'),
                    borderRadius: pw.BorderRadius.circular(10),
                  ),
                  child: pw.Column(
                    children: [
                      pw.Text(
                        'تفاصيل المعمل',
                        style: pw.TextStyle(
                          fontSize: 24,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                      pw.SizedBox(height: 10),
                      pw.Text(
                        'تاريخ التصدير: ${_getCurrentDate()}',
                        style: const pw.TextStyle(fontSize: 12),
                      ),
                    ],
                  ),
                ),

                pw.SizedBox(height: 20),

                // معلومات المعمل
                pw.Container(
                  padding: const pw.EdgeInsets.all(15),
                  decoration: pw.BoxDecoration(
                    border: pw.Border.all(color: PdfColor.fromHex('#E0E0E0')),
                    borderRadius: pw.BorderRadius.circular(8),
                  ),
                  child: pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      _buildSimpleInfoRow('اسم المعمل:', workshop['name'] ?? ''),
                      _buildSimpleInfoRow('رقم هاتف المالك:', workshop['ownerPhone'] ?? ''),
                      _buildSimpleInfoRow('نوع العمل:', workshop['workType'] ?? ''),
                      _buildSimpleInfoRow('السعر لكل قطعة:', '${workshop['pricePerPiece'] ?? 0} ريال'),
                      if (workshop['isQassas'] == 1) ...[
                        _buildSimpleInfoRow('يدعم القصة:', 'نعم'),
                        _buildSimpleInfoRow('سعر القصة:', '${workshop['qassasPrice'] ?? 0} ريال'),
                      ],
                      if (workshop['otherTypeName']?.isNotEmpty == true) ...[
                        _buildSimpleInfoRow('نوع عمل آخر:', workshop['otherTypeName']),
                        _buildSimpleInfoRow('سعر العمل الآخر:', '${workshop['otherTypePrice'] ?? 0} ريال'),
                      ],
                      if (workshop['dayName'] != null)
                        _buildSimpleInfoRow('يوم الإضافة:', workshop['dayName']),
                      _buildSimpleInfoRow('تاريخ الإنشاء:', _formatSimpleDate(workshop['createdAt'])),
                    ],
                  ),
                ),

                pw.Spacer(),

                // تذييل
                pw.Container(
                  width: double.infinity,
                  padding: const pw.EdgeInsets.all(10),
                  decoration: pw.BoxDecoration(
                    color: PdfColor.fromHex('#F5F5F5'),
                    borderRadius: pw.BorderRadius.circular(5),
                  ),
                  child: pw.Text(
                    'تم إنشاء هذا التقرير بواسطة تطبيق إدارة الخياطين',
                    style: const pw.TextStyle(fontSize: 10),
                    textAlign: pw.TextAlign.center,
                  ),
                ),
              ],
            );
          },
        ),
      );

      return await _saveSimplePDF(pdf, 'معمل_${workshop['name']}_${DateTime.now().millisecondsSinceEpoch}');
    } catch (e) {
      throw Exception('فشل في إنشاء ملف PDF: ${e.toString()}');
    }
  }

  /// إنشاء PDF للأعمال
  static Future<File> generateWorksPDF(List<Map<String, dynamic>> works, {String? title}) async {
    try {
      final pdf = pw.Document();

      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          textDirection: pw.TextDirection.rtl,
          build: (pw.Context context) {
            return [
              // العنوان
              pw.Container(
                width: double.infinity,
                padding: const pw.EdgeInsets.all(20),
                decoration: pw.BoxDecoration(
                  color: PdfColor.fromHex('#E8F5E8'),
                  borderRadius: pw.BorderRadius.circular(10),
                ),
                child: pw.Column(
                  children: [
                    pw.Text(
                      title ?? 'تقرير الأعمال',
                      style: pw.TextStyle(
                        fontSize: 24,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                    pw.SizedBox(height: 10),
                    pw.Text(
                      'تاريخ التصدير: ${_getCurrentDate()}',
                      style: const pw.TextStyle(fontSize: 12),
                    ),
                    pw.Text(
                      'عدد الأعمال: ${works.length}',
                      style: const pw.TextStyle(fontSize: 12),
                    ),
                  ],
                ),
              ),

              pw.SizedBox(height: 20),

              // قائمة الأعمال
              ...works.map((work) => pw.Container(
                margin: const pw.EdgeInsets.only(bottom: 15),
                padding: const pw.EdgeInsets.all(15),
                decoration: pw.BoxDecoration(
                  border: pw.Border.all(color: PdfColor.fromHex('#E0E0E0')),
                  borderRadius: pw.BorderRadius.circular(8),
                ),
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text(
                      'عمل: ${work['workshopName'] ?? ''}',
                      style: pw.TextStyle(
                        fontSize: 16,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                    pw.SizedBox(height: 10),
                    _buildSimpleInfoRow('عدد القطع:', '${work['pieceCount'] ?? 0}'),
                    _buildSimpleInfoRow('المصاريف اليومية:', '${work['dailyExpenses'] ?? 0} ريال'),
                    _buildSimpleInfoRow('السعر لكل قطعة:', '${work['pricePerPiece'] ?? 0} ريال'),
                    _buildSimpleInfoRow('الإجمالي:', '${work['totalPrice'] ?? 0} ريال'),
                    _buildSimpleInfoRow('تاريخ الإنشاء:', _formatSimpleDate(work['createdAt'])),
                  ],
                ),
              )),
            ];
          },
        ),
      );

      return await _saveSimplePDF(pdf, 'الأعمال_${DateTime.now().millisecondsSinceEpoch}');
    } catch (e) {
      throw Exception('فشل في إنشاء ملف PDF: ${e.toString()}');
    }
  }

  /// إنشاء PDF للمقاسات
  static Future<File> generateMeasurementsPDF(List<Map<String, dynamic>> measurements, String type) async {
    try {
      final pdf = pw.Document();

      String typeTitle = '';
      switch (type) {
        case 'clothing':
          typeTitle = 'مقاسات الثياب';
          break;
        case 'suits':
          typeTitle = 'مقاسات البدل';
          break;
        case 'women':
          typeTitle = 'مقاسات النساء';
          break;
        default:
          typeTitle = 'المقاسات';
      }

      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          textDirection: pw.TextDirection.rtl,
          build: (pw.Context context) {
            return [
              // العنوان
              pw.Container(
                width: double.infinity,
                padding: const pw.EdgeInsets.all(20),
                decoration: pw.BoxDecoration(
                  color: PdfColor.fromHex('#F3E5F5'),
                  borderRadius: pw.BorderRadius.circular(10),
                ),
                child: pw.Column(
                  children: [
                    pw.Text(
                      typeTitle,
                      style: pw.TextStyle(
                        fontSize: 24,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                    pw.SizedBox(height: 10),
                    pw.Text(
                      'تاريخ التصدير: ${_getCurrentDate()}',
                      style: const pw.TextStyle(fontSize: 12),
                    ),
                    pw.Text(
                      'عدد المقاسات: ${measurements.length}',
                      style: const pw.TextStyle(fontSize: 12),
                    ),
                  ],
                ),
              ),

              pw.SizedBox(height: 20),

              // قائمة المقاسات
              ...measurements.map((measurement) => pw.Container(
                margin: const pw.EdgeInsets.only(bottom: 15),
                padding: const pw.EdgeInsets.all(15),
                decoration: pw.BoxDecoration(
                  border: pw.Border.all(color: PdfColors.grey300),
                  borderRadius: pw.BorderRadius.circular(8),
                ),
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text(
                      'مقاس: ${measurement['customerName'] ?? ''}',
                      style: pw.TextStyle(
                        fontSize: 16,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                    pw.SizedBox(height: 10),
                    _buildSimpleInfoRow('رقم الهاتف:', measurement['phoneNumber'] ?? ''),
                    _buildSimpleInfoRow('رقم الفاتورة:', measurement['billNumber'] ?? ''),
                    _buildSimpleInfoRow('نوع القماش:', measurement['fabricType'] ?? ''),
                    _buildSimpleInfoRow('الكمية:', '${measurement['quantity'] ?? 0}'),
                    _buildSimpleInfoRow('المبلغ:', '${measurement['price'] ?? 0} ريال'),
                    _buildSimpleInfoRow('المدفوع:', '${measurement['paid'] ?? 0} ريال'),
                    _buildSimpleInfoRow('المتبقي:', '${measurement['remaining'] ?? 0} ريال'),
                    if (measurement['dayName'] != null)
                      _buildSimpleInfoRow('يوم الإضافة:', measurement['dayName']),
                    _buildSimpleInfoRow('تاريخ الإنشاء:', _formatSimpleDate(measurement['createdAt'])),
                  ],
                ),
              )),
            ];
          },
        ),
      );

      return await _saveSimplePDF(pdf, '${typeTitle}_${DateTime.now().millisecondsSinceEpoch}');
    } catch (e) {
      throw Exception('فشل في إنشاء ملف PDF: ${e.toString()}');
    }
  }

  /// إنشاء PDF للفواتير
  static Future<File> generateInvoicesPDF(List<Map<String, dynamic>> invoices) async {
    try {
      final pdf = pw.Document();

      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          textDirection: pw.TextDirection.rtl,
          build: (pw.Context context) {
            return [
              // العنوان
              pw.Container(
                width: double.infinity,
                padding: const pw.EdgeInsets.all(20),
                decoration: pw.BoxDecoration(
                  color: PdfColor.fromHex('#FFF3E0'),
                  borderRadius: pw.BorderRadius.circular(10),
                ),
                child: pw.Column(
                  children: [
                    pw.Text(
                      'تقرير الفواتير',
                      style: pw.TextStyle(
                        fontSize: 24,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                    pw.SizedBox(height: 10),
                    pw.Text(
                      'تاريخ التصدير: ${_getCurrentDate()}',
                      style: const pw.TextStyle(fontSize: 12),
                    ),
                    pw.Text(
                      'عدد الفواتير: ${invoices.length}',
                      style: const pw.TextStyle(fontSize: 12),
                    ),
                  ],
                ),
              ),

              pw.SizedBox(height: 20),

              // قائمة الفواتير
              ...invoices.map((invoice) => pw.Container(
                margin: const pw.EdgeInsets.only(bottom: 15),
                padding: const pw.EdgeInsets.all(15),
                decoration: pw.BoxDecoration(
                  border: pw.Border.all(color: PdfColor.fromHex('#E0E0E0')),
                  borderRadius: pw.BorderRadius.circular(8),
                ),
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text(
                      'فاتورة: ${invoice['invoiceNumber'] ?? ''}',
                      style: pw.TextStyle(
                        fontSize: 16,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                    pw.SizedBox(height: 10),
                    _buildSimpleInfoRow('اسم المحل:', invoice['shopName'] ?? ''),
                    _buildSimpleInfoRow('اسم العميل:', invoice['customerName'] ?? ''),
                    _buildSimpleInfoRow('عدد الثياب:', '${invoice['clothesCount'] ?? 0}'),
                    _buildSimpleInfoRow('الحالة:', invoice['isReady'] == 1 ? 'جاهزة' : 'غير جاهزة'),
                    _buildSimpleInfoRow('تاريخ الإنشاء:', _formatSimpleDate(invoice['createdAt'])),
                  ],
                ),
              )),
            ];
          },
        ),
      );

      return await _saveSimplePDF(pdf, 'الفواتير_${DateTime.now().millisecondsSinceEpoch}');
    } catch (e) {
      throw Exception('فشل في إنشاء ملف PDF: ${e.toString()}');
    }
  }

  /// إنشاء PDF للإحصائيات
  static Future<File> generateStatisticsPDF(Map<String, dynamic> stats, {String? title}) async {
    try {
      final pdf = pw.Document();

      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          textDirection: pw.TextDirection.rtl,
          build: (pw.Context context) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // العنوان
                pw.Container(
                  width: double.infinity,
                  padding: const pw.EdgeInsets.all(20),
                  decoration: pw.BoxDecoration(
                    color: PdfColor.fromHex('#E1F5FE'),
                    borderRadius: pw.BorderRadius.circular(10),
                  ),
                  child: pw.Column(
                    children: [
                      pw.Text(
                        title ?? 'تقرير الإحصائيات',
                        style: pw.TextStyle(
                          fontSize: 24,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                      pw.SizedBox(height: 10),
                      pw.Text(
                        'تاريخ التصدير: ${_getCurrentDate()}',
                        style: const pw.TextStyle(fontSize: 12),
                      ),
                    ],
                  ),
                ),

                pw.SizedBox(height: 20),

                // الإحصائيات
                pw.Container(
                  padding: const pw.EdgeInsets.all(15),
                  decoration: pw.BoxDecoration(
                    border: pw.Border.all(color: PdfColor.fromHex('#E0E0E0')),
                    borderRadius: pw.BorderRadius.circular(8),
                  ),
                  child: pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text(
                        'الإحصائيات المالية',
                        style: pw.TextStyle(
                          fontSize: 18,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                      pw.SizedBox(height: 15),
                      _buildSimpleInfoRow('إجمالي الأرباح:', '${stats['totalEarnings'] ?? 0} ريال'),
                      _buildSimpleInfoRow('إجمالي المصاريف:', '${stats['totalExpenses'] ?? 0} ريال'),
                      _buildSimpleInfoRow('صافي الربح:', '${stats['netProfit'] ?? 0} ريال'),
                      _buildSimpleInfoRow('عدد الأعمال:', '${stats['totalWorks'] ?? 0}'),
                      _buildSimpleInfoRow('عدد المعامل:', '${stats['totalWorkshops'] ?? 0}'),
                      _buildSimpleInfoRow('عدد الفواتير:', '${stats['totalInvoices'] ?? 0}'),
                      _buildSimpleInfoRow('عدد المقاسات:', '${stats['totalMeasurements'] ?? 0}'),
                    ],
                  ),
                ),

                pw.Spacer(),

                // تذييل
                pw.Container(
                  width: double.infinity,
                  padding: const pw.EdgeInsets.all(10),
                  decoration: pw.BoxDecoration(
                    color: PdfColor.fromHex('#F5F5F5'),
                    borderRadius: pw.BorderRadius.circular(5),
                  ),
                  child: pw.Text(
                    'تم إنشاء هذا التقرير بواسطة تطبيق إدارة الخياطين',
                    style: const pw.TextStyle(fontSize: 10),
                    textAlign: pw.TextAlign.center,
                  ),
                ),
              ],
            );
          },
        ),
      );

      return await _saveSimplePDF(pdf, 'الإحصائيات_${DateTime.now().millisecondsSinceEpoch}');
    } catch (e) {
      throw Exception('فشل في إنشاء ملف PDF: ${e.toString()}');
    }
  }

  // دوال مساعدة بسيطة
  static pw.Widget _buildSimpleInfoRow(String label, String value) {
    return pw.Padding(
      padding: const pw.EdgeInsets.symmetric(vertical: 3),
      child: pw.Row(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.SizedBox(
            width: 120,
            child: pw.Text(
              label,
              style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
            ),
          ),
          pw.Expanded(
            child: pw.Text(value),
          ),
        ],
      ),
    );
  }

  static String _formatSimpleDate(String? dateString) {
    if (dateString == null) return 'غير محدد';
    try {
      final date = DateTime.parse(dateString);
      return DateFormat('yyyy/MM/dd').format(date);
    } catch (e) {
      return 'غير محدد';
    }
  }

  static String _getCurrentDate() {
    return DateFormat('yyyy/MM/dd').format(DateTime.now());
  }

  static Future<File> _saveSimplePDF(pw.Document pdf, String fileName) async {
    try {
      final output = await getTemporaryDirectory();
      final file = File('${output.path}/$fileName.pdf');
      await file.writeAsBytes(await pdf.save());
      return file;
    } catch (e) {
      throw Exception('فشل في حفظ ملف PDF: ${e.toString()}');
    }
  }
}
