import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../models/db_helper.dart';
import 'add_tailor_screen.dart';
import 'tailor_details_screen.dart';
import 'tailor_info_screen.dart';
import 'tailor_summary_screen.dart';
import '../../widgets/export_print_buttons.dart';

class TailorsHomeScreen extends StatefulWidget {
  const TailorsHomeScreen({super.key});

  @override
  State<TailorsHomeScreen> createState() => _TailorsHomeScreenState();
}

class _TailorsHomeScreenState extends State<TailorsHomeScreen> {
  List<Map<String, dynamic>> _tailors = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _fetchTailors();
  }

  Future<void> _fetchTailors() async {
    setState(() => _isLoading = true);

    try {
      final tailors = await DBHelper.getAllTailors();
      setState(() {
        _tailors = tailors;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل الخياطين: $e')),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF9F9F9),
      appBar: AppBar(
        title: const Text(
          '👥 قائمة الخياطين',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.purple.shade700,
        foregroundColor: Colors.white,
        elevation: 2,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _tailors.isEmpty
              ? _buildEmptyState()
              : Column(
                  children: [
                    // زر التصدير الشامل للخياطين
                    Container(
                      margin: const EdgeInsets.all(16),
                      child: ExportPrintButtons(
                        data: _tailors,
                        type: 'tailors',
                        title: 'جميع الخياطين',
                        showIndividual: false,
                        showBulk: true,
                      ),
                    ),

                    // قائمة الخياطين
                    Expanded(
                      child: RefreshIndicator(
                        onRefresh: _fetchTailors,
                        child: ListView.builder(
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          itemCount: _tailors.length,
                          itemBuilder: (context, index) {
                            final tailor = _tailors[index];
                            return _buildTailorCard(tailor);
                          },
                        ),
                      ),
                    ),
                  ],
                ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () async {
          final result = await Navigator.push(
            context,
            MaterialPageRoute(builder: (_) => const AddTailorScreen()),
          );
          if (result == true) {
            _fetchTailors();
          }
        },
        backgroundColor: Colors.purple.shade600,
        foregroundColor: Colors.white,
        icon: const Icon(Icons.person_add),
        label: const Text('إضافة خياط'),
        heroTag: 'tailors_home_fab', // إضافة heroTag فريد
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.people_outline,
            size: 120,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 24),
          Text(
            'لا يوجد خياطين بعد',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 12),
          Text(
            'ابدأ بإضافة خياط جديد لفريقك',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[500],
            ),
          ),
          const SizedBox(height: 32),
          ElevatedButton.icon(
            onPressed: () async {
              final result = await Navigator.push(
                context,
                MaterialPageRoute(builder: (_) => const AddTailorScreen()),
              );
              if (result == true) {
                _fetchTailors();
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.purple.shade600,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            icon: const Icon(Icons.person_add),
            label: const Text(
              'إضافة خياط جديد',
              style: TextStyle(fontSize: 16),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTailorCard(Map<String, dynamic> tailor) {
    final createdAt = DateTime.tryParse(tailor['createdAt'] ?? '');
    final formattedDate = createdAt != null
        ? DateFormat('dd/MM/yyyy').format(createdAt)
        : 'غير محدد';

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white,
            Colors.purple.withOpacity(0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.purple.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
        border: Border.all(
          color: Colors.purple.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(16),
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (_) => TailorDetailsScreen(tailor: tailor),
            ),
          );
        },
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // الرأس: الاسم والإجراءات
              Row(
                children: [
                  // صورة رمزية
                  CircleAvatar(
                    radius: 25,
                    backgroundColor: Colors.purple.withOpacity(0.1),
                    child: Text(
                      tailor['name']?.substring(0, 1) ?? 'خ',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.purple[700],
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),

                  // اسم الخياط ونوع العمل
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          tailor['name'] ?? 'غير محدد',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.grey[800],
                          ),
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                        ),
                        const SizedBox(height: 4),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 2),
                          decoration: BoxDecoration(
                            color: Colors.purple,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            tailor['workType'] ?? 'غير محدد',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 11,
                              fontWeight: FontWeight.w600,
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // قائمة الإجراءات
                  PopupMenuButton<String>(
                    icon: Icon(Icons.more_vert, color: Colors.grey[600]),
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12)),
                    onSelected: (value) {
                      if (value == 'edit') {
                        _editTailor(tailor);
                      } else if (value == 'delete') {
                        _deleteTailor(tailor);
                      } else if (value == 'info') {
                        _showTailorInfo(tailor);
                      } else if (value == 'summary') {
                        _showTailorSummary(tailor);
                      }
                    },
                    itemBuilder: (context) => [
                      PopupMenuItem(
                        value: 'info',
                        child: Row(
                          children: [
                            Icon(Icons.info, color: Colors.blue[600], size: 20),
                            const SizedBox(width: 8),
                            const Text('معلومات'),
                          ],
                        ),
                      ),
                      PopupMenuItem(
                        value: 'summary',
                        child: Row(
                          children: [
                            Icon(Icons.analytics,
                                color: Colors.green[600], size: 20),
                            const SizedBox(width: 8),
                            const Text('الإحصائيات'),
                          ],
                        ),
                      ),
                      PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          children: [
                            Icon(Icons.edit,
                                color: Colors.orange[600], size: 20),
                            const SizedBox(width: 8),
                            const Text('تعديل'),
                          ],
                        ),
                      ),
                      PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete,
                                color: Colors.red[600], size: 20),
                            const SizedBox(width: 8),
                            const Text('حذف'),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // معلومات الاتصال
              if (tailor['phone'] != null &&
                  tailor['phone'].toString().isNotEmpty)
                _buildInfoRow(
                    Icons.phone, tailor['phone'].toString(), Colors.green),

              // السعر
              // if (tailor['pricePerPiece'] != null)
              //   _buildInfoRow(Icons.attach_money, '${tailor['pricePerPiece']} ريال/قطعة', Colors.blue),
              if (tailor['pricePerPiece'] != null)
                _buildInfoRow(
                  Icons.attach_money,
                  '${(double.tryParse(tailor['pricePerPiece'].toString())?.toInt() ?? 0)} سعر القطعة',
                  Colors.blue,
                ),

              const SizedBox(height: 12),

              // معلومات إضافية
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey[200]!),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: _buildStatInfo(
                          'تاريخ الإضافة', formattedDate, Icons.calendar_today),
                    ),
                    if (tailor['dayName'] != null) ...[
                      Container(
                        width: 1,
                        height: 30,
                        color: Colors.grey[300],
                      ),
                      Expanded(
                        child: _buildStatInfo(
                            'يوم الإضافة', tailor['dayName'], Icons.today),
                      ),
                    ],
                    Container(
                      width: 1,
                      height: 30,
                      color: Colors.grey[300],
                    ),
                    Expanded(
                      child:
                          _buildStatInfo('الحالة', 'نشط', Icons.check_circle),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 12),

              // أزرار التصدير للخياط
              ExportPrintButtons(
                data: tailor,
                type: 'tailors',
                title: 'خياط ${tailor['name']}',
                showIndividual: true,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String text, Color color) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 6),
      child: Row(
        children: [
          Icon(icon, size: 16, color: color),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                color: Colors.grey[700],
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatInfo(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, size: 16, color: Colors.grey[600]),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            color: Colors.grey[800],
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
          overflow: TextOverflow.ellipsis,
          maxLines: 1,
          textAlign: TextAlign.center,
        ),
        Text(
          label,
          style: TextStyle(
            color: Colors.grey[600],
            fontSize: 10,
          ),
          overflow: TextOverflow.ellipsis,
          maxLines: 1,
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  void _editTailor(Map<String, dynamic> tailor) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => AddTailorScreen(tailorToEdit: tailor),
      ),
    );
    if (result == true) {
      _fetchTailors();
    }
  }

  void _deleteTailor(Map<String, dynamic> tailor) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(Icons.warning, color: Colors.red[600]),
            const SizedBox(width: 8),
            const Text('تأكيد الحذف'),
          ],
        ),
        content: Text(
          'هل أنت متأكد من حذف الخياط "${tailor['name'] ?? 'غير محدد'}"?\n\nسيتم حذف جميع الأعمال المرتبطة به.',
          style: TextStyle(color: Colors.grey[700]),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إلغاء',
              style: TextStyle(color: Colors.grey[600]),
            ),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red[600],
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            onPressed: () async {
              Navigator.pop(context);
              await _performDelete(tailor);
            },
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  Future<void> _performDelete(Map<String, dynamic> tailor) async {
    try {
      await DBHelper.deleteTailor(tailor['id']);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Row(
              children: [
                Icon(Icons.check_circle, color: Colors.white),
                SizedBox(width: 8),
                Text('تم حذف الخياط بنجاح'),
              ],
            ),
            backgroundColor: Colors.green[600],
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
        _fetchTailors();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                Text('خطأ في حذف الخياط: $e'),
              ],
            ),
            backgroundColor: Colors.red[600],
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    }
  }

  void _showTailorInfo(Map<String, dynamic> tailor) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => TailorInfoScreen(tailor: tailor),
      ),
    );
  }

  void _showTailorSummary(Map<String, dynamic> tailor) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => TailorSummaryScreen(tailorId: tailor['id']),
      ),
    );
  }
}
