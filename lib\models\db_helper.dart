







// lib/models/db_helper.dart

import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sqflite/sqflite.dart';
import 'dart:io';




// void deleteOldDatabase() async {
//   final path = await getDatabasesPath();
//   await deleteDatabase(join(path, 'your_database_name.db'));
// }

class DBHelper {
  static const _dbName = 'tailor.db';
  static const _dbVersion = 18;  // رفعنا الإصدار إلى 18 لإضافة عمود dayName لجداول invoices وsuits_measurements وwomen_measurements
  static Database? _db;

  static Future<Database> get database async {
    if (_db != null) return _db!;
    _db = await _initDB();
    return _db!;
  }

  // دالة لإعادة تعيين قاعدة البيانات في حالة حدوث مشاكل (استخدام يدوي فقط)
  static Future<void> resetDatabase() async {
    if (_db != null) {
      await _db!.close();
      _db = null;
    }
    final dir = await getApplicationDocumentsDirectory();
    final path = join(dir.path, _dbName);
    await deleteDatabase(path);
    _db = await _initDB();
  }

  // دالة للتحقق من وجود قاعدة البيانات
  static Future<bool> checkDatabaseExists() async {
    final dir = await getApplicationDocumentsDirectory();
    final path = join(dir.path, _dbName);
    return await File(path).exists();
  }

  // دالة لإغلاق قاعدة البيانات
  static Future<void> closeDatabase() async {
    if (_db != null) {
      await _db!.close();
      _db = null;
    }
  }

  static Future<Database> _initDB() async {
    final dir = await getApplicationDocumentsDirectory();
    final path = join(dir.path, _dbName);
// await deleteDatabase(path);
    try {
      // فتح قاعدة البيانات الموجودة أو إنشاء جديدة
      return await openDatabase(
        path,
        version: _dbVersion,
        onCreate: _onCreate,
        onUpgrade: _onUpgrade,
        readOnly: false,
        singleInstance: true,
      );
    } catch (e) {
      // في حالة حدوث خطأ في فتح قاعدة البيانات
      // خطأ في فتح قاعدة البيانات: $e

      // في حالة الخطأ، احذف قاعدة البيانات التالفة وأعد إنشائها
      try {
        await deleteDatabase(path);
        return await openDatabase(
          path,
          version: _dbVersion,
          onCreate: _onCreate,
          onUpgrade: _onUpgrade,
          readOnly: false,
          singleInstance: true,
        );
      } catch (e2) {
        throw Exception('فشل في إنشاء قاعدة البيانات: $e2');
      }
    }
  }

  static Future<void> _onCreate(Database db, int version) async {
    // جدول workshops (يتطابق مع WorkshopModel)
    await db.execute('''
      CREATE TABLE workshops (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        ownerPhone TEXT NOT NULL,
        workType TEXT NOT NULL,
        pricePerPiece REAL NOT NULL,
        isQassas INTEGER NOT NULL DEFAULT 0,
        qassasPrice REAL NOT NULL DEFAULT 0,
        otherTypeName TEXT NOT NULL DEFAULT '',
        otherTypePrice REAL NOT NULL DEFAULT 0,
        dayName TEXT,
        createdAt TEXT NOT NULL
      )
    ''');

    // جدول works (يتطابق مع WorkModel)
    await db.execute('''
      CREATE TABLE works (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        workshopId INTEGER NOT NULL,
        workType TEXT NOT NULL,
        pricePerPiece REAL NOT NULL,
        quantity INTEGER NOT NULL,
        cutQuantity INTEGER NOT NULL DEFAULT 0,
        otherCount INTEGER NOT NULL DEFAULT 0,
        expense REAL NOT NULL,
        notes TEXT,
        date TEXT NOT NULL,
        dayName TEXT,
        updatedAt TEXT,
        isSynced INTEGER NOT NULL DEFAULT 0,
        createdAt TEXT NOT NULL,
        FOREIGN KEY (workshopId) REFERENCES workshops(id) ON DELETE CASCADE
      )
    ''');

    // جدول قياسات الثياب (مربوط بالمعامل)
    await db.execute('''
      CREATE TABLE clothing_measurements (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        workshopId INTEGER NOT NULL,
        billNumber TEXT,
        customerName TEXT NOT NULL,
        phoneNumber TEXT,
        fabricType TEXT,
        quantity INTEGER,
        receivedDate TEXT,
        price REAL,
        paid REAL,
        remaining REAL,
        deliveryDate TEXT,
        notes TEXT,
        height TEXT,
        shoulder TEXT,
        sleeveLength TEXT,
        chest TEXT,
        neck TEXT,
        handDrop TEXT,
        middleHand TEXT,
        cuffLength TEXT,
        cuffHeight TEXT,
        step TEXT,
        neckType TEXT,
        zipperType TEXT,
        handType TEXT,
        pocketType TEXT,
        styleType TEXT,
        buttonType TEXT,
        cuffType TEXT,
        dayName TEXT,
        createdAt TEXT NOT NULL,
        updatedAt TEXT,
        FOREIGN KEY (workshopId) REFERENCES workshops (id) ON DELETE CASCADE
      )
    ''');

    // جدول قياسات البدلات (مربوط بالمعامل)
    await db.execute('''
      CREATE TABLE suits_measurements (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        workshopId INTEGER NOT NULL,
        billNumber TEXT,
        customerName TEXT NOT NULL,
        phoneNumber TEXT,
        fabricType TEXT,
        quantity INTEGER,
        receivedDate TEXT,
        price REAL,
        paid REAL,
        remaining REAL,
        deliveryDate TEXT,
        notes TEXT,

        shirtHeight TEXT,
        shirtShoulder TEXT,
        shirtSleeveLength TEXT,
        shirtChest TEXT,
        shirtStomach TEXT,
        shirtNeck TEXT,
        shirtHandDrop TEXT,
        shirtCuffLength TEXT,
        shirtCuffWidth TEXT,

        pantsHeight TEXT,
        pantsBelt TEXT,
        pantsHip TEXT,
        pantsThigh TEXT,
        pantsKnee TEXT,
        pantsOpening TEXT,

        coatHeight TEXT,
        coatShoulder TEXT,
        coatHandLength TEXT,
        coatChest TEXT,
        coatStomach TEXT,
        coatHandWidth TEXT,
        coatMiddleHand TEXT,
        coatHandOpening TEXT,

        vestHeight TEXT,
        vestShoulder TEXT,
        vestWidth TEXT,

        neckType TEXT,
        zipperType TEXT,
        handType TEXT,
        pocketType TEXT,
        styleType TEXT,
        buttonType TEXT,
        cuffType TEXT,
        dayName TEXT,

        createdAt TEXT NOT NULL,
        updatedAt TEXT,
        FOREIGN KEY (workshopId) REFERENCES workshops (id) ON DELETE CASCADE
      )
    ''');

    // جدول القياسات النسائية (مربوط بالمعامل)
    await db.execute('''
      CREATE TABLE women_measurements (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        workshopId INTEGER NOT NULL,
        billNumber TEXT,
        customerName TEXT NOT NULL,
        phoneNumber TEXT,
        fabricType TEXT,
        quantity INTEGER,
        receivedDate TEXT,
        price REAL,
        paid REAL,
        remaining REAL,
        deliveryDate TEXT,
        notes TEXT,

        fullHeight TEXT,
        shoulder TEXT,
        sleeveLength TEXT,
        chestCircumference TEXT,
        waist TEXT,
        hip TEXT,
        modelLength TEXT,
        frontLength TEXT,
        backLength TEXT,
        backCollar TEXT,
        chestDrop TEXT,
        hipDrop TEXT,
        backDrop TEXT,
        neckCircumference TEXT,
        backWidth TEXT,
        chestWidth TEXT,
        sleeveSeatWidth TEXT,
        wristWidth TEXT,
        pantsLength TEXT,
        pantsBelt TEXT,
        pantsHip TEXT,
        pantsThigh TEXT,
        sittingHeight TEXT,
        knee TEXT,
        legOpening TEXT,

        neckType TEXT,
        zipperType TEXT,
        handType TEXT,
        pocketType TEXT,
        styleType TEXT,
        buttonType TEXT,
        cuffType TEXT,
        dayName TEXT,

        createdAt TEXT NOT NULL,
        updatedAt TEXT,
        FOREIGN KEY (workshopId) REFERENCES workshops (id) ON DELETE CASCADE
      )
    ''');

    // جدول الرصيد المرحل من الشهر الأول
    await db.execute('''
      CREATE TABLE initial_balance (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        tailorId INTEGER NOT NULL,
        debtFromFirstMonth REAL NOT NULL DEFAULT 0,
        extraFromFirstMonth REAL NOT NULL DEFAULT 0,
        createdAt TEXT NOT NULL,
        updatedAt TEXT,
        FOREIGN KEY (tailorId) REFERENCES workshops (id) ON DELETE CASCADE
      )
    ''');

    // جدول الغرامات
    await db.execute('''
      CREATE TABLE penalties (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        tailorId INTEGER NOT NULL,
        penaltyDescription TEXT NOT NULL,
        penaltyAmount REAL NOT NULL,
        penaltyDate TEXT NOT NULL,
        createdAt TEXT NOT NULL,
        updatedAt TEXT,
        FOREIGN KEY (tailorId) REFERENCES workshops (id) ON DELETE CASCADE
      )
    ''');

    // جدول الأعمال الإضافية
    await db.execute('''
      CREATE TABLE extra_work (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        tailorId INTEGER NOT NULL,
        extraWorkDescription TEXT NOT NULL,
        extraWorkAmount REAL NOT NULL,
        extraWorkDate TEXT NOT NULL,
        createdAt TEXT NOT NULL,
        updatedAt TEXT,
        FOREIGN KEY (tailorId) REFERENCES workshops (id) ON DELETE CASCADE
      )
    ''');

    // جدول تصفية الحساب الشهري
    await db.execute('''
      CREATE TABLE monthly_settlements (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        tailorId INTEGER NOT NULL,
        month INTEGER NOT NULL,
        year INTEGER NOT NULL,
        totalIncome REAL NOT NULL DEFAULT 0,
        totalExpenses REAL NOT NULL DEFAULT 0,
        totalPenalties REAL NOT NULL DEFAULT 0,
        totalExtraWork REAL NOT NULL DEFAULT 0,
        previousBalance REAL NOT NULL DEFAULT 0,
        finalBalance REAL NOT NULL DEFAULT 0,
        settlementDate TEXT NOT NULL,
        notes TEXT,
        createdAt TEXT NOT NULL,
        updatedAt TEXT,
        FOREIGN KEY (tailorId) REFERENCES workshops (id) ON DELETE CASCADE
      )
    ''');

    // جدول الفواتير (مربوط بالمعامل)
    await db.execute('''
      CREATE TABLE invoices (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        workshopId INTEGER NOT NULL,
        shopName TEXT NOT NULL,
        invoiceNumber TEXT NOT NULL,
        customerName TEXT NOT NULL,
        clothesCount INTEGER NOT NULL,
        isReady INTEGER NOT NULL DEFAULT 0,
        dayName TEXT,
        createdAt TEXT NOT NULL,
        FOREIGN KEY (workshopId) REFERENCES workshops (id) ON DELETE CASCADE
      )
    ''');

    // جدول tailors (الخياطين - لمدير الخياطين)
    await db.execute('''
      CREATE TABLE tailors (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        phone TEXT,
        workType TEXT,
        pricePerPiece REAL,
        address TEXT,
        notes TEXT,
        isCutter INTEGER DEFAULT 0,
        cutPrice REAL,
        hasOtherWork INTEGER DEFAULT 0,
        otherWorkType TEXT,
        otherWorkPrice REAL,
        dayName TEXT,
        createdAt TEXT,
        updatedAt TEXT
      )
    ''');

    // جدول tailor_works (أعمال الخياطين)
    await db.execute('''
      CREATE TABLE tailor_works (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        tailorId INTEGER NOT NULL,
        workType TEXT,
        quantity INTEGER,
        cutQuantity INTEGER DEFAULT 0,
        otherQuantity INTEGER DEFAULT 0,
        pricePerPiece REAL,
        cutPrice REAL,
        otherWorkPrice REAL,
        totalPrice REAL,
        dailyExpense REAL DEFAULT 0,
        executionDate TEXT,
        notes TEXT,
        dayName TEXT,
        createdAt TEXT,
        updatedAt TEXT,
        FOREIGN KEY (tailorId) REFERENCES tailors (id) ON DELETE CASCADE
      )
    ''');

    // جدول فواتير المدير (منفصل عن فواتير المعامل)
    await db.execute('''
      CREATE TABLE manager_invoices (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        shopName TEXT NOT NULL,
        invoiceNumber TEXT NOT NULL,
        customerName TEXT NOT NULL,
        clothesCount INTEGER NOT NULL,
        isReady INTEGER NOT NULL DEFAULT 0,
        dayName TEXT,
        createdAt TEXT NOT NULL
      )
    ''');

    // جدول مقاسات الثياب للمدير
    await db.execute('''
      CREATE TABLE manager_clothing_measurements (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        billNumber TEXT,
        customerName TEXT NOT NULL,
        phoneNumber TEXT,
        fabricType TEXT,
        quantity INTEGER,
        receivedDate TEXT,
        price REAL,
        paid REAL,
        remaining REAL,
        deliveryDate TEXT,
        notes TEXT,
        height TEXT,
        shoulder TEXT,
        sleeveLength TEXT,
        chest TEXT,
        neck TEXT,
        handDrop TEXT,
        middleHand TEXT,
        cuffLength TEXT,
        cuffHeight TEXT,
        step TEXT,
        neckType TEXT,
        zipperType TEXT,
        handType TEXT,
        pocketType TEXT,
        styleType TEXT,
        buttonType TEXT,
        cuffType TEXT,
        dayName TEXT,
        createdAt TEXT NOT NULL,
        updatedAt TEXT
      )
    ''');

    // جدول مقاسات البدلات للمدير
    await db.execute('''
      CREATE TABLE manager_suits_measurements (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        billNumber TEXT,
        customerName TEXT NOT NULL,
        phoneNumber TEXT,
        fabricType TEXT,
        quantity INTEGER,
        receivedDate TEXT,
        price REAL,
        paid REAL,
        remaining REAL,
        deliveryDate TEXT,
        notes TEXT,
        shirtHeight TEXT,
        shirtShoulder TEXT,
        shirtSleeveLength TEXT,
        shirtChest TEXT,
        shirtStomach TEXT,
        shirtNeck TEXT,
        shirtHandDrop TEXT,
        shirtCuffLength TEXT,
        shirtCuffWidth TEXT,
        pantsHeight TEXT,
        pantsBelt TEXT,
        pantsHip TEXT,
        pantsThigh TEXT,
        pantsKnee TEXT,
        pantsOpening TEXT,
        coatHeight TEXT,
        coatShoulder TEXT,
        coatHandLength TEXT,
        coatChest TEXT,
        coatStomach TEXT,
        coatHandWidth TEXT,
        coatMiddleHand TEXT,
        coatHandOpening TEXT,
        cuffType TEXT,
        dayName TEXT,
        createdAt TEXT NOT NULL,
        updatedAt TEXT
      )
    ''');

    // جدول المقاسات النسائية للمدير
    await db.execute('''
      CREATE TABLE manager_women_measurements (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        billNumber TEXT,
        customerName TEXT NOT NULL,
        phoneNumber TEXT,
        fabricType TEXT,
        quantity INTEGER,
        receivedDate TEXT,
        price REAL,
        paid REAL,
        remaining REAL,
        deliveryDate TEXT,
        notes TEXT,
        height TEXT,
        shoulder TEXT,
        sleeve TEXT,
        chest TEXT,
        waist TEXT,
        hip TEXT,
        dressLength TEXT,
        neckOpening TEXT,
        sleeveWidth TEXT,
        sleeveLengthFromShoulder TEXT,
        armCircumference TEXT,
        legOpening TEXT,
        dayName TEXT,
        createdAt TEXT NOT NULL,
        updatedAt TEXT
      )
    ''');

    // جداول خيارات التفصيل
    // جدول أنواع الرقاب
    await db.execute('''
      CREATE TABLE neck_types (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        createdAt TEXT NOT NULL
      )
    ''');

    // جدول أنواع السحابات
    await db.execute('''
      CREATE TABLE zipper_types (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        createdAt TEXT NOT NULL
      )
    ''');

    // جدول أنواع الأيادي
    await db.execute('''
      CREATE TABLE hand_types (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        createdAt TEXT NOT NULL
      )
    ''');

    // جدول أنواع الجيوب
    await db.execute('''
      CREATE TABLE pocket_types (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        createdAt TEXT NOT NULL
      )
    ''');

    // جدول أنواع التفصيل
    await db.execute('''
      CREATE TABLE style_types (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        createdAt TEXT NOT NULL
      )
    ''');

    // جدول أنواع الأزرار
    await db.execute('''
      CREATE TABLE button_types (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        createdAt TEXT NOT NULL
      )
    ''');

    // جدول أنواع الكبك
    await db.execute('''
      CREATE TABLE cuff_types (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        createdAt TEXT NOT NULL
      )
    ''');

    // إدراج بيانات افتراضية
    await _insertDefaultDetailOptions(db);
  }

  // إدراج بيانات افتراضية لخيارات التفصيل
  static Future<void> _insertDefaultDetailOptions(Database db) async {
    final now = DateTime.now().toIso8601String();

    // أنواع الرقاب الافتراضية
    final neckTypes = ['عادية', 'مفتوحة', 'مغلقة', 'بياقة', 'رقبة صينية', 'رقبة مربعة'];
    for (final type in neckTypes) {
      await db.insert('neck_types', {'name': type, 'createdAt': now});
    }

    // أنواع السحابات الافتراضية
    final zipperTypes = ['بدون', 'عادي', 'مخفي', 'جانبي', 'سحاب معدني', 'سحاب بلاستيكي'];
    for (final type in zipperTypes) {
      await db.insert('zipper_types', {'name': type, 'createdAt': now});
    }

    // أنواع الأيادي الافتراضية
    final handTypes = ['عادية', 'ضيقة', 'واسعة', 'مطاطية', 'منفوخة', 'مجمعة'];
    for (final type in handTypes) {
      await db.insert('hand_types', {'name': type, 'createdAt': now});
    }

    // أنواع الجيوب الافتراضية
    final pocketTypes = ['بدون', 'جيب واحد', 'جيبين', 'ثلاثة جيوب', 'جيب داخلي', 'جيب خارجي'];
    for (final type in pocketTypes) {
      await db.insert('pocket_types', {'name': type, 'createdAt': now});
    }

    // أنواع التفصيل الافتراضية
    final styleTypes = ['عادي', 'رسمي', 'رياضي', 'عصري', 'كلاسيكي', 'موديرن'];
    for (final type in styleTypes) {
      await db.insert('style_types', {'name': type, 'createdAt': now});
    }

    // أنواع الأزرار الافتراضية
    final buttonTypes = ['بدون', 'زر عادي', 'زر معدني', 'زر خشبي', 'زر بلاستيكي', 'زر مزخرف'];
    for (final type in buttonTypes) {
      await db.insert('button_types', {'name': type, 'createdAt': now});
    }

    // أنواع الكبك الافتراضية
    final cuffTypes = ['بدون', 'كبك عادي', 'كبك فرنسي', 'كبك مربع', 'كبك مستدير', 'كبك مزخرف'];
    for (final type in cuffTypes) {
      await db.insert('cuff_types', {'name': type, 'createdAt': now});
    }
  }

  static Future<void> _onUpgrade(Database db, int oldV, int newV) async {
    // من الإصدار 1 إلى 2 نضيف أعمدة updatedAt + managerId للـ workshops،
    // و isSynced + updatedAt للـ works
    if (oldV < 2) {
      await db.execute(
          'ALTER TABLE workshops ADD COLUMN updatedAt TEXT NOT NULL DEFAULT ""');
      await db.execute(
          'ALTER TABLE workshops ADD COLUMN managerId TEXT NOT NULL DEFAULT ""');
      await db.execute(
          'ALTER TABLE workshops ADD COLUMN isSynced INTEGER NOT NULL DEFAULT 0');
      await db.execute(
          'ALTER TABLE works ADD COLUMN isSynced INTEGER NOT NULL DEFAULT 0');
      await db.execute(
          'ALTER TABLE works ADD COLUMN updatedAt TEXT NOT NULL DEFAULT ""');
    }
    // من الإصدار 2 إلى 3 نضيف عمود createdAt للـ works
    if (oldV < 3) {
      await db.execute(
          'ALTER TABLE works ADD COLUMN createdAt TEXT NOT NULL DEFAULT ""');
    }

    // من الإصدار 3 إلى 4 نضيف جداول المقاسات
    if (oldV < 4) {
      // جدول قياسات الثياب
      await db.execute('''
        CREATE TABLE IF NOT EXISTS clothing_measurements (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          billNumber TEXT,
          customerName TEXT NOT NULL,
          phoneNumber TEXT,
          fabricType TEXT,
          quantity INTEGER,
          receivedDate TEXT,
          price REAL,
          paid REAL,
          remaining REAL,
          deliveryDate TEXT,
          notes TEXT,
          height TEXT,
          shoulder TEXT,
          sleeveLength TEXT,
          chest TEXT,
          neck TEXT,
          handDrop TEXT,
          middleHand TEXT,
          cuffLength TEXT,
          cuffHeight TEXT,
          step TEXT,
          neckType TEXT,
          zipperType TEXT,
          handType TEXT,
          pocketType TEXT,
          styleType TEXT,
          buttonType TEXT,
          createdAt TEXT NOT NULL,
          updatedAt TEXT
        )
      ''');

      // جدول قياسات البدلات
      await db.execute('''
        CREATE TABLE IF NOT EXISTS suits_measurements (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          billNumber TEXT,
          customerName TEXT NOT NULL,
          phoneNumber TEXT,
          fabricType TEXT,
          quantity INTEGER,
          receivedDate TEXT,
          price REAL,
          paid REAL,
          remaining REAL,
          deliveryDate TEXT,
          notes TEXT,

          shirtHeight TEXT,
          shirtShoulder TEXT,
          shirtSleeveLength TEXT,
          shirtChest TEXT,
          shirtStomach TEXT,
          shirtNeck TEXT,
          shirtHandDrop TEXT,
          shirtCuffLength TEXT,
          shirtCuffWidth TEXT,

          pantsHeight TEXT,
          pantsBelt TEXT,
          pantsHip TEXT,
          pantsThigh TEXT,
          pantsKnee TEXT,
          pantsOpening TEXT,

          coatHeight TEXT,
          coatShoulder TEXT,
          coatHandLength TEXT,
          coatChest TEXT,
          coatStomach TEXT,
          coatHandWidth TEXT,
          coatMiddleHand TEXT,
          coatHandOpening TEXT,

          vestHeight TEXT,
          vestShoulder TEXT,
          vestWidth TEXT,

          createdAt TEXT NOT NULL,
          updatedAt TEXT
        )
      ''');

      // جدول القياسات النسائية
      await db.execute('''
        CREATE TABLE IF NOT EXISTS women_measurements (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          billNumber TEXT,
          customerName TEXT NOT NULL,
          phoneNumber TEXT,
          fabricType TEXT,
          quantity INTEGER,
          receivedDate TEXT,
          price REAL,
          paid REAL,
          remaining REAL,
          deliveryDate TEXT,
          notes TEXT,

          fullHeight TEXT,
          shoulder TEXT,
          sleeveLength TEXT,
          chestCircumference TEXT,
          waist TEXT,
          hip TEXT,
          modelLength TEXT,
          frontLength TEXT,
          backLength TEXT,
          backCollar TEXT,
          chestDrop TEXT,
          hipDrop TEXT,
          backDrop TEXT,
          neckCircumference TEXT,
          backWidth TEXT,
          chestWidth TEXT,
          sleeveSeatWidth TEXT,
          wristWidth TEXT,
          pantsLength TEXT,
          pantsBelt TEXT,
          pantsHip TEXT,
          pantsThigh TEXT,
          sittingHeight TEXT,
          knee TEXT,
          legOpening TEXT,

          createdAt TEXT NOT NULL,
          updatedAt TEXT
        )
      ''');
    }

    // من الإصدار 4 إلى 5 نضيف جداول النظام المالي
    if (oldV < 5) {
      // جدول الرصيد المرحل من الشهر الأول
      await db.execute('''
        CREATE TABLE IF NOT EXISTS initial_balance (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          tailorId INTEGER NOT NULL,
          debtFromFirstMonth REAL NOT NULL DEFAULT 0,
          extraFromFirstMonth REAL NOT NULL DEFAULT 0,
          createdAt TEXT NOT NULL,
          updatedAt TEXT,
          FOREIGN KEY (tailorId) REFERENCES workshops (id) ON DELETE CASCADE
        )
      ''');

      // جدول الغرامات
      await db.execute('''
        CREATE TABLE IF NOT EXISTS penalties (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          tailorId INTEGER NOT NULL,
          penaltyDescription TEXT NOT NULL,
          penaltyAmount REAL NOT NULL,
          penaltyDate TEXT NOT NULL,
          createdAt TEXT NOT NULL,
          updatedAt TEXT,
          FOREIGN KEY (tailorId) REFERENCES workshops (id) ON DELETE CASCADE
        )
      ''');

      // جدول الأعمال الإضافية
      await db.execute('''
        CREATE TABLE IF NOT EXISTS extra_work (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          tailorId INTEGER NOT NULL,
          extraWorkDescription TEXT NOT NULL,
          extraWorkAmount REAL NOT NULL,
          extraWorkDate TEXT NOT NULL,
          createdAt TEXT NOT NULL,
          updatedAt TEXT,
          FOREIGN KEY (tailorId) REFERENCES workshops (id) ON DELETE CASCADE
        )
      ''');

      // جدول تصفية الحساب الشهري
      await db.execute('''
        CREATE TABLE IF NOT EXISTS monthly_settlements (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          tailorId INTEGER NOT NULL,
          month INTEGER NOT NULL,
          year INTEGER NOT NULL,
          totalIncome REAL NOT NULL DEFAULT 0,
          totalExpenses REAL NOT NULL DEFAULT 0,
          totalPenalties REAL NOT NULL DEFAULT 0,
          totalExtraWork REAL NOT NULL DEFAULT 0,
          previousBalance REAL NOT NULL DEFAULT 0,
          finalBalance REAL NOT NULL DEFAULT 0,
          settlementDate TEXT NOT NULL,
          notes TEXT,
          createdAt TEXT NOT NULL,
          updatedAt TEXT,
          FOREIGN KEY (tailorId) REFERENCES workshops (id) ON DELETE CASCADE
        )
      ''');
    }

    // من الإصدار 5 إلى 6 لا نحتاج تغييرات إضافية
    if (oldV < 6) {
      // لا توجد تغييرات مطلوبة
    }

    // من الإصدار 6 إلى 7 نحتاج إعادة بناء الجداول لتطابق مع النماذج
    if (oldV < 7) {
      // إعادة بناء جدول workshops
      await db.execute('DROP TABLE IF EXISTS workshops_backup');
      await db.execute('ALTER TABLE workshops RENAME TO workshops_backup');

      // إنشاء جدول workshops جديد
      await db.execute('''
        CREATE TABLE workshops (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          ownerPhone TEXT NOT NULL,
          workType TEXT NOT NULL,
          pricePerPiece REAL NOT NULL,
          isQassas INTEGER NOT NULL DEFAULT 0,
          qassasPrice REAL NOT NULL DEFAULT 0,
          otherTypeName TEXT NOT NULL DEFAULT '',
          otherTypePrice REAL NOT NULL DEFAULT 0,
          createdAt TEXT NOT NULL
        )
      ''');

      // نسخ البيانات من الجدول القديم
      await db.execute('''
        INSERT INTO workshops (id, name, ownerPhone, workType, pricePerPiece, isQassas, qassasPrice, otherTypeName, otherTypePrice, createdAt)
        SELECT id, name, ownerPhone, workType, pricePerPiece, isQassas, qassasPrice, otherTypeName, otherTypePrice, createdAt
        FROM workshops_backup
      ''');

      // حذف الجدول القديم
      await db.execute('DROP TABLE workshops_backup');

      // إعادة بناء جدول works
      await db.execute('DROP TABLE IF EXISTS works_backup');
      await db.execute('ALTER TABLE works RENAME TO works_backup');

      // إنشاء جدول works جديد
      await db.execute('''
        CREATE TABLE works (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          workshopId INTEGER NOT NULL,
          workType TEXT NOT NULL,
          pricePerPiece REAL NOT NULL,
          quantity INTEGER NOT NULL,
          cutQuantity INTEGER NOT NULL DEFAULT 0,
          otherCount INTEGER NOT NULL DEFAULT 0,
          expense REAL NOT NULL,
          notes TEXT,
          date TEXT NOT NULL,
          FOREIGN KEY (workshopId) REFERENCES workshops(id) ON DELETE CASCADE
        )
      ''');

      // نسخ البيانات من الجدول القديم (مع التحقق من وجود الأعمدة)
      // أولاً نتحقق من الأعمدة الموجودة في الجدول القديم
      final oldColumns = await db.rawQuery('PRAGMA table_info(works_backup)');
      final columnNames = oldColumns.map((col) => col['name'] as String).toSet();

      // بناء استعلام النسخ حسب الأعمدة الموجودة
      String selectClause = 'id, workshopId, workType, pricePerPiece, quantity';

      if (columnNames.contains('cutQuantity')) {
        selectClause += ', COALESCE(cutQuantity, 0) as cutQuantity';
      } else {
        selectClause += ', 0 as cutQuantity';
      }

      if (columnNames.contains('otherCount')) {
        selectClause += ', COALESCE(otherCount, 0) as otherCount';
      } else {
        selectClause += ', 0 as otherCount';
      }

      selectClause += ', expense';

      if (columnNames.contains('notes')) {
        selectClause += ', COALESCE(notes, \'\') as notes';
      } else {
        selectClause += ', \'\'\' as notes';
      }

      selectClause += ', date';

      await db.execute('''
        INSERT INTO works (id, workshopId, workType, pricePerPiece, quantity, cutQuantity, otherCount, expense, notes, date)
        SELECT $selectClause
        FROM works_backup
      ''');

      // حذف الجدول القديم
      await db.execute('DROP TABLE works_backup');
    }

    // من الإصدار 7 إلى 8 لا نحتاج تغييرات إضافية
    if (oldV < 8) {
      // لا توجد تغييرات مطلوبة
    }

    // من الإصدار 8 إلى 9 لا نحتاج تغييرات إضافية
    // الإصلاحات في الكود فقط
    if (oldV < 9) {
      // لا توجد تغييرات في قاعدة البيانات مطلوبة
    }

    // من الإصدار 9 إلى 10 نضيف جدول الفواتير (مربوط بالمعامل)
    if (oldV < 10) {
      await db.execute('''
        CREATE TABLE IF NOT EXISTS invoices (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          workshopId INTEGER NOT NULL,
          shopName TEXT NOT NULL,
          invoiceNumber TEXT NOT NULL,
          customerName TEXT NOT NULL,
          clothesCount INTEGER NOT NULL,
          isReady INTEGER NOT NULL DEFAULT 0,
          createdAt TEXT NOT NULL,
          FOREIGN KEY (workshopId) REFERENCES workshops (id) ON DELETE CASCADE
        )
      ''');
    }

    // من الإصدار 10 إلى 11 نحتاج تعديل جدول الفواتير
    if (oldV < 11) {
      // حذف جدول الفواتير القديم وإعادة إنشائه بالهيكل الجديد
      await db.execute('DROP TABLE IF EXISTS invoices');
      await db.execute('''
        CREATE TABLE invoices (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          workshopId INTEGER NOT NULL,
          shopName TEXT NOT NULL,
          invoiceNumber TEXT NOT NULL,
          customerName TEXT NOT NULL,
          clothesCount INTEGER NOT NULL,
          isReady INTEGER NOT NULL DEFAULT 0,
          createdAt TEXT NOT NULL,
          FOREIGN KEY (workshopId) REFERENCES workshops (id) ON DELETE CASCADE
        )
      ''');
    }

    // من الإصدار 11 إلى 12 نحتاج إضافة workshopId لجداول المقاسات
    if (oldV < 12) {
      // حذف جداول المقاسات القديمة وإعادة إنشائها بالهيكل الجديد
      await db.execute('DROP TABLE IF EXISTS clothing_measurements');
      await db.execute('DROP TABLE IF EXISTS suits_measurements');
      await db.execute('DROP TABLE IF EXISTS women_measurements');

      // إعادة إنشاء جداول المقاسات بالهيكل الجديد
      // جدول قياسات الثياب
      await db.execute('''
        CREATE TABLE clothing_measurements (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          workshopId INTEGER NOT NULL,
          billNumber TEXT,
          customerName TEXT NOT NULL,
          phoneNumber TEXT,
          fabricType TEXT,
          quantity INTEGER,
          receivedDate TEXT,
          price REAL,
          paid REAL,
          remaining REAL,
          deliveryDate TEXT,
          notes TEXT,
          height TEXT,
          shoulder TEXT,
          sleeveLength TEXT,
          chest TEXT,
          neck TEXT,
          handDrop TEXT,
          middleHand TEXT,
          cuffLength TEXT,
          cuffHeight TEXT,
          step TEXT,
          neckType TEXT,
          zipperType TEXT,
          handType TEXT,
          pocketType TEXT,
          styleType TEXT,
          buttonType TEXT,
          cuffType TEXT,
          createdAt TEXT NOT NULL,
          updatedAt TEXT,
          FOREIGN KEY (workshopId) REFERENCES workshops (id) ON DELETE CASCADE
        )
      ''');

      // جدول قياسات البدلات
      await db.execute('''
        CREATE TABLE suits_measurements (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          workshopId INTEGER NOT NULL,
          billNumber TEXT,
          customerName TEXT NOT NULL,
          phoneNumber TEXT,
          fabricType TEXT,
          quantity INTEGER,
          receivedDate TEXT,
          price REAL,
          paid REAL,
          remaining REAL,
          deliveryDate TEXT,
          notes TEXT,
          shirtHeight TEXT,
          shirtShoulder TEXT,
          shirtSleeveLength TEXT,
          shirtChest TEXT,
          shirtStomach TEXT,
          shirtNeck TEXT,
          shirtHandDrop TEXT,
          shirtCuffLength TEXT,
          shirtCuffWidth TEXT,
          pantsHeight TEXT,
          pantsBelt TEXT,
          pantsHip TEXT,
          pantsThigh TEXT,
          pantsKnee TEXT,
          pantsOpening TEXT,
          coatHeight TEXT,
          coatShoulder TEXT,
          coatHandLength TEXT,
          coatChest TEXT,
          coatStomach TEXT,
          coatHandWidth TEXT,
          coatMiddleHand TEXT,
          coatHandOpening TEXT,
          vestHeight TEXT,
          vestShoulder TEXT,
          vestWidth TEXT,
          neckType TEXT,
          zipperType TEXT,
          handType TEXT,
          pocketType TEXT,
          styleType TEXT,
          buttonType TEXT,
          cuffType TEXT,
          createdAt TEXT NOT NULL,
          updatedAt TEXT,
          FOREIGN KEY (workshopId) REFERENCES workshops (id) ON DELETE CASCADE
        )
      ''');

      // جدول القياسات النسائية
      await db.execute('''
        CREATE TABLE women_measurements (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          workshopId INTEGER NOT NULL,
          billNumber TEXT,
          customerName TEXT NOT NULL,
          phoneNumber TEXT,
          fabricType TEXT,
          quantity INTEGER,
          receivedDate TEXT,
          price REAL,
          paid REAL,
          remaining REAL,
          deliveryDate TEXT,
          notes TEXT,
          fullHeight TEXT,
          shoulder TEXT,
          sleeveLength TEXT,
          chestCircumference TEXT,
          waist TEXT,
          hip TEXT,
          modelLength TEXT,
          frontLength TEXT,
          backLength TEXT,
          backCollar TEXT,
          chestDrop TEXT,
          hipDrop TEXT,
          backDrop TEXT,
          neckCircumference TEXT,
          backWidth TEXT,
          chestWidth TEXT,
          sleeveSeatWidth TEXT,
          wristWidth TEXT,
          pantsLength TEXT,
          pantsBelt TEXT,
          pantsHip TEXT,
          pantsThigh TEXT,
          sittingHeight TEXT,
          knee TEXT,
          legOpening TEXT,
          neckType TEXT,
          zipperType TEXT,
          handType TEXT,
          pocketType TEXT,
          styleType TEXT,
          buttonType TEXT,
          cuffType TEXT,
          createdAt TEXT NOT NULL,
          updatedAt TEXT,
          FOREIGN KEY (workshopId) REFERENCES workshops (id) ON DELETE CASCADE
        )
      ''');
    }

    // من الإصدار 12 إلى 13 نضيف أعمدة مفقودة في جدول works
    if (oldV < 13) {
      // إضافة الأعمدة المفقودة في جدول works
      try {
        await db.execute('ALTER TABLE works ADD COLUMN updatedAt TEXT');
      } catch (e) {
        // العمود موجود بالفعل
      }
      try {
        await db.execute('ALTER TABLE works ADD COLUMN isSynced INTEGER NOT NULL DEFAULT 0');
      } catch (e) {
        // العمود موجود بالفعل
      }
      try {
        await db.execute('ALTER TABLE works ADD COLUMN createdAt TEXT NOT NULL DEFAULT ""');
      } catch (e) {
        // العمود موجود بالفعل
      }
    }

    // من الإصدار 13 إلى 14 نضيف جداول الخياطين (لمدير الخياطين)
    if (oldV < 14) {
      // جدول الخياطين
      await db.execute('''
        CREATE TABLE IF NOT EXISTS tailors (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          phone TEXT,
          workType TEXT,
          pricePerPiece REAL,
          address TEXT,
          notes TEXT,
          isCutter INTEGER DEFAULT 0,
          cutPrice REAL,
          hasOtherWork INTEGER DEFAULT 0,
          otherWorkType TEXT,
          otherWorkPrice REAL,
          createdAt TEXT,
          updatedAt TEXT
        )
      ''');

      // جدول أعمال الخياطين
      await db.execute('''
        CREATE TABLE IF NOT EXISTS tailor_works (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          tailorId INTEGER NOT NULL,
          workType TEXT,
          quantity INTEGER,
          cutQuantity INTEGER DEFAULT 0,
          otherQuantity INTEGER DEFAULT 0,
          pricePerPiece REAL,
          cutPrice REAL,
          otherWorkPrice REAL,
          totalPrice REAL,
          dailyExpense REAL DEFAULT 0,
          executionDate TEXT,
          notes TEXT,
          createdAt TEXT,
          updatedAt TEXT,
          FOREIGN KEY (tailorId) REFERENCES tailors (id) ON DELETE CASCADE
        )
      ''');

      // جدول فواتير المدير
      await db.execute('''
        CREATE TABLE IF NOT EXISTS manager_invoices (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          shopName TEXT NOT NULL,
          invoiceNumber TEXT NOT NULL,
          customerName TEXT NOT NULL,
          clothesCount INTEGER NOT NULL,
          isReady INTEGER NOT NULL DEFAULT 0,
          createdAt TEXT NOT NULL
        )
      ''');

      // جدول مقاسات الثياب للمدير
      await db.execute('''
        CREATE TABLE IF NOT EXISTS manager_clothing_measurements (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          billNumber TEXT,
          customerName TEXT NOT NULL,
          phoneNumber TEXT,
          fabricType TEXT,
          quantity INTEGER,
          receivedDate TEXT,
          price REAL,
          paid REAL,
          remaining REAL,
          deliveryDate TEXT,
          notes TEXT,
          height TEXT,
          shoulder TEXT,
          sleeveLength TEXT,
          chest TEXT,
          neck TEXT,
          handDrop TEXT,
          middleHand TEXT,
          cuffLength TEXT,
          cuffHeight TEXT,
          step TEXT,
          neckType TEXT,
          zipperType TEXT,
          handType TEXT,
          pocketType TEXT,
          styleType TEXT,
          buttonType TEXT,
          cuffType TEXT,
          createdAt TEXT NOT NULL,
          updatedAt TEXT
        )
      ''');

      // جدول مقاسات البدلات للمدير
      await db.execute('''
        CREATE TABLE IF NOT EXISTS manager_suits_measurements (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          billNumber TEXT,
          customerName TEXT NOT NULL,
          phoneNumber TEXT,
          fabricType TEXT,
          quantity INTEGER,
          receivedDate TEXT,
          price REAL,
          paid REAL,
          remaining REAL,
          deliveryDate TEXT,
          notes TEXT,
          shirtHeight TEXT,
          shirtShoulder TEXT,
          shirtSleeveLength TEXT,
          shirtChest TEXT,
          shirtStomach TEXT,
          shirtNeck TEXT,
          shirtHandDrop TEXT,
          shirtCuffLength TEXT,
          shirtCuffWidth TEXT,
          pantsHeight TEXT,
          pantsBelt TEXT,
          pantsHip TEXT,
          pantsThigh TEXT,
          pantsKnee TEXT,
          pantsOpening TEXT,
          coatHeight TEXT,
          coatShoulder TEXT,
          coatHandLength TEXT,
          coatChest TEXT,
          coatStomach TEXT,
          coatHandWidth TEXT,
          coatMiddleHand TEXT,
          coatHandOpening TEXT,
          cuffType TEXT,
          createdAt TEXT NOT NULL,
          updatedAt TEXT
        )
      ''');

      // جدول المقاسات النسائية للمدير
      await db.execute('''
        CREATE TABLE IF NOT EXISTS manager_women_measurements (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          billNumber TEXT,
          customerName TEXT NOT NULL,
          phoneNumber TEXT,
          fabricType TEXT,
          quantity INTEGER,
          receivedDate TEXT,
          price REAL,
          paid REAL,
          remaining REAL,
          deliveryDate TEXT,
          notes TEXT,
          height TEXT,
          shoulder TEXT,
          sleeve TEXT,
          chest TEXT,
          waist TEXT,
          hip TEXT,
          dressLength TEXT,
          neckOpening TEXT,
          sleeveWidth TEXT,
          sleeveLengthFromShoulder TEXT,
          armCircumference TEXT,
          legOpening TEXT,
          createdAt TEXT NOT NULL,
          updatedAt TEXT
        )
      ''');

      // جداول خيارات التفصيل
      await db.execute('''
        CREATE TABLE IF NOT EXISTS neck_types (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL UNIQUE,
          createdAt TEXT NOT NULL
        )
      ''');

      await db.execute('''
        CREATE TABLE IF NOT EXISTS zipper_types (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL UNIQUE,
          createdAt TEXT NOT NULL
        )
      ''');

      await db.execute('''
        CREATE TABLE IF NOT EXISTS hand_types (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL UNIQUE,
          createdAt TEXT NOT NULL
        )
      ''');

      await db.execute('''
        CREATE TABLE IF NOT EXISTS pocket_types (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL UNIQUE,
          createdAt TEXT NOT NULL
        )
      ''');

      await db.execute('''
        CREATE TABLE IF NOT EXISTS style_types (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL UNIQUE,
          createdAt TEXT NOT NULL
        )
      ''');

      await db.execute('''
        CREATE TABLE IF NOT EXISTS button_types (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL UNIQUE,
          createdAt TEXT NOT NULL
        )
      ''');

      await db.execute('''
        CREATE TABLE IF NOT EXISTS cuff_types (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL UNIQUE,
          createdAt TEXT NOT NULL
        )
      ''');

      // إدراج بيانات افتراضية إذا لم تكن موجودة
      final neckCount = await db.rawQuery('SELECT COUNT(*) as count FROM neck_types');
      if ((neckCount.first['count'] as int) == 0) {
        await _insertDefaultDetailOptions(db);
      }
    }

    // من الإصدار 14 إلى 15 نضيف عمود dayName لجميع الجداول
    if (oldV < 15) {
      // إضافة عمود dayName لجدول workshops
      try {
        await db.execute('ALTER TABLE workshops ADD COLUMN dayName TEXT');
      } catch (e) {
        // العمود موجود بالفعل
      }

      // إضافة عمود dayName لجدول works
      try {
        await db.execute('ALTER TABLE works ADD COLUMN dayName TEXT');
      } catch (e) {
        // العمود موجود بالفعل
      }

      // إضافة عمود dayName لجدول invoices
      try {
        await db.execute('ALTER TABLE invoices ADD COLUMN dayName TEXT');
      } catch (e) {
        // العمود موجود بالفعل
      }

      // إضافة عمود dayName لجدول clothing_measurements
      try {
        await db.execute('ALTER TABLE clothing_measurements ADD COLUMN dayName TEXT');
      } catch (e) {
        // العمود موجود بالفعل
      }

      // إضافة عمود dayName لجدول suits_measurements
      try {
        await db.execute('ALTER TABLE suits_measurements ADD COLUMN dayName TEXT');
      } catch (e) {
        // العمود موجود بالفعل
      }

      // إضافة عمود dayName لجدول women_measurements
      try {
        await db.execute('ALTER TABLE women_measurements ADD COLUMN dayName TEXT');
      } catch (e) {
        // العمود موجود بالفعل
      }

      // إضافة عمود dayName لجدول tailors
      try {
        await db.execute('ALTER TABLE tailors ADD COLUMN dayName TEXT');
      } catch (e) {
        // العمود موجود بالفعل
      }

      // إضافة عمود dayName لجدول tailor_works
      try {
        await db.execute('ALTER TABLE tailor_works ADD COLUMN dayName TEXT');
      } catch (e) {
        // العمود موجود بالفعل
      }

      // إضافة عمود dayName لجدول manager_invoices
      try {
        await db.execute('ALTER TABLE manager_invoices ADD COLUMN dayName TEXT');
      } catch (e) {
        // العمود موجود بالفعل
      }

      // إضافة عمود dayName لجدول manager_measurements
      try {
        await db.execute('ALTER TABLE manager_measurements ADD COLUMN dayName TEXT');
      } catch (e) {
        // العمود موجود بالفعل
      }
    }

    // من الإصدار 15 إلى 16 نضيف عمود dayName لجداول tailors وtailor_works وmanager_invoices
    if (oldV < 16) {
      // إضافة عمود dayName لجدول tailors (إذا لم يكن موجود)
      try {
        await db.execute('ALTER TABLE tailors ADD COLUMN dayName TEXT');
      } catch (e) {
        // العمود موجود بالفعل
      }

      // إضافة عمود dayName لجدول tailor_works (إذا لم يكن موجود)
      try {
        await db.execute('ALTER TABLE tailor_works ADD COLUMN dayName TEXT');
      } catch (e) {
        // العمود موجود بالفعل
      }

      // إضافة عمود dayName لجدول manager_invoices (إذا لم يكن موجود)
      try {
        await db.execute('ALTER TABLE manager_invoices ADD COLUMN dayName TEXT');
      } catch (e) {
        // العمود موجود بالفعل
      }

      // إضافة عمود dayName لجدول manager_clothing_measurements (إذا لم يكن موجود)
      try {
        await db.execute('ALTER TABLE manager_clothing_measurements ADD COLUMN dayName TEXT');
      } catch (e) {
        // العمود موجود بالفعل
      }
    }

    // من الإصدار 16 إلى 17 نضيف عمود dayName لجداول المدير المتبقية
    if (oldV < 17) {
      // إضافة عمود dayName لجدول manager_suits_measurements
      try {
        await db.execute('ALTER TABLE manager_suits_measurements ADD COLUMN dayName TEXT');
      } catch (e) {
        // العمود موجود بالفعل
      }

      // إضافة عمود dayName لجدول manager_women_measurements
      try {
        await db.execute('ALTER TABLE manager_women_measurements ADD COLUMN dayName TEXT');
      } catch (e) {
        // العمود موجود بالفعل
      }

      // إضافة عمود dayName لجدول manager_clothing_measurements (إذا لم يكن موجود من قبل)
      try {
        await db.execute('ALTER TABLE manager_clothing_measurements ADD COLUMN dayName TEXT');
      } catch (e) {
        // العمود موجود بالفعل
      }
    }

    // من الإصدار 17 إلى 18 نضيف عمود dayName لجداول الخياط المتبقية
    if (oldV < 18) {
      // إضافة عمود dayName لجدول invoices
      try {
        await db.execute('ALTER TABLE invoices ADD COLUMN dayName TEXT');
      } catch (e) {
        // العمود موجود بالفعل
      }

      // إضافة عمود dayName لجدول suits_measurements
      try {
        await db.execute('ALTER TABLE suits_measurements ADD COLUMN dayName TEXT');
      } catch (e) {
        // العمود موجود بالفعل
      }

      // إضافة عمود dayName لجدول women_measurements
      try {
        await db.execute('ALTER TABLE women_measurements ADD COLUMN dayName TEXT');
      } catch (e) {
        // العمود موجود بالفعل
      }
    }
  }

  // إضافة معمل جديد
  static Future<void> insertWorkshop(Map<String, dynamic> data) async {
    final db = await database;
    await db.insert('workshops', data);
  }

  // جلب كل المعامل
  static Future<List<Map<String, dynamic>>> getAllWorkshops() async {
    final db = await database;
    return db.query('workshops', orderBy: 'id DESC');
  }

  // جلب معمل حسب المعرف
  static Future<Map<String, dynamic>?> getWorkshopById(int id) async {
    final db = await database;
    final res = await db.query('workshops', where: 'id = ?', whereArgs: [id]);
    return res.isNotEmpty ? res.first : null;
  }

  // تحديث معمل
  static Future<void> updateWorkshop(int id, Map<String, dynamic> data) async {
    final db = await database;
    await db.update('workshops', data, where: 'id = ?', whereArgs: [id]);
  }

  // حذف معمل
  static Future<void> deleteWorkshop(int id) async {
    final db = await database;
    await db.delete('workshops', where: 'id = ?', whereArgs: [id]);
  }

  // إضافة عمل جديد (يتطابق مع WorkModel)
  static Future<void> insertWork(Map<String, dynamic> data) async {
    final db = await database;
    // لا نحتاج createdAt في WorkModel
    await db.insert('works', data);
  }

  // جلب الأعمال لورشة معينة
  static Future<List<Map<String, dynamic>>> getWorksForWorkshop(int workshopId) async {
    final db = await database;
    return db.query(
      'works',
      where: 'workshopId = ?',
      whereArgs: [workshopId],
      orderBy: 'id DESC',
    );
  }

  // جلب عمل حسب المعرف
  static Future<Map<String, dynamic>?> getWorkById(int id) async {
    final db = await database;
    final res = await db.query('works', where: 'id = ?', whereArgs: [id]);
    return res.isNotEmpty ? res.first : null;
  }

  // تحديث عمل
  static Future<void> updateWork(int id, Map<String, dynamic> data) async {
    final db = await database;
    await db.update('works', data, where: 'id = ?', whereArgs: [id]);
  }

  // حذف عمل
  static Future<void> deleteWork(int id) async {
    final db = await database;
    await db.delete('works', where: 'id = ?', whereArgs: [id]);
  }

  // ========== وظائف إدارة فواتير المدير ==========

  // إضافة فاتورة جديدة للمدير
  static Future<int> insertManagerInvoice(Map<String, dynamic> data) async {
    final db = await database;
    data['createdAt'] ??= DateTime.now().toIso8601String();
    return await db.insert('manager_invoices', data);
  }

  // جلب جميع فواتير المدير
  static Future<List<Map<String, dynamic>>> getAllManagerInvoices() async {
    final db = await database;
    return await db.query('manager_invoices', orderBy: 'id DESC');
  }

  // جلب فاتورة حسب المعرف
  static Future<Map<String, dynamic>?> getManagerInvoiceById(int id) async {
    final db = await database;
    final res = await db.query('manager_invoices', where: 'id = ?', whereArgs: [id]);
    return res.isNotEmpty ? res.first : null;
  }

  // تحديث فاتورة مدير
  static Future<void> updateManagerInvoice(int id, Map<String, dynamic> data) async {
    final db = await database;
    await db.update('manager_invoices', data, where: 'id = ?', whereArgs: [id]);
  }

  // حذف فاتورة مدير
  static Future<void> deleteManagerInvoice(int id) async {
    final db = await database;
    await db.delete('manager_invoices', where: 'id = ?', whereArgs: [id]);
  }

  // بحث في فواتير المدير
  static Future<List<Map<String, dynamic>>> searchManagerInvoices(String query) async {
    final db = await database;
    return await db.query(
      'manager_invoices',
      where: 'customerName LIKE ? OR invoiceNumber LIKE ? OR shopName LIKE ?',
      whereArgs: ['%$query%', '%$query%', '%$query%'],
      orderBy: 'id DESC',
    );
  }

  // حساب إحصائيات فواتير المدير
  static Future<Map<String, dynamic>> getManagerInvoicesStatistics() async {
    final db = await database;

    final totalResult = await db.rawQuery('SELECT COUNT(*) as total FROM manager_invoices');
    final readyResult = await db.rawQuery('SELECT COUNT(*) as ready FROM manager_invoices WHERE isReady = 1');
    final pendingResult = await db.rawQuery('SELECT COUNT(*) as pending FROM manager_invoices WHERE isReady = 0');
    final clothesResult = await db.rawQuery('SELECT SUM(clothesCount) as totalClothes FROM manager_invoices');

    return {
      'totalInvoices': totalResult.first['total'] ?? 0,
      'readyInvoices': readyResult.first['ready'] ?? 0,
      'pendingInvoices': pendingResult.first['pending'] ?? 0,
      'totalClothes': clothesResult.first['totalClothes'] ?? 0,
    };
  }

  // ========== وظائف إدارة مقاسات المدير ==========

  // إضافة مقاس جديد للمدير
  static Future<int> insertManagerMeasurement(String type, Map<String, dynamic> data) async {
    final db = await database;
    data['createdAt'] ??= DateTime.now().toIso8601String();
    data['updatedAt'] ??= DateTime.now().toIso8601String();

    String tableName;
    switch (type) {
      case 'clothing':
        tableName = 'manager_clothing_measurements';
        break;
      case 'suits':
        tableName = 'manager_suits_measurements';
        break;
      case 'women':
        tableName = 'manager_women_measurements';
        break;
      default:
        throw ArgumentError('Invalid measurement type: $type');
    }

    return await db.insert(tableName, data);
  }

  // جلب جميع مقاسات الثياب للمدير
  static Future<List<Map<String, dynamic>>> getAllManagerClothingMeasurements() async {
    final db = await database;
    return await db.query('manager_clothing_measurements', orderBy: 'id DESC');
  }

  // جلب جميع مقاسات البدلات للمدير
  static Future<List<Map<String, dynamic>>> getAllManagerSuitMeasurements() async {
    final db = await database;
    return await db.query('manager_suits_measurements', orderBy: 'id DESC');
  }

  // جلب جميع المقاسات النسائية للمدير
  static Future<List<Map<String, dynamic>>> getAllManagerWomenMeasurements() async {
    final db = await database;
    return await db.query('manager_women_measurements', orderBy: 'id DESC');
  }

  // جلب مقاس محدد للمدير
  static Future<Map<String, dynamic>?> getManagerMeasurementById(int id, String type) async {
    final db = await database;

    String tableName;
    switch (type) {
      case 'clothing':
        tableName = 'manager_clothing_measurements';
        break;
      case 'suits':
        tableName = 'manager_suits_measurements';
        break;
      case 'women':
        tableName = 'manager_women_measurements';
        break;
      default:
        throw ArgumentError('Invalid measurement type: $type');
    }

    final res = await db.query(tableName, where: 'id = ?', whereArgs: [id]);
    return res.isNotEmpty ? res.first : null;
  }

  // تحديث مقاس مدير
  static Future<void> updateManagerMeasurement(int id, String type, Map<String, dynamic> data) async {
    final db = await database;
    data['updatedAt'] = DateTime.now().toIso8601String();

    String tableName;
    switch (type) {
      case 'clothing':
        tableName = 'manager_clothing_measurements';
        break;
      case 'suits':
        tableName = 'manager_suits_measurements';
        break;
      case 'women':
        tableName = 'manager_women_measurements';
        break;
      default:
        throw ArgumentError('Invalid measurement type: $type');
    }

    await db.update(tableName, data, where: 'id = ?', whereArgs: [id]);
  }

  // حذف مقاس مدير
  static Future<void> deleteManagerMeasurement(int id, String type) async {
    final db = await database;

    String tableName;
    switch (type) {
      case 'clothing':
        tableName = 'manager_clothing_measurements';
        break;
      case 'suits':
        tableName = 'manager_suits_measurements';
        break;
      case 'women':
        tableName = 'manager_women_measurements';
        break;
      default:
        throw ArgumentError('Invalid measurement type: $type');
    }

    await db.delete(tableName, where: 'id = ?', whereArgs: [id]);
  }

  // بحث في مقاسات المدير
  static Future<List<Map<String, dynamic>>> searchManagerMeasurements(String type, String query) async {
    final db = await database;

    String tableName;
    switch (type) {
      case 'clothing':
        tableName = 'manager_clothing_measurements';
        break;
      case 'suits':
        tableName = 'manager_suits_measurements';
        break;
      case 'women':
        tableName = 'manager_women_measurements';
        break;
      default:
        throw ArgumentError('Invalid measurement type: $type');
    }

    return await db.query(
      tableName,
      where: 'customerName LIKE ? OR phoneNumber LIKE ? OR billNumber LIKE ?',
      whereArgs: ['%$query%', '%$query%', '%$query%'],
      orderBy: 'id DESC',
    );
  }

  // حساب إحصائيات مقاسات المدير
  static Future<Map<String, dynamic>> getManagerMeasurementsStatistics() async {
    final db = await database;

    final clothingResult = await db.rawQuery('SELECT COUNT(*) as count, SUM(price) as totalPrice, SUM(remaining) as totalRemaining FROM manager_clothing_measurements');
    final suitsResult = await db.rawQuery('SELECT COUNT(*) as count, SUM(price) as totalPrice, SUM(remaining) as totalRemaining FROM manager_suits_measurements');
    final womenResult = await db.rawQuery('SELECT COUNT(*) as count, SUM(price) as totalPrice, SUM(remaining) as totalRemaining FROM manager_women_measurements');

    final clothingCount = clothingResult.first['count'] ?? 0;
    final suitsCount = suitsResult.first['count'] ?? 0;
    final womenCount = womenResult.first['count'] ?? 0;

    final clothingPrice = clothingResult.first['totalPrice'] ?? 0;
    final suitsPrice = suitsResult.first['totalPrice'] ?? 0;
    final womenPrice = womenResult.first['totalPrice'] ?? 0;

    final clothingRemaining = clothingResult.first['totalRemaining'] ?? 0;
    final suitsRemaining = suitsResult.first['totalRemaining'] ?? 0;
    final womenRemaining = womenResult.first['totalRemaining'] ?? 0;

    return {
      'totalMeasurements': (clothingCount as int) + (suitsCount as int) + (womenCount as int),
      'clothingCount': clothingCount,
      'suitsCount': suitsCount,
      'womenCount': womenCount,
      'totalRevenue': (clothingPrice as num) + (suitsPrice as num) + (womenPrice as num),
      'totalRemaining': (clothingRemaining as num) + (suitsRemaining as num) + (womenRemaining as num),
    };
  }

  // ========== وظائف إدارة خيارات التفصيل ==========

  // جلب جميع الخيارات حسب النوع
  static Future<List<Map<String, dynamic>>> getDetailOptions(String type) async {
    final db = await database;
    String tableName;

    switch (type) {
      case 'neck':
        tableName = 'neck_types';
        break;
      case 'zipper':
        tableName = 'zipper_types';
        break;
      case 'hand':
        tableName = 'hand_types';
        break;
      case 'pocket':
        tableName = 'pocket_types';
        break;
      case 'style':
        tableName = 'style_types';
        break;
      case 'button':
        tableName = 'button_types';
        break;
      case 'cuff':
        tableName = 'cuff_types';
        break;
      default:
        throw ArgumentError('Invalid detail option type: $type');
    }

    return await db.query(tableName, orderBy: 'name ASC');
  }

  // إضافة خيار تفصيل جديد
  static Future<int> addDetailOption(String type, String name) async {
    final db = await database;
    String tableName;

    switch (type) {
      case 'neck':
        tableName = 'neck_types';
        break;
      case 'zipper':
        tableName = 'zipper_types';
        break;
      case 'hand':
        tableName = 'hand_types';
        break;
      case 'pocket':
        tableName = 'pocket_types';
        break;
      case 'style':
        tableName = 'style_types';
        break;
      case 'button':
        tableName = 'button_types';
        break;
      case 'cuff':
        tableName = 'cuff_types';
        break;
      default:
        throw ArgumentError('Invalid detail option type: $type');
    }

    return await db.insert(tableName, {
      'name': name,
      'createdAt': DateTime.now().toIso8601String(),
    });
  }

  // حذف خيار تفصيل
  static Future<void> deleteDetailOption(String type, int id) async {
    final db = await database;
    String tableName;

    switch (type) {
      case 'neck':
        tableName = 'neck_types';
        break;
      case 'zipper':
        tableName = 'zipper_types';
        break;
      case 'hand':
        tableName = 'hand_types';
        break;
      case 'pocket':
        tableName = 'pocket_types';
        break;
      case 'style':
        tableName = 'style_types';
        break;
      case 'button':
        tableName = 'button_types';
        break;
      case 'cuff':
        tableName = 'cuff_types';
        break;
      default:
        throw ArgumentError('Invalid detail option type: $type');
    }

    await db.delete(tableName, where: 'id = ?', whereArgs: [id]);
  }

  // تحديث خيار تفصيل
  static Future<void> updateDetailOption(String type, int id, String name) async {
    final db = await database;
    String tableName;

    switch (type) {
      case 'neck':
        tableName = 'neck_types';
        break;
      case 'zipper':
        tableName = 'zipper_types';
        break;
      case 'hand':
        tableName = 'hand_types';
        break;
      case 'pocket':
        tableName = 'pocket_types';
        break;
      case 'style':
        tableName = 'style_types';
        break;
      case 'button':
        tableName = 'button_types';
        break;
      case 'cuff':
        tableName = 'cuff_types';
        break;
      default:
        throw ArgumentError('Invalid detail option type: $type');
    }

    await db.update(tableName, {'name': name}, where: 'id = ?', whereArgs: [id]);
  }

  // جلب جميع خيارات التفصيل لجميع الأنواع
  static Future<Map<String, List<String>>> getAllDetailOptions() async {
    final result = <String, List<String>>{};

    final types = ['neck', 'zipper', 'hand', 'pocket', 'style', 'button', 'cuff'];

    for (final type in types) {
      final options = await getDetailOptions(type);
      result[type] = options.map((option) => option['name'] as String).toList();
    }

    return result;
  }

  // ========== وظائف إدارة الخياطين ==========

  // إضافة خياط جديد
  static Future<int> insertTailor(Map<String, dynamic> data) async {
    final db = await database;
    data['createdAt'] ??= DateTime.now().toIso8601String();
    data['updatedAt'] ??= DateTime.now().toIso8601String();
    return await db.insert('tailors', data);
  }

  // جلب جميع الخياطين
  static Future<List<Map<String, dynamic>>> getAllTailors() async {
    final db = await database;
    return await db.query('tailors', orderBy: 'id DESC');
  }

  // جلب خياط حسب المعرف
  static Future<Map<String, dynamic>?> getTailorById(int id) async {
    final db = await database;
    final res = await db.query('tailors', where: 'id = ?', whereArgs: [id]);
    return res.isNotEmpty ? res.first : null;
  }

  // تحديث خياط
  static Future<void> updateTailor(int id, Map<String, dynamic> data) async {
    final db = await database;
    data['updatedAt'] = DateTime.now().toIso8601String();
    await db.update('tailors', data, where: 'id = ?', whereArgs: [id]);
  }

  // حذف خياط
  static Future<void> deleteTailor(int id) async {
    final db = await database;
    await db.delete('tailors', where: 'id = ?', whereArgs: [id]);
  }

  // إضافة عمل جديد للخياط
  static Future<int> insertTailorWork(Map<String, dynamic> data) async {
    final db = await database;
    data['createdAt'] ??= DateTime.now().toIso8601String();
    data['updatedAt'] ??= DateTime.now().toIso8601String();
    return await db.insert('tailor_works', data);
  }

  // جلب أعمال خياط معين
  static Future<List<Map<String, dynamic>>> getTailorWorks(int tailorId) async {
    final db = await database;
    return await db.query(
      'tailor_works',
      where: 'tailorId = ?',
      whereArgs: [tailorId],
      orderBy: 'id DESC',
    );
  }

  // جلب عمل حسب المعرف
  static Future<Map<String, dynamic>?> getTailorWorkById(int id) async {
    final db = await database;
    final res = await db.query('tailor_works', where: 'id = ?', whereArgs: [id]);
    return res.isNotEmpty ? res.first : null;
  }

  // تحديث عمل خياط
  static Future<void> updateTailorWork(int id, Map<String, dynamic> data) async {
    final db = await database;
    data['updatedAt'] = DateTime.now().toIso8601String();
    await db.update('tailor_works', data, where: 'id = ?', whereArgs: [id]);
  }

  // حذف عمل خياط
  static Future<void> deleteTailorWork(int id) async {
    final db = await database;
    await db.delete('tailor_works', where: 'id = ?', whereArgs: [id]);
  }

  // حذف جميع أعمال خياط معين
  static Future<void> deleteTailorWorks(int tailorId) async {
    final db = await database;
    await db.delete('tailor_works', where: 'tailorId = ?', whereArgs: [tailorId]);
  }

  // حساب إجمالي أرباح خياط
  static Future<double> getTailorTotalEarnings(int tailorId) async {
    final db = await database;
    final result = await db.rawQuery(
      'SELECT SUM(totalPrice) as total FROM tailor_works WHERE tailorId = ?',
      [tailorId],
    );
    return (result.first['total'] as double?) ?? 0.0;
  }

  // حساب إجمالي مصروفات خياط
  static Future<double> getTailorTotalExpenses(int tailorId) async {
    final db = await database;
    final result = await db.rawQuery(
      'SELECT SUM(dailyExpense) as total FROM tailor_works WHERE tailorId = ?',
      [tailorId],
    );
    return (result.first['total'] as double?) ?? 0.0;
  }

  // ==================== تحديث أسعار أعمال الخياط عند تعديل الخياط ====================

  // تحديث أسعار جميع الأعمال المرتبطة بخياط معين
  static Future<int> updateAllTailorWorksPrices(int tailorId, Map<String, dynamic> newPrices) async {
    final db = await database;

    // جلب جميع أعمال الخياط
    final works = await db.query(
      'tailor_works',
      where: 'tailorId = ?',
      whereArgs: [tailorId],
    );

    int updatedCount = 0;

    for (final work in works) {
      final quantity = (work['quantity'] as int?) ?? 0;
      final cutQuantity = (work['cutQuantity'] as int?) ?? 0;
      final otherQuantity = (work['otherQuantity'] as int?) ?? 0;

      // حساب السعر الجديد
      double newTotalPrice = quantity * ((newPrices['pricePerPiece'] as num?)?.toDouble() ?? 0.0);

      if (cutQuantity > 0) {
        newTotalPrice += cutQuantity * ((newPrices['cutPrice'] as num?)?.toDouble() ?? 0.0);
      }

      if (otherQuantity > 0) {
        newTotalPrice += otherQuantity * ((newPrices['otherWorkPrice'] as num?)?.toDouble() ?? 0.0);
      }

      // تحديث العمل بالأسعار والسعر الإجمالي الجديد
      await db.update(
        'tailor_works',
        {
          'pricePerPiece': newPrices['pricePerPiece'],
          'cutPrice': newPrices['cutPrice'] ?? 0.0,
          'otherWorkPrice': newPrices['otherWorkPrice'] ?? 0.0,
          'totalPrice': newTotalPrice,
          'updatedAt': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [work['id']],
      );

      updatedCount++;
    }

    return updatedCount;
  }

  // تحديث خياط مع تحديث أسعار جميع الأعمال المرتبطة
  static Future<Map<String, dynamic>> updateTailorWithPriceUpdate(int tailorId, Map<String, dynamic> tailorData) async {
    try {
      final db = await database;

      // الحصول على بيانات الخياط القديمة
      final oldTailor = await getTailorById(tailorId);

      // بدء معاملة قاعدة بيانات
      await db.transaction((txn) async {
        // تحديث بيانات الخياط
        tailorData['updatedAt'] = DateTime.now().toIso8601String();
        await txn.update('tailors', tailorData, where: 'id = ?', whereArgs: [tailorId]);

        // تحديث أسعار جميع الأعمال
        final works = await txn.query(
          'tailor_works',
          where: 'tailorId = ?',
          whereArgs: [tailorId],
        );

        for (final work in works) {
          final quantity = (work['quantity'] as int?) ?? 0;
          final cutQuantity = (work['cutQuantity'] as int?) ?? 0;
          final otherQuantity = (work['otherQuantity'] as int?) ?? 0;

          // حساب السعر الجديد
          double newTotalPrice = quantity * ((tailorData['pricePerPiece'] as num?)?.toDouble() ?? 0.0);

          if (cutQuantity > 0) {
            newTotalPrice += cutQuantity * ((tailorData['cutPrice'] as num?)?.toDouble() ?? 0.0);
          }

          if (otherQuantity > 0) {
            newTotalPrice += otherQuantity * ((tailorData['otherWorkPrice'] as num?)?.toDouble() ?? 0.0);
          }

          // تحديث العمل بالأسعار والسعر الإجمالي الجديد
          await txn.update(
            'tailor_works',
            {
              'pricePerPiece': tailorData['pricePerPiece'],
              'cutPrice': tailorData['cutPrice'] ?? 0.0,
              'otherWorkPrice': tailorData['otherWorkPrice'] ?? 0.0,
              'totalPrice': newTotalPrice,
              'updatedAt': DateTime.now().toIso8601String(),
            },
            where: 'id = ?',
            whereArgs: [work['id']],
          );
        }
      });

      // إرجاع عدد الأعمال المحدثة
      final worksCount = await getTailorWorksCount(tailorId);

      return {
        'updatedWorksCount': worksCount,
        'oldTailor': oldTailor,
      };
    } catch (e) {
      // في حالة حدوث خطأ في قاعدة البيانات
      if (e.toString().contains('read only') || e.toString().contains('readonly')) {
        // إعادة تعيين قاعدة البيانات
        await resetDatabase();
        // إعادة المحاولة
        return await updateTailorWithPriceUpdate(tailorId, tailorData);
      }
      rethrow;
    }
  }

  // الحصول على عدد الأعمال التي سيتم تحديث أسعارها للخياط
  static Future<int> getTailorWorksCount(int tailorId) async {
    final db = await database;
    final result = await db.rawQuery(
      'SELECT COUNT(*) as count FROM tailor_works WHERE tailorId = ?',
      [tailorId]
    );
    return (result.first['count'] as int?) ?? 0;
  }

  // ========== وظائف إدارة قياسات الثياب ==========

  // إضافة قياس ثوب جديد
  static Future<int> insertClothingMeasurement(Map<String, dynamic> data) async {
    final db = await database;
    data['createdAt'] ??= DateTime.now().toIso8601String();
    data['updatedAt'] ??= DateTime.now().toIso8601String();
    return await db.insert('clothing_measurements', data);
  }

  // جلب جميع قياسات الثياب لمعمل معين
  static Future<List<Map<String, dynamic>>> getClothingMeasurementsForWorkshop(int workshopId) async {
    final db = await database;
    return await db.query(
      'clothing_measurements',
      where: 'workshopId = ?',
      whereArgs: [workshopId],
      orderBy: 'id DESC'
    );
  }

  // جلب جميع قياسات الثياب (للتوافق مع الملفات القديمة)
  static Future<List<Map<String, dynamic>>> getAllClothingMeasurements() async {
    final db = await database;
    return await db.query('clothing_measurements', orderBy: 'id DESC');
  }

  // البحث في قياسات الثياب لمعمل معين
  static Future<List<Map<String, dynamic>>> searchClothingMeasurementsForWorkshop(int workshopId, String query) async {
    final db = await database;
    return await db.query(
      'clothing_measurements',
      where: 'workshopId = ? AND (customerName LIKE ? OR phoneNumber LIKE ? OR billNumber LIKE ?)',
      whereArgs: [workshopId, '%$query%', '%$query%', '%$query%'],
      orderBy: 'id DESC'
    );
  }

  // جلب قياس ثوب بواسطة المعرف
  static Future<Map<String, dynamic>?> getClothingMeasurementById(int id) async {
    final db = await database;
    final results = await db.query(
      'clothing_measurements',
      where: 'id = ?',
      whereArgs: [id],
      limit: 1
    );
    return results.isNotEmpty ? results.first : null;
  }

  // تحديث قياس ثوب
  static Future<int> updateClothingMeasurement(int id, Map<String, dynamic> data) async {
    final db = await database;
    data['updatedAt'] = DateTime.now().toIso8601String();
    return await db.update(
      'clothing_measurements',
      data,
      where: 'id = ?',
      whereArgs: [id]
    );
  }

  // حذف قياس ثوب
  static Future<int> deleteClothingMeasurement(int id) async {
    final db = await database;
    return await db.delete(
      'clothing_measurements',
      where: 'id = ?',
      whereArgs: [id]
    );
  }

  // ========== وظائف إدارة قياسات البدلات ==========

  // إضافة قياس بدلة جديد
  static Future<int> insertSuitMeasurement(Map<String, dynamic> data) async {
    final db = await database;
    data['createdAt'] ??= DateTime.now().toIso8601String();
    data['updatedAt'] ??= DateTime.now().toIso8601String();
    return await db.insert('suits_measurements', data);
  }

  // جلب جميع قياسات البدلات لمعمل معين
  static Future<List<Map<String, dynamic>>> getSuitMeasurementsForWorkshop(int workshopId) async {
    final db = await database;
    return await db.query(
      'suits_measurements',
      where: 'workshopId = ?',
      whereArgs: [workshopId],
      orderBy: 'id DESC'
    );
  }

  // جلب جميع قياسات البدلات (للتوافق مع الملفات القديمة)
  static Future<List<Map<String, dynamic>>> getAllSuitMeasurements() async {
    final db = await database;
    return await db.query('suits_measurements', orderBy: 'id DESC');
  }

  // البحث في قياسات البدلات لمعمل معين
  static Future<List<Map<String, dynamic>>> searchSuitMeasurementsForWorkshop(int workshopId, String query) async {
    final db = await database;
    return await db.query(
      'suits_measurements',
      where: 'workshopId = ? AND (customerName LIKE ? OR phoneNumber LIKE ? OR billNumber LIKE ?)',
      whereArgs: [workshopId, '%$query%', '%$query%', '%$query%'],
      orderBy: 'id DESC'
    );
  }

  // جلب قياس بدلة بواسطة المعرف
  static Future<Map<String, dynamic>?> getSuitMeasurementById(int id) async {
    final db = await database;
    final results = await db.query(
      'suits_measurements',
      where: 'id = ?',
      whereArgs: [id],
      limit: 1
    );
    return results.isNotEmpty ? results.first : null;
  }

  // تحديث قياس بدلة
  static Future<int> updateSuitMeasurement(int id, Map<String, dynamic> data) async {
    final db = await database;
    data['updatedAt'] = DateTime.now().toIso8601String();
    return await db.update(
      'suits_measurements',
      data,
      where: 'id = ?',
      whereArgs: [id]
    );
  }

  // حذف قياس بدلة
  static Future<int> deleteSuitMeasurement(int id) async {
    final db = await database;
    return await db.delete(
      'suits_measurements',
      where: 'id = ?',
      whereArgs: [id]
    );
  }

  // ========== وظائف إدارة القياسات النسائية ==========

  // إضافة قياس نسائي جديد
  static Future<int> insertWomenMeasurement(Map<String, dynamic> data) async {
    final db = await database;
    data['createdAt'] ??= DateTime.now().toIso8601String();
    data['updatedAt'] ??= DateTime.now().toIso8601String();
    return await db.insert('women_measurements', data);
  }

  // جلب جميع القياسات النسائية لمعمل معين
  static Future<List<Map<String, dynamic>>> getWomenMeasurementsForWorkshop(int workshopId) async {
    final db = await database;
    return await db.query(
      'women_measurements',
      where: 'workshopId = ?',
      whereArgs: [workshopId],
      orderBy: 'id DESC'
    );
  }

  // جلب جميع القياسات النسائية (للتوافق مع الملفات القديمة)
  static Future<List<Map<String, dynamic>>> getAllWomenMeasurements() async {
    final db = await database;
    return await db.query('women_measurements', orderBy: 'id DESC');
  }

  // البحث في القياسات النسائية لمعمل معين
  static Future<List<Map<String, dynamic>>> searchWomenMeasurementsForWorkshop(int workshopId, String query) async {
    final db = await database;
    return await db.query(
      'women_measurements',
      where: 'workshopId = ? AND (customerName LIKE ? OR phoneNumber LIKE ? OR billNumber LIKE ?)',
      whereArgs: [workshopId, '%$query%', '%$query%', '%$query%'],
      orderBy: 'id DESC'
    );
  }

  // جلب قياس نسائي بواسطة المعرف
  static Future<Map<String, dynamic>?> getWomenMeasurementById(int id) async {
    final db = await database;
    final results = await db.query(
      'women_measurements',
      where: 'id = ?',
      whereArgs: [id],
      limit: 1
    );
    return results.isNotEmpty ? results.first : null;
  }

  // تحديث قياس نسائي
  static Future<int> updateWomenMeasurement(int id, Map<String, dynamic> data) async {
    final db = await database;
    data['updatedAt'] = DateTime.now().toIso8601String();
    return await db.update(
      'women_measurements',
      data,
      where: 'id = ?',
      whereArgs: [id]
    );
  }

  // حذف قياس نسائي
  static Future<int> deleteWomenMeasurement(int id) async {
    final db = await database;
    return await db.delete(
      'women_measurements',
      where: 'id = ?',
      whereArgs: [id]
    );
  }

  // ==================== نظام إدارة المتأخرات والغرامات ====================

  // ==================== الرصيد المرحل من الشهر الأول ====================

  // إضافة رصيد مرحل جديد
  static Future<int> insertInitialBalance(Map<String, dynamic> data) async {
    final db = await database;
    data['createdAt'] ??= DateTime.now().toIso8601String();
    data['updatedAt'] ??= DateTime.now().toIso8601String();
    return await db.insert('initial_balance', data);
  }

  // جلب الرصيد المرحل لخياط معين
  static Future<Map<String, dynamic>?> getInitialBalanceByTailorId(int tailorId) async {
    final db = await database;
    final results = await db.query(
      'initial_balance',
      where: 'tailorId = ?',
      whereArgs: [tailorId],
      limit: 1
    );
    return results.isNotEmpty ? results.first : null;
  }

  // تحديث الرصيد المرحل
  static Future<int> updateInitialBalance(int id, Map<String, dynamic> data) async {
    final db = await database;
    data['updatedAt'] = DateTime.now().toIso8601String();
    return await db.update(
      'initial_balance',
      data,
      where: 'id = ?',
      whereArgs: [id]
    );
  }

  // حذف الرصيد المرحل
  static Future<int> deleteInitialBalance(int id) async {
    final db = await database;
    return await db.delete(
      'initial_balance',
      where: 'id = ?',
      whereArgs: [id]
    );
  }

  // ==================== الغرامات ====================

  // إضافة غرامة جديدة
  static Future<int> insertPenalty(Map<String, dynamic> data) async {
    final db = await database;
    data['createdAt'] ??= DateTime.now().toIso8601String();
    data['updatedAt'] ??= DateTime.now().toIso8601String();
    return await db.insert('penalties', data);
  }

  // جلب جميع الغرامات
  static Future<List<Map<String, dynamic>>> getAllPenalties() async {
    final db = await database;
    return await db.query('penalties', orderBy: 'penaltyDate DESC');
  }

  // جلب غرامات خياط معين
  static Future<List<Map<String, dynamic>>> getPenaltiesByTailorId(int tailorId) async {
    final db = await database;
    return await db.query(
      'penalties',
      where: 'tailorId = ?',
      whereArgs: [tailorId],
      orderBy: 'penaltyDate DESC'
    );
  }

  // جلب غرامة بواسطة المعرف
  static Future<Map<String, dynamic>?> getPenaltyById(int id) async {
    final db = await database;
    final results = await db.query(
      'penalties',
      where: 'id = ?',
      whereArgs: [id],
      limit: 1
    );
    return results.isNotEmpty ? results.first : null;
  }

  // تحديث غرامة
  static Future<int> updatePenalty(int id, Map<String, dynamic> data) async {
    final db = await database;
    data['updatedAt'] = DateTime.now().toIso8601String();
    return await db.update(
      'penalties',
      data,
      where: 'id = ?',
      whereArgs: [id]
    );
  }

  // حذف غرامة
  static Future<int> deletePenalty(int id) async {
    final db = await database;
    return await db.delete(
      'penalties',
      where: 'id = ?',
      whereArgs: [id]
    );
  }

  // ==================== الأعمال الإضافية ====================

  // إضافة عمل إضافي جديد
  static Future<int> insertExtraWork(Map<String, dynamic> data) async {
    final db = await database;
    data['createdAt'] ??= DateTime.now().toIso8601String();
    data['updatedAt'] ??= DateTime.now().toIso8601String();
    return await db.insert('extra_work', data);
  }

  // جلب جميع الأعمال الإضافية
  static Future<List<Map<String, dynamic>>> getAllExtraWork() async {
    final db = await database;
    return await db.query('extra_work', orderBy: 'extraWorkDate DESC');
  }

  // جلب أعمال إضافية لخياط معين
  static Future<List<Map<String, dynamic>>> getExtraWorkByTailorId(int tailorId) async {
    final db = await database;
    return await db.query(
      'extra_work',
      where: 'tailorId = ?',
      whereArgs: [tailorId],
      orderBy: 'extraWorkDate DESC'
    );
  }

  // جلب عمل إضافي بواسطة المعرف
  static Future<Map<String, dynamic>?> getExtraWorkById(int id) async {
    final db = await database;
    final results = await db.query(
      'extra_work',
      where: 'id = ?',
      whereArgs: [id],
      limit: 1
    );
    return results.isNotEmpty ? results.first : null;
  }

  // تحديث عمل إضافي
  static Future<int> updateExtraWork(int id, Map<String, dynamic> data) async {
    final db = await database;
    data['updatedAt'] = DateTime.now().toIso8601String();
    return await db.update(
      'extra_work',
      data,
      where: 'id = ?',
      whereArgs: [id]
    );
  }

  // حذف عمل إضافي
  static Future<int> deleteExtraWork(int id) async {
    final db = await database;
    return await db.delete(
      'extra_work',
      where: 'id = ?',
      whereArgs: [id]
    );
  }

  // ==================== تصفية الحساب الشهري ====================

  // إضافة تصفية شهرية جديدة
  static Future<int> insertMonthlySettlement(Map<String, dynamic> data) async {
    final db = await database;
    data['createdAt'] ??= DateTime.now().toIso8601String();
    data['updatedAt'] ??= DateTime.now().toIso8601String();
    return await db.insert('monthly_settlements', data);
  }

  // جلب جميع التصفيات الشهرية
  static Future<List<Map<String, dynamic>>> getAllMonthlySettlements() async {
    final db = await database;
    return await db.query('monthly_settlements', orderBy: 'year DESC, month DESC');
  }

  // جلب تصفيات خياط معين
  static Future<List<Map<String, dynamic>>> getMonthlySettlementsByTailorId(int tailorId) async {
    final db = await database;
    return await db.query(
      'monthly_settlements',
      where: 'tailorId = ?',
      whereArgs: [tailorId],
      orderBy: 'year DESC, month DESC'
    );
  }

  // جلب تصفية شهر معين لخياط معين
  static Future<Map<String, dynamic>?> getMonthlySettlement(int tailorId, int month, int year) async {
    final db = await database;
    final results = await db.query(
      'monthly_settlements',
      where: 'tailorId = ? AND month = ? AND year = ?',
      whereArgs: [tailorId, month, year],
      limit: 1
    );
    return results.isNotEmpty ? results.first : null;
  }

  // جلب تصفية بواسطة المعرف
  static Future<Map<String, dynamic>?> getMonthlySettlementById(int id) async {
    final db = await database;
    final results = await db.query(
      'monthly_settlements',
      where: 'id = ?',
      whereArgs: [id],
      limit: 1
    );
    return results.isNotEmpty ? results.first : null;
  }

  // تحديث تصفية شهرية
  static Future<int> updateMonthlySettlement(int id, Map<String, dynamic> data) async {
    final db = await database;
    data['updatedAt'] = DateTime.now().toIso8601String();
    return await db.update(
      'monthly_settlements',
      data,
      where: 'id = ?',
      whereArgs: [id]
    );
  }

  // حذف تصفية شهرية
  static Future<int> deleteMonthlySettlement(int id) async {
    final db = await database;
    return await db.delete(
      'monthly_settlements',
      where: 'id = ?',
      whereArgs: [id]
    );
  }

  // ==================== حسابات مالية مساعدة ====================

  // حساب إجمالي الغرامات لخياط في شهر معين
  static Future<double> getTotalPenaltiesForMonth(int tailorId, int month, int year) async {
    final db = await database;
    final result = await db.rawQuery('''
      SELECT SUM(penaltyAmount) as total
      FROM penalties
      WHERE tailorId = ? AND strftime('%m', penaltyDate) = ? AND strftime('%Y', penaltyDate) = ?
    ''', [tailorId, month.toString().padLeft(2, '0'), year.toString()]);

    return (result.first['total'] as double?) ?? 0.0;
  }

  // حساب إجمالي الأعمال الإضافية لخياط في شهر معين
  static Future<double> getTotalExtraWorkForMonth(int tailorId, int month, int year) async {
    final db = await database;
    final result = await db.rawQuery('''
      SELECT SUM(extraWorkAmount) as total
      FROM extra_work
      WHERE tailorId = ? AND strftime('%m', extraWorkDate) = ? AND strftime('%Y', extraWorkDate) = ?
    ''', [tailorId, month.toString().padLeft(2, '0'), year.toString()]);

    return (result.first['total'] as double?) ?? 0.0;
  }

  // حساب إجمالي المصروفات لخياط في شهر معين
  static Future<double> getTotalExpensesForMonth(int tailorId, int month, int year) async {
    final db = await database;
    final result = await db.rawQuery('''
      SELECT SUM(expense) as total
      FROM works
      WHERE workshopId = ? AND strftime('%m', date) = ? AND strftime('%Y', date) = ?
    ''', [tailorId, month.toString().padLeft(2, '0'), year.toString()]);

    return (result.first['total'] as double?) ?? 0.0;
  }

  // حساب إجمالي الدخل لخياط في شهر معين (بناءً على الكمية والسعر)
  static Future<double> getTotalIncomeForMonth(int tailorId, int month, int year) async {
    final db = await database;
    final result = await db.rawQuery('''
      SELECT SUM((quantity - cutQuantity) * pricePerPiece) as total
      FROM works
      WHERE workshopId = ? AND strftime('%m', date) = ? AND strftime('%Y', date) = ?
    ''', [tailorId, month.toString().padLeft(2, '0'), year.toString()]);

    return (result.first['total'] as double?) ?? 0.0;
  }

  // ==================== تحديث أسعار الأعمال عند تعديل المعمل ====================

  // تحديث سعر جميع الأعمال المرتبطة بمعمل معين (يتطابق مع WorkModel)
  static Future<int> updateAllWorksPricesForWorkshop(int workshopId, double newPrice) async {
    final db = await database;
    return await db.update(
      'works',
      {
        'pricePerPiece': newPrice,
        // لا نحتاج updatedAt في WorkModel
      },
      where: 'workshopId = ?',
      whereArgs: [workshopId],
    );
  }

  // تحديث سعر القصة لجميع الأعمال المرتبطة بمعمل معين (إذا كان المعمل يدعم القصة)
  static Future<int> updateAllWorksQassasPriceForWorkshop(int workshopId, double newQassasPrice) async {
    // هذا يتطلب إضافة عمود qassasPrice في جدول works إذا لم يكن موجوداً
    // للآن سنتجاهل هذا ونركز على السعر الأساسي
    return 0;
  }

  // تحديث معمل مع تحديث أسعار جميع الأعمال المرتبطة
  static Future<void> updateWorkshopWithPriceUpdate(int workshopId, Map<String, dynamic> workshopData) async {
    final db = await database;

    // الحصول على السعر القديم
    final oldWorkshop = await getWorkshopById(workshopId);
    final oldPrice = oldWorkshop != null ? (oldWorkshop['pricePerPiece'] as num?)?.toDouble() ?? 0.0 : 0.0;
    final newPrice = (workshopData['pricePerPiece'] as num?)?.toDouble() ?? 0.0;

    // تحديث معمل $workshopId: السعر القديم = $oldPrice, السعر الجديد = $newPrice

    // تحديث بيانات المعمل
    await db.update(
      'workshops',
      workshopData,
      where: 'id = ?',
      whereArgs: [workshopId],
    );

    // إذا تغير السعر، قم بتحديث جميع الأعمال
    if (oldPrice != newPrice && newPrice > 0) {
      await updateAllWorksPricesForWorkshop(workshopId, newPrice);
    }
  }

  // الحصول على عدد الأعمال التي سيتم تحديث أسعارها
  static Future<int> getWorksCountForWorkshop(int workshopId) async {
    final db = await database;
    final result = await db.rawQuery(
      'SELECT COUNT(*) as count FROM works WHERE workshopId = ?',
      [workshopId]
    );
    return (result.first['count'] as int?) ?? 0;
  }

  // ==================== دوال الفواتير ====================

  // إضافة فاتورة جديدة
  static Future<int> insertInvoice(Map<String, dynamic> data) async {
    final db = await database;
    return await db.insert('invoices', data);
  }

  // جلب جميع فواتير معمل معين
  static Future<List<Map<String, dynamic>>> getInvoicesForWorkshop(int workshopId) async {
    final db = await database;
    return await db.query(
      'invoices',
      where: 'workshopId = ?',
      whereArgs: [workshopId],
      orderBy: 'createdAt DESC'
    );
  }

  // البحث في فواتير معمل معين باسم الزبون أو رقم الفاتورة
  static Future<List<Map<String, dynamic>>> searchInvoicesForWorkshop(int workshopId, String query) async {
    final db = await database;
    return await db.query(
      'invoices',
      where: 'workshopId = ? AND (customerName LIKE ? OR invoiceNumber LIKE ?)',
      whereArgs: [workshopId, '%$query%', '%$query%'],
      orderBy: 'createdAt DESC'
    );
  }

  // تحديث حالة الفاتورة (جاهزة/غير جاهزة)
  static Future<void> updateInvoiceStatus(int invoiceId, bool isReady) async {
    final db = await database;
    await db.update(
      'invoices',
      {'isReady': isReady ? 1 : 0},
      where: 'id = ?',
      whereArgs: [invoiceId]
    );
  }

  // تحديث فاتورة كاملة
  static Future<void> updateInvoice(int invoiceId, Map<String, dynamic> data) async {
    final db = await database;
    await db.update(
      'invoices',
      data,
      where: 'id = ?',
      whereArgs: [invoiceId]
    );
  }

  // حذف فاتورة
  static Future<void> deleteInvoice(int invoiceId) async {
    final db = await database;
    await db.delete(
      'invoices',
      where: 'id = ?',
      whereArgs: [invoiceId]
    );
  }

  // جلب فاتورة بالمعرف
  static Future<Map<String, dynamic>?> getInvoiceById(int invoiceId) async {
    final db = await database;
    final result = await db.query(
      'invoices',
      where: 'id = ?',
      whereArgs: [invoiceId]
    );
    return result.isNotEmpty ? result.first : null;
  }

}

