# خطة تنفيذ إدارة المقاسات في تطبيق Flutter باستخدام SQLite

## المتطلبات

تطوير نظام لإدارة المقاسات في تطبيق Flutter يشمل:
- عرض المقاسات حسب النوع (ثياب، بدلات، مقاسات نسائية)
- وظائف التحكم: تعديل وحذف المقاسات
- تصميم احترافي وجذاب باستخدام Card Widget
- دعم اللغة العربية

## تصميم قاعدة البيانات

سنستخدم جداول منفصلة لكل نوع من المقاسات لسهولة الاستعلام والفصل بين البيانات:

### 1. جدول قياسات الثياب (clothing_measurements)

```sql
CREATE TABLE clothing_measurements (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  billNumber TEXT,
  customerName TEXT NOT NULL,
  phoneNumber TEXT,
  fabricType TEXT,
  quantity INTEGER,
  receivedDate TEXT,
  price REAL,
  paid REAL,
  remaining REAL,
  deliveryDate TEXT,
  notes TEXT,
  height TEXT,
  shoulder TEXT,
  sleeveLength TEXT,
  chest TEXT,
  neck TEXT,
  handDrop TEXT,
  middleHand TEXT,
  cuffLength TEXT,
  cuffHeight TEXT,
  step TEXT,
  neckType TEXT,
  zipperType TEXT,
  handType TEXT,
  pocketType TEXT,
  styleType TEXT,
  buttonType TEXT,
  createdAt TEXT NOT NULL,
  updatedAt TEXT
)
```

### 2. جدول قياسات البدلات (suits_measurements)

```sql
CREATE TABLE suits_measurements (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  billNumber TEXT,
  customerName TEXT NOT NULL,
  phoneNumber TEXT,
  fabricType TEXT,
  quantity INTEGER,
  receivedDate TEXT,
  price REAL,
  paid REAL,
  remaining REAL,
  deliveryDate TEXT,
  notes TEXT,
  
  -- قياسات القميص
  shirtHeight TEXT,
  shirtShoulder TEXT,
  shirtSleeveLength TEXT,
  shirtChest TEXT,
  shirtStomach TEXT,
  shirtNeck TEXT,
  shirtHandDrop TEXT,
  shirtCuffLength TEXT,
  shirtCuffWidth TEXT,
  
  -- قياسات البنطلون
  pantsHeight TEXT,
  pantsBelt TEXT,
  pantsHip TEXT,
  pantsThigh TEXT,
  pantsKnee TEXT,
  pantsOpening TEXT,
  
  -- قياسات الكوت
  coatHeight TEXT,
  coatShoulder TEXT,
  coatHandLength TEXT,
  coatChest TEXT,
  coatStomach TEXT,
  coatHandWidth TEXT,
  coatMiddleHand TEXT,
  coatHandOpening TEXT,
  
  -- قياسات اليلق
  vestHeight TEXT,
  vestShoulder TEXT,
  vestWidth TEXT,
  
  createdAt TEXT NOT NULL,
  updatedAt TEXT
)
```

### 3. جدول القياسات النسائية (women_measurements)

```sql
CREATE TABLE women_measurements (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  billNumber TEXT,
  customerName TEXT NOT NULL,
  phoneNumber TEXT,
  fabricType TEXT,
  quantity INTEGER,
  receivedDate TEXT,
  price REAL,
  paid REAL,
  remaining REAL,
  deliveryDate TEXT,
  notes TEXT,
  
  fullHeight TEXT,
  shoulder TEXT,
  sleeveLength TEXT,
  chestCircumference TEXT,
  waist TEXT,
  hip TEXT,
  modelLength TEXT,
  frontLength TEXT,
  backLength TEXT,
  backCollar TEXT,
  chestDrop TEXT,
  hipDrop TEXT,
  backDrop TEXT,
  neckCircumference TEXT,
  backWidth TEXT,
  chestWidth TEXT,
  sleeveSeatWidth TEXT,
  wristWidth TEXT,
  pantsLength TEXT,
  pantsBelt TEXT,
  pantsHip TEXT,
  pantsThigh TEXT,
  sittingHeight TEXT,
  knee TEXT,
  legOpening TEXT,
  
  createdAt TEXT NOT NULL,
  updatedAt TEXT
)
```

## تحديث ملف DBHelper

سنقوم بتحديث ملف `db_helper.dart` لإضافة الجداول الجديدة ووظائف CRUD:

### 1. إضافة الجداول في دالة _onCreate

```dart
// في db_helper.dart - دالة _onCreate أو initDB
static Future<void> _onCreate(Database db, int version) async {
  // جدول قياسات الثياب
  await db.execute('''
    CREATE TABLE clothing_measurements (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      billNumber TEXT,
      customerName TEXT NOT NULL,
      phoneNumber TEXT,
      fabricType TEXT,
      quantity INTEGER,
      receivedDate TEXT,
      price REAL,
      paid REAL,
      remaining REAL,
      deliveryDate TEXT,
      notes TEXT,
      height TEXT,
      shoulder TEXT,
      sleeveLength TEXT,
      chest TEXT,
      neck TEXT,
      handDrop TEXT,
      middleHand TEXT,
      cuffLength TEXT,
      cuffHeight TEXT,
      step TEXT,
      neckType TEXT,
      zipperType TEXT,
      handType TEXT,
      pocketType TEXT,
      styleType TEXT,
      buttonType TEXT,
      createdAt TEXT NOT NULL,
      updatedAt TEXT
    )
  ''');

  // جدول قياسات البدلات
  await db.execute('''
    CREATE TABLE suits_measurements (
      // بناء على هيكل البيانات المحدد أعلاه
      ...
    )
  ''');

  // جدول القياسات النسائية
  await db.execute('''
    CREATE TABLE women_measurements (
      // بناء على هيكل البيانات المحدد أعلاه
      ...
    )
  ''');
}
```

### 2. إضافة وظائف CRUD لقياسات الثياب

```dart
// إضافة قياس ثوب جديد
static Future<int> insertClothingMeasurement(Map<String, dynamic> data) async {
  final db = await database;
  data['createdAt'] ??= DateTime.now().toIso8601String();
  data['updatedAt'] ??= DateTime.now().toIso8601String();
  return await db.insert('clothing_measurements', data);
}

// جلب جميع قياسات الثياب
static Future<List<Map<String, dynamic>>> getAllClothingMeasurements() async {
  final db = await database;
  return await db.query('clothing_measurements', orderBy: 'id DESC');
}

// جلب قياس ثوب بواسطة المعرف
static Future<Map<String, dynamic>?> getClothingMeasurementById(int id) async {
  final db = await database;
  final results = await db.query(
    'clothing_measurements',
    where: 'id = ?',
    whereArgs: [id],
    limit: 1
  );
  return results.isNotEmpty ? results.first : null;
}

// تحديث قياس ثوب
static Future<int> updateClothingMeasurement(int id, Map<String, dynamic> data) async {
  final db = await database;
  data['updatedAt'] = DateTime.now().toIso8601String();
  return await db.update(
    'clothing_measurements',
    data,
    where: 'id = ?',
    whereArgs: [id]
  );
}

// حذف قياس ثوب
static Future<int> deleteClothingMeasurement(int id) async {
  final db = await database;
  return await db.delete(
    'clothing_measurements',
    where: 'id = ?',
    whereArgs: [id]
  );
}
```

### 3. إضافة وظائف CRUD مماثلة لقياسات البدلات والقياسات النسائية

سيتم إضافة وظائف مماثلة لجداول قياسات البدلات والقياسات النسائية بنفس نمط الوظائف المذكورة أعلاه.

## تحديث شاشات إدخال المقاسات

سنقوم بتحديث الشاشات الحالية لتخزين البيانات في قاعدة البيانات ودعم عمليات التعديل:

### 1. تحديث شاشة قياسات الثياب (clothing_measurements_screen.dart)

```dart
class ClothingMeasurementsScreen extends StatefulWidget {
  final int? measurementId; // إضافة معرف للمقاس لدعم التعديل
  
  const ClothingMeasurementsScreen({super.key, this.measurementId});

  @override
  State<ClothingMeasurementsScreen> createState() => _ClothingMeasurementsScreenState();
}

class _ClothingMeasurementsScreenState extends State<ClothingMeasurementsScreen> {
  // الكود الموجود حالياً...
  
  bool _isLoading = false;
  bool _isEdit = false;
  
  @override
  void initState() {
    super.initState();
    _isEdit = widget.measurementId != null;
    if (_isEdit) {
      _loadMeasurement();
    }
  }
  
  // تحميل بيانات القياس للتعديل
  Future<void> _loadMeasurement() async {
    setState(() => _isLoading = true);
    try {
      final measurement = await DBHelper.getClothingMeasurementById(widget.measurementId!);
      if (measurement != null) {
        // ملء النموذج بالبيانات
        billController.text = measurement['billNumber'] ?? '';
        nameController.text = measurement['customerName'] ?? '';
        phoneController.text = measurement['phoneNumber'] ?? '';
        fabricController.text = measurement['fabricType'] ?? '';
        quantityController.text = measurement['quantity']?.toString() ?? '';
        dateController.text = measurement['receivedDate'] ?? '';
        priceController.text = measurement['price']?.toString() ?? '';
        paidController.text = measurement['paid']?.toString() ?? '';
        remainController.text = measurement['remaining']?.toString() ?? '';
        deliveryDateController.text = measurement['deliveryDate'] ?? '';
        noteController.text = measurement['notes'] ?? '';
        
        // ملء قيم المقاسات
        measurements['الطول']!.text = measurement['height'] ?? '';
        measurements['الكتف']!.text = measurement['shoulder'] ?? '';
        measurements['طول الكم']!.text = measurement['sleeveLength'] ?? '';
        measurements['وسع الصدر']!.text = measurement['chest'] ?? '';
        measurements['الرقبه']!.text = measurement['neck'] ?? '';
        measurements['تنزيله اليد']!.text = measurement['handDrop'] ?? '';
        measurements['وسط اليد']!.text = measurement['middleHand'] ?? '';
        measurements['طول الكبك']!.text = measurement['cuffLength'] ?? '';
        measurements['ارتفاع الكبك']!.text = measurement['cuffHeight'] ?? '';
        measurements['الخطوة']!.text = measurement['step'] ?? '';
        
        // ملء القوائم المنسدلة
        setState(() {
          selectedNeck = measurement['neckType'];
          selectedZipper = measurement['zipperType'];
          selectedHand = measurement['handType'];
          selectedPocket = measurement['pocketType'];
          selectedStyle = measurement['styleType'];
          selectedButtons = measurement['buttonType'];
        });
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }
  
  // حفظ بيانات القياس
  Future<void> _saveMeasurement() async {
    if (!_formKey.currentState!.validate()) return;
    
    setState(() => _isLoading = true);
    try {
      // جمع البيانات من النموذج
      final data = {
        'billNumber': billController.text,
        'customerName': nameController.text,
        'phoneNumber': phoneController.text,
        'fabricType': fabricController.text,
        'quantity': quantityController.text.isNotEmpty ? int.parse(quantityController.text) : null,
        'receivedDate': dateController.text,
        'price': priceController.text.isNotEmpty ? double.parse(priceController.text) : null,
        'paid': paidController.text.isNotEmpty ? double.parse(paidController.text) : null,
        'remaining': remainController.text.isNotEmpty ? double.parse(remainController.text) : null,
        'deliveryDate': deliveryDateController.text,
        'notes': noteController.text,
        
        // بيانات المقاسات
        'height': measurements['الطول']!.text,
        'shoulder': measurements['الكتف']!.text,
        'sleeveLength': measurements['طول الكم']!.text,
        'chest': measurements['وسع الصدر']!.text,
        'neck': measurements['الرقبه']!.text,
        'handDrop': measurements['تنزيله اليد']!.text,
        'middleHand': measurements['وسط اليد']!.text,
        'cuffLength': measurements['طول الكبك']!.text,
        'cuffHeight': measurements['ارتفاع الكبك']!.text,
        'step': measurements['الخطوة']!.text,
        
        // بيانات الخيارات
        'neckType': selectedNeck,
        'zipperType': selectedZipper,
        'handType': selectedHand,
        'pocketType': selectedPocket,
        'styleType': selectedStyle,
        'buttonType': selectedButtons,
      };
      
      if (_isEdit) {
        await DBHelper.updateClothingMeasurement(widget.measurementId!, data);
      } else {
        await DBHelper.insertClothingMeasurement(data);
      }
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(_isEdit ? 'تم تحديث القياس بنجاح' : 'تم حفظ القياس بنجاح')),
      );
      
      if (!mounted) return;
      Navigator.pop(context, true); // إرجاع true للإشارة إلى نجاح العملية
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('حدث خطأ: ${e.toString()}')),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }
  
  // تعديل زر الحفظ ليستدعي دالة _saveMeasurement
  @override
  Widget build(BuildContext context) {
    // الكود الموجود حالياً...
    
    // تعديل زر الحفظ
    MeasurementStyles.saveButton(_isEdit ? "تحديث قياسات العميل" : "حفظ قياسات العميل", () {
      _saveMeasurement();
    }),
    
    // إضافة مؤشر التحميل إذا كان هناك عملية جارية
    if (_isLoading) 
      const Center(child: CircularProgressIndicator()),
  }
}
```

سيتم تنفيذ تحديثات مماثلة لشاشات قياسات البدلات والقياسات النسائية.

## إنشاء شاشات قوائم المقاسات

سنقوم بإنشاء ثلاث شاشات جديدة لعرض المقاسات المحفوظة لكل نوع:

### 1. شاشة قائمة قياسات الثياب (clothing_measurements_list.dart)

```dart
import 'package:flutter/material.dart';
import '../models/db_helper.dart';
import 'measurement_styles.dart';
import 'clothing_measurements_screen.dart';

class ClothingMeasurementsList extends StatefulWidget {
  const ClothingMeasurementsList({super.key});

  @override
  State<ClothingMeasurementsList> createState() => _ClothingMeasurementsListState();
}

class _ClothingMeasurementsListState extends State<ClothingMeasurementsList> {
  List<Map<String, dynamic>> _measurements = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadMeasurements();
  }

  Future<void> _loadMeasurements() async {
    setState(() => _isLoading = true);
    try {
      final measurements = await DBHelper.getAllClothingMeasurements();
      setState(() {
        _measurements = measurements;
      });
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _deleteMeasurement(int id) async {
    try {
      await DBHelper.deleteClothingMeasurement(id);
      _loadMeasurements();
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('تم حذف القياس بنجاح')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('حدث خطأ أثناء الحذف: ${e.toString()}')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: MeasurementStyles.backgroundColor,
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Directionality(
              textDirection: TextDirection.rtl,
              child: _measurements.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Text(
                            'لا توجد قياسات محفوظة',
                            style: TextStyle(fontSize: 18),
                          ),
                          const SizedBox(height: 20),
                          ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              backgroundColor: MeasurementStyles.primaryColor,
                              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                            ),
                            onPressed: () async {
                              final result = await Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => const ClothingMeasurementsScreen(),
                                ),
                              );
                              if (result == true) {
                                _loadMeasurements();
                              }
                            },
                            child: const Text('إضافة قياس جديد'),
                          ),
                        ],
                      ),
                    )
                  : ListView.builder(
                      padding: const EdgeInsets.all(8),
                      itemCount: _measurements.length,
                      itemBuilder: (ctx, index) {
                        final measurement = _measurements[index];
                        return _buildMeasurementCard(measurement);
                      },
                    ),
            ),
      floatingActionButton: FloatingActionButton(
        backgroundColor: MeasurementStyles.primaryColor,
        child: const Icon(Icons.add),
        onPressed: () async {
          final result = await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const ClothingMeasurementsScreen(),
            ),
          );
          if (result == true) {
            _loadMeasurements();
          }
        },
      ),
    );
  }

  Widget _buildMeasurementCard(Map<String, dynamic> measurement) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // الجزء العلوي من البطاقة
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        measurement['customerName'] ?? '',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      if (measurement['billNumber'] != null && measurement['billNumber'].toString().isNotEmpty)
                        Text(
                          'رقم الفاتورة: ${measurement['billNumber']}',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 14,
                          ),
                        ),
                    ],
                  ),
                ),
                Text(
                  measurement['phoneNumber'] ?? '',
                  style: TextStyle(
                    color: Colors.grey[700],
                    fontSize: 15,
                  ),
                ),
              ],
            ),
            const Divider(),
            
            // ملخص المقاسات الرئيسية
            Row(
              children: [
                _buildMeasureSummary('الطول', measurement['height']),
                _buildMeasureSummary('الكتف', measurement['shoulder']),
                _buildMeasureSummary('الصدر', measurement['chest']),
              ],
            ),
            const SizedBox(height: 8),
            
            // معلومات المبلغ
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'المبلغ: ${measurement['price'] ?? 0} ريال',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                Text(
                  'المتبقي: ${measurement['remaining'] ?? 0} ريال',
                  style: TextStyle(
                    color: (measurement['remaining'] ?? 0) > 0
                        ? Colors.red[700]
                        : Colors.green[700],
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const Divider(),
            
            // أزرار التحكم
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton.icon(
                  icon: const Icon(Icons.edit, color: Colors.blue),
                  label: const Text('تعديل', style: TextStyle(color: Colors.blue)),
                  onPressed: () async {
                    final result = await Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => ClothingMeasurementsScreen(
                          measurementId: measurement['id'],
                        ),
                      ),
                    );
                    if (result == true) {
                      _loadMeasurements();
                    }
                  },
                ),
                const SizedBox(width: 8),
                TextButton.icon(
                  icon: const Icon(Icons.delete, color: Colors.red),
                  label: const Text('حذف', style: TextStyle(color: Colors.red)),
                  onPressed: () {
                    showDialog(
                      context: context,
                      builder: (ctx) => AlertDialog(
                        title: const Text('تأكيد الحذف'),
                        content: const Text('هل أنت متأكد من حذف هذا القياس؟'),
                        actions: [
                          TextButton(
                            child: const Text('إلغاء'),
                            onPressed: () => Navigator.of(ctx).pop(),
                          ),
                          TextButton(
                            child: const Text('حذف'),
                            onPressed: () {
                              Navigator.of(ctx).pop();
                              _deleteMeasurement(measurement['id']);
                            },
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMeasureSummary(String label, String? value) {
    return Expanded(
      child: Column(
        children: [
          Text(
            label,
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 14,
            ),
          ),
          Text(
            value ?? '-',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }
}
```

سيتم إنشاء شاشات مماثلة لـ `SuitsMeasurementsList` و `WomenMeasurementsList`.

## تحديث شاشة علامات التبويب

سنقوم بتحديث شاشة `all_measurements_tabs_screen.dart` لاستخدام الشاشات الجديدة:

```dart
import 'package:flutter/material.dart';
import 'clothing_measurements_list.dart';
import 'suits_measurements_list.dart';
import 'women_measurements_list.dart';
import 'measurement_styles.dart';

class AllMeasurementsTabsScreen extends StatelessWidget {
  const AllMeasurementsTabsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 3,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('المقاسات'),
          backgroundColor: MeasurementStyles.primaryColor,
          centerTitle: true,
          bottom: const TabBar(
            indicatorColor: Colors.white,
            tabs: [
              Tab(text: 'الثياب'),
              Tab(text: 'البدلات'),
              Tab(text: 'النسائية'),
            ],
          ),
        ),
        body: const TabBarView(
          children: [
            ClothingMeasurementsList(),
            SuitsMeasurementsList(),
            WomenMeasurementsList(),
          ],
        ),
      ),
    );
  }
}
```

## خطة التنفيذ

1. **تحديث ملف db_helper.dart**:
   - إضافة الجداول الثلاثة لأنواع المقاسات المختلفة
   - إضافة دوال CRUD لكل نوع من المقاسات

2. **تعديل شاشات إدخال المقاسات**:
   - تحديث `clothing_measurements_screen.dart`
   - تحديث `suits_measurements_screen.dart`
   - تحديث `women_measurements_screen.dart`
   - إضافة وظائف الحفظ والتعديل

3. **إنشاء شاشات قوائم المقاسات**:
   - إنشاء `clothing_measurements_list.dart`
   - إنشاء `suits_measurements_list.dart`
   - إنشاء `women_measurements_list.dart`
   - تنفيذ وظائف العرض والتعديل والحذف

4. **تحديث شاشة علامات التبويب**:
   - تعديل `all_measurements_tabs_screen.dart` لاستخدام الشاشات الجديدة

## الملخص

هذه الخطة تقدم حلاً شاملاً لإدارة المقاسات في التطبيق باستخدام جداول منفصلة في SQLite. التصميم يحقق:
1. فصل كامل بين أنواع المقاسات المختلفة
2. تخزين وعرض المقاسات حسب النوع
3. وظائف التحكم (تعديل وحذف)
4. تصميم احترافي وجذاب باستخدام Card Widget
5. دعم اللغة العربية

التصميم قابل للتوسع مستقبلاً وسهل الصيانة مع إمكانية إضافة أنواع جديدة من المقاسات عند الحاجة.