// صفحة الإحصائيات المحسنة داخل النظام المالي
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../models/db_helper.dart';
import '../../widgets/export_print_buttons.dart';
import '../../services/financial_state_manager.dart';
import 'widgets/financial_status_card.dart';

class EnhancedStatisticsScreen extends StatefulWidget {
  final int workshopId;
  final String workshopName;

  const EnhancedStatisticsScreen({
    super.key,
    required this.workshopId,
    required this.workshopName,
  });

  @override
  State<EnhancedStatisticsScreen> createState() => _EnhancedStatisticsScreenState();
}

class _EnhancedStatisticsScreenState extends State<EnhancedStatisticsScreen>
    with TickerProviderStateMixin {

  bool _isLoading = true;

  // بيانات الورشة
  double pricePerPiece = 0.0;
  double qassasPrice = 0.0;
  String otherTypeName = '';
  double otherTypePrice = 0.0;
  bool isQassas = false;

  // ملخص الأرقام
  int totalQuantity = 0;
  double totalIncomeFromProduction = 0.0;
  int totalCutQuantity = 0;
  double totalIncomeFromCuts = 0.0;
  int totalOtherCount = 0;
  double totalIncomeFromOthers = 0.0;
  double totalExpense = 0.0;
  double grossIncome = 0.0;
  double netProfitOrDebt = 0.0;
  bool isInDebt = false;

  // بيانات النظام المالي الجديدة
  double totalPenalties = 0.0;
  double totalExtraWork = 0.0;
  double initialBalance = 0.0;
  double finalBalance = 0.0;

  final FinancialStateManager _financialManager = FinancialStateManager();

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _calculateSummary();
    _financialManager.setCurrentWorkshop(widget.workshopId);

    // إعداد الأنيميشن
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<double>(
      begin: 0.3,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _calculateSummary() async {
    setState(() => _isLoading = true);

    try {
      // 1. جلب بيانات الورشة
      final workshopMap = await DBHelper.getWorkshopById(widget.workshopId);
      if (workshopMap != null) {
        pricePerPiece = (workshopMap['price'] as num? ?? 0).toDouble();
        qassasPrice = (workshopMap['qassasPrice'] as num? ?? 0).toDouble();
        otherTypeName = workshopMap['otherTypeName'] ?? '';
        otherTypePrice = (workshopMap['otherTypePrice'] as num? ?? 0).toDouble();
        isQassas = (workshopMap['isQassas'] as int? ?? 0) == 1;
      }

      // 2. جلب جميع الأعمال
      final works = await DBHelper.getWorksForWorkshop(widget.workshopId);

      // 3. حساب الإحصائيات
      totalQuantity = 0;
      totalIncomeFromProduction = 0.0;
      totalCutQuantity = 0;
      totalIncomeFromCuts = 0.0;
      totalOtherCount = 0;
      totalIncomeFromOthers = 0.0;
      totalExpense = 0.0;

      for (var work in works) {
        final quantity = work['quantity'] as int? ?? 0;
        final cutQuantity = work['cutQuantity'] as int? ?? 0;
        final otherCount = work['otherCount'] as int? ?? 0;
        final expense = (work['expense'] as num? ?? 0).toDouble();

        totalQuantity += quantity;
        totalIncomeFromProduction += quantity * pricePerPiece;

        if (isQassas) {
          totalCutQuantity += cutQuantity;
          totalIncomeFromCuts += cutQuantity * qassasPrice;
        }

        if (otherTypeName.isNotEmpty) {
          totalOtherCount += otherCount;
          totalIncomeFromOthers += otherCount * otherTypePrice;
        }

        totalExpense += expense;
      }

      // 4. جلب البيانات المالية الجديدة
      await _loadFinancialData();

      // 5. حساب الإجماليات
      grossIncome = totalIncomeFromProduction + totalIncomeFromCuts + totalIncomeFromOthers + totalExtraWork;

      // الرصيد النهائي = الدخل الإجمالي - المصروفات - الغرامات + الرصيد المرحل
      finalBalance = grossIncome - totalExpense - totalPenalties + initialBalance;

      netProfitOrDebt = finalBalance;
      isInDebt = netProfitOrDebt < 0;

    } catch (e) {
      debugPrint('خطأ في حساب الملخص: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _loadFinancialData() async {
    try {
      // جلب الغرامات
      final penalties = await DBHelper.getPenaltiesByTailorId(widget.workshopId);
      totalPenalties = penalties.fold(0.0, (sum, penalty) =>
        sum + (penalty['penaltyAmount'] as num? ?? 0).toDouble());

      // جلب الأعمال الإضافية
      final extraWorks = await DBHelper.getExtraWorkByTailorId(widget.workshopId);
      totalExtraWork = extraWorks.fold(0.0, (sum, work) =>
        sum + (work['extraWorkAmount'] as num? ?? 0).toDouble());

      // جلب الرصيد المرحل
      final balance = await DBHelper.getInitialBalanceByTailorId(widget.workshopId);
      if (balance != null) {
        final extra = (balance['extraFromFirstMonth'] as num? ?? 0).toDouble();
        final debt = (balance['debtFromFirstMonth'] as num? ?? 0).toDouble();
        initialBalance = extra - debt;
      } else {
        initialBalance = 0.0;
      }
    } catch (e) {
      // في حالة عدم وجود الجداول أو الدوال، نضع القيم الافتراضية
      totalPenalties = 0.0;
      totalExtraWork = 0.0;
      initialBalance = 0.0;
    }
  }

  String formatNum(double v) {
    if (v == v.roundToDouble()) return v.toInt().toString();
    return v.toStringAsFixed(2);
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: Colors.grey[50],
        body: FadeTransition(
          opacity: _fadeAnimation,
          child: Column(
            children: [
              // هيدر محسن
              _buildEnhancedHeader(),

              // المحتوى الرئيسي
              Expanded(
                child: _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : SlideTransition(
                        position: Tween<Offset>(
                          begin: Offset(0, _slideAnimation.value),
                          end: Offset.zero,
                        ).animate(_animationController),
                        child: SingleChildScrollView(
                          child: Column(
                            children: [
                              // كارد الحالة المالية
                              FinancialStatusCard(
                                workshopId: widget.workshopId,
                                onTap: () {
                                  // يمكن إضافة الانتقال لصفحة أخرى
                                },
                              ),

                              // إحصائيات الإنتاج
                              _buildProductionStats(),

                              // إحصائيات النظام المالي
                              _buildFinancialStats(),

                              // الملخص النهائي
                              _buildFinalSummary(),

                              // أزرار التصدير والطباعة
                              _buildExportButtons(),
                            ],
                          ),
                        ),
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEnhancedHeader() {
    return Container(
      height: 160,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.teal[700]!, Colors.teal[500]!],
          begin: Alignment.topRight,
          end: Alignment.bottomLeft,
        ),
        borderRadius: const BorderRadius.vertical(bottom: Radius.circular(30)),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Row(
                children: [
                  GestureDetector(
                    onTap: () => Navigator.pop(context),
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(Icons.arrow_back, color: Colors.white),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Text(
                      'الإحصائيات المالية الشاملة',
                      style: GoogleFonts.cairo(
                        color: Colors.white,
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(Icons.analytics, color: Colors.white),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                'معمل: ${widget.workshopName}',
                style: GoogleFonts.cairo(
                  color: Colors.white.withOpacity(0.9),
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'تقرير شامل للوضع المالي والإنتاجي',
                style: GoogleFonts.cairo(
                  color: Colors.white.withOpacity(0.8),
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      isInDebt ? Icons.trending_down : Icons.trending_up,
                      color: Colors.white,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      isInDebt ? 'يوجد مديونية' : 'وضع مالي جيد',
                      style: GoogleFonts.cairo(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProductionStats() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue[600],
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.production_quantity_limits,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  'إحصائيات الإنتاج',
                  style: GoogleFonts.cairo(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[800],
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // إحصائيات الإنتاج الرئيسي
          _buildStatCard(
            'الإنتاج الرئيسي',
            '$totalQuantity قطعة',
            '${formatNum(totalIncomeFromProduction)} ريال',
            Icons.inventory,
            Colors.blue,
          ),

          const SizedBox(height: 12),

          // إحصائيات القصاص (إذا كان متاحاً)
          if (isQassas) ...[
            _buildStatCard(
              'القصاص',
              '$totalCutQuantity قطعة',
              '${formatNum(totalIncomeFromCuts)} ريال',
              Icons.content_cut,
              Colors.orange,
            ),
            const SizedBox(height: 12),
          ],

          // إحصائيات الأنواع الأخرى (إذا كانت متاحة)
          if (otherTypeName.isNotEmpty) ...[
            _buildStatCard(
              otherTypeName,
              '$totalOtherCount قطعة',
              '${formatNum(totalIncomeFromOthers)} ريال',
              Icons.category,
              Colors.purple,
            ),
            const SizedBox(height: 12),
          ],

          // إجمالي المصروفات
          _buildStatCard(
            'إجمالي المصروفات',
            'مصروفات يومية',
            '${formatNum(totalExpense)} ريال',
            Icons.money_off,
            Colors.red,
          ),
        ],
      ),
    );
  }

  Widget _buildFinancialStats() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.indigo[600],
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.account_balance,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  'إحصائيات النظام المالي',
                  style: GoogleFonts.cairo(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[800],
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // الرصيد المرحل
          _buildStatCard(
            'الرصيد المرحل',
            initialBalance >= 0 ? 'رصيد موجب' : 'رصيد سالب',
            '${formatNum(initialBalance)} ريال',
            Icons.account_balance_wallet,
            initialBalance >= 0 ? Colors.green : Colors.red,
          ),

          const SizedBox(height: 12),

          // الأعمال الإضافية
          _buildStatCard(
            'الأعمال الإضافية',
            'مكافآت وأعمال إضافية',
            '${formatNum(totalExtraWork)} ريال',
            Icons.add_business,
            Colors.green,
          ),

          const SizedBox(height: 12),

          // الغرامات
          _buildStatCard(
            'الغرامات',
            'خصومات وغرامات',
            '${formatNum(totalPenalties)} ريال',
            Icons.warning,
            Colors.red,
          ),
        ],
      ),
    );
  }

  Widget _buildFinalSummary() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: isInDebt
            ? [Colors.red[50]!, Colors.red[100]!]
            : [Colors.green[50]!, Colors.green[100]!],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: (isInDebt ? Colors.red : Colors.green).withOpacity(0.2),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: isInDebt ? Colors.red[600] : Colors.green[600],
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  isInDebt ? Icons.trending_down : Icons.trending_up,
                  color: Colors.white,
                  size: 32,
                ),
              ),
              const SizedBox(width: 20),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'الملخص النهائي',
                      style: GoogleFonts.cairo(
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        color: Colors.grey[800],
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      isInDebt ? 'يوجد مديونية تحتاج تسديد' : 'الوضع المالي ممتاز',
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 24),

          // الدخل الإجمالي
          _buildSummaryRow(
            'إجمالي الدخل',
            '${formatNum(grossIncome)} ريال',
            Icons.attach_money,
            Colors.green,
          ),

          const SizedBox(height: 12),

          // إجمالي المصروفات والغرامات
          _buildSummaryRow(
            'إجمالي المصروفات والغرامات',
            '${formatNum(totalExpense + totalPenalties)} ريال',
            Icons.money_off,
            Colors.red,
          ),

          const SizedBox(height: 20),

          // الخط الفاصل
          Container(
            height: 2,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.grey[300]!,
                  Colors.grey[400]!,
                  Colors.grey[300]!,
                ],
              ),
            ),
          ),

          const SizedBox(height: 20),

          // الرصيد النهائي
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: isInDebt ? Colors.red[600] : Colors.green[600],
              borderRadius: BorderRadius.circular(16),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  isInDebt ? 'المديونية النهائية:' : 'الرصيد النهائي:',
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                Text(
                  '${isInDebt ? '' : '+'}${formatNum(netProfitOrDebt)} ريال',
                  style: GoogleFonts.cairo(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String subtitle, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
            child: Icon(icon, color: Colors.white, size: 20),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[800],
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Text(
            value,
            style: GoogleFonts.cairo(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value, IconData icon, Color color) {
    return Row(
      children: [
        Icon(icon, color: color, size: 20),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            label,
            style: GoogleFonts.cairo(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.grey[700],
            ),
          ),
        ),
        Text(
          value,
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  Widget _buildExportButtons() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: ExportPrintButtons(
        data: {
          'workshopId': widget.workshopId,
          'workshopName': widget.workshopName,
          'totalQuantity': totalQuantity,
          'totalIncomeFromProduction': totalIncomeFromProduction,
          'totalCutQuantity': totalCutQuantity,
          'totalIncomeFromCuts': totalIncomeFromCuts,
          'totalOtherCount': totalOtherCount,
          'totalIncomeFromOthers': totalIncomeFromOthers,
          'totalExpense': totalExpense,
          'totalPenalties': totalPenalties,
          'totalExtraWork': totalExtraWork,
          'initialBalance': initialBalance,
          'grossIncome': grossIncome,
          'finalBalance': finalBalance,
          'isInDebt': isInDebt,
        },
        type: 'financial_statistics',
        title: 'الإحصائيات المالية - ${widget.workshopName}',
        showIndividual: true,
      ),
    );
  }
}
