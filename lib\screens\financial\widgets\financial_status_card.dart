import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../services/financial_state_manager.dart';

class FinancialStatusCard extends StatefulWidget {
  final int workshopId;
  final VoidCallback? onTap;

  const FinancialStatusCard({
    super.key,
    required this.workshopId,
    this.onTap,
  });

  @override
  State<FinancialStatusCard> createState() => _FinancialStatusCardState();
}

class _FinancialStatusCardState extends State<FinancialStatusCard>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<Color?> _colorAnimation;

  final FinancialStateManager _financialManager = FinancialStateManager();

  @override
  void initState() {
    super.initState();
    
    // إعداد الأنيميشن
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    // تحديد المعمل الحالي
    _financialManager.setCurrentWorkshop(widget.workshopId);
    
    // الاستماع للتحديثات
    _financialManager.addListener(_onFinancialUpdate);
  }

  void _onFinancialUpdate() {
    if (mounted) {
      setState(() {
        // تحديث الألوان حسب الحالة المالية
        _colorAnimation = ColorTween(
          begin: _financialManager.isInDebt ? Colors.red.shade100 : Colors.green.shade100,
          end: _financialManager.isInDebt ? Colors.red.shade200 : Colors.green.shade200,
        ).animate(_animationController);
        
        // تشغيل الأنيميشن عند التحديث
        _animationController.forward().then((_) {
          _animationController.reverse();
        });
      });
    }
  }

  @override
  void dispose() {
    _financialManager.removeListener(_onFinancialUpdate);
    _animationController.dispose();
    super.dispose();
  }

  String _formatAmount(double amount) {
    if (amount == amount.roundToDouble()) {
      return amount.toInt().toString();
    }
    return amount.toStringAsFixed(0);
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<Map<String, dynamic>>(
      stream: _financialManager.financialDataStream,
      builder: (context, snapshot) {
        final isInDebt = _financialManager.isInDebt;
        final finalBalance = _financialManager.finalBalance;
        final amount = finalBalance.abs();

        return AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return Transform.scale(
              scale: _scaleAnimation.value,
              child: GestureDetector(
                onTap: () {
                  _animationController.forward().then((_) {
                    _animationController.reverse();
                  });
                  widget.onTap?.call();
                },
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: isInDebt 
                        ? [Colors.red.shade100, Colors.red.shade50]
                        : [Colors.green.shade100, Colors.green.shade50],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: (isInDebt ? Colors.red : Colors.green).withOpacity(0.3),
                        blurRadius: 15,
                        offset: const Offset(0, 5),
                        spreadRadius: 2,
                      ),
                    ],
                    border: Border.all(
                      color: (isInDebt ? Colors.red : Colors.green).withOpacity(0.3),
                      width: 2,
                    ),
                  ),
                  child: Column(
                    children: [
                      // الأيقونة والحالة
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: isInDebt ? Colors.red.shade600 : Colors.green.shade600,
                              shape: BoxShape.circle,
                              boxShadow: [
                                BoxShadow(
                                  color: (isInDebt ? Colors.red : Colors.green).withOpacity(0.4),
                                  blurRadius: 8,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Icon(
                              isInDebt ? Icons.warning_amber_rounded : Icons.account_balance_wallet_rounded,
                              size: 32,
                              color: Colors.white,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  isInDebt ? 'لديك مديونية ⚠️' : 'رصيدك موجب 💰',
                                  style: GoogleFonts.cairo(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: isInDebt ? Colors.red.shade700 : Colors.green.shade700,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  isInDebt 
                                    ? 'تحتاج لتسديد المديونية' 
                                    : 'وضعك المالي ممتاز',
                                  style: GoogleFonts.cairo(
                                    fontSize: 12,
                                    color: Colors.grey.shade600,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // المبلغ
                      Container(
                        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 20),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.8),
                          borderRadius: BorderRadius.circular(15),
                          border: Border.all(
                            color: (isInDebt ? Colors.red : Colors.green).withOpacity(0.2),
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              isInDebt ? '-' : '+',
                              style: GoogleFonts.cairo(
                                fontSize: 28,
                                fontWeight: FontWeight.bold,
                                color: isInDebt ? Colors.red.shade800 : Colors.green.shade800,
                              ),
                            ),
                            Text(
                              _formatAmount(amount),
                              style: GoogleFonts.cairo(
                                fontSize: 28,
                                fontWeight: FontWeight.bold,
                                color: isInDebt ? Colors.red.shade800 : Colors.green.shade800,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'ريال',
                              style: GoogleFonts.cairo(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: Colors.grey.shade600,
                              ),
                            ),
                          ],
                        ),
                      ),
                      
                      const SizedBox(height: 12),
                      
                      // معلومات إضافية
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          _buildQuickInfo(
                            'غرامات',
                            _formatAmount(_financialManager.totalPenalties),
                            Icons.warning,
                            Colors.red.shade600,
                          ),
                          Container(
                            width: 1,
                            height: 30,
                            color: Colors.grey.shade300,
                          ),
                          _buildQuickInfo(
                            'أعمال إضافية',
                            _formatAmount(_financialManager.totalExtraWork),
                            Icons.add_business,
                            Colors.blue.shade600,
                          ),
                          Container(
                            width: 1,
                            height: 30,
                            color: Colors.grey.shade300,
                          ),
                          _buildQuickInfo(
                            'رصيد مرحل',
                            _formatAmount(_financialManager.initialBalance),
                            Icons.account_balance,
                            Colors.purple.shade600,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildQuickInfo(String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, size: 16, color: color),
        const SizedBox(height: 4),
        Text(
          value,
          style: GoogleFonts.cairo(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: Colors.grey.shade800,
          ),
        ),
        Text(
          label,
          style: GoogleFonts.cairo(
            fontSize: 10,
            color: Colors.grey.shade600,
          ),
        ),
      ],
    );
  }
}
