// ملف اختبار شامل لجميع الصفحات المصلحة
import 'package:flutter/material.dart';
import 'screens/financial/financial_main_screen.dart';
import 'screens/financial/enhanced_statistics_screen.dart';
import 'screens/financial/enhanced_initial_balance_screen.dart';
import 'screens/financial/enhanced_monthly_settlement_screen.dart';

class TestAllPagesFixed extends StatelessWidget {
  const TestAllPagesFixed({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'اختبار جميع الصفحات المصلحة',
      theme: ThemeData(
        primarySwatch: Colors.green,
        fontFamily: 'Cairo',
      ),
      home: const TestAllPagesScreen(),
    );
  }
}

class TestAllPagesScreen extends StatelessWidget {
  const TestAllPagesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('جميع الصفحات مصلحة'),
        backgroundColor: Colors.green[600],
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Icon(
              Icons.verified_user,
              size: 80,
              color: Colors.green,
            ),
            const SizedBox(height: 20),
            const Text(
              'تم إصلاح جميع الصفحات!',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.green,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            const Text(
              'الصفحات المصلحة:',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            
            // كارد الصفحة الرئيسية
            _buildPageCard(
              'الصفحة الرئيسية للنظام المالي',
              'SingleChildScrollView + هيدر مرن',
              Icons.account_balance,
              Colors.indigo,
              () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const FinancialMainScreen(
                    tailorId: 1,
                    tailorName: 'خياط تجريبي - مصلح',
                  ),
                ),
              ),
            ),
            
            const SizedBox(height: 12),
            
            // كارد صفحة الإحصائيات
            _buildPageCard(
              'صفحة الإحصائيات المالية',
              'SingleChildScrollView + هيدر مرن',
              Icons.analytics,
              Colors.teal,
              () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const EnhancedStatisticsScreen(
                    workshopId: 1,
                    workshopName: 'معمل تجريبي - مصلح',
                  ),
                ),
              ),
            ),
            
            const SizedBox(height: 12),
            
            // كارد صفحة الرصيد المرحل
            _buildPageCard(
              'صفحة الرصيد المرحل',
              'SingleChildScrollView + هيدر مرن',
              Icons.account_balance_wallet,
              Colors.cyan,
              () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const EnhancedInitialBalanceScreen(
                    tailorId: 1,
                    tailorName: 'خياط تجريبي - مصلح',
                  ),
                ),
              ),
            ),
            
            const SizedBox(height: 12),
            
            // كارد صفحة التصفية الشهرية
            _buildPageCard(
              'صفحة التصفية الشهرية',
              'SingleChildScrollView + هيدر مرن',
              Icons.receipt_long,
              Colors.purple,
              () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const EnhancedMonthlySettlementScreen(
                    tailorId: 1,
                    tailorName: 'خياط تجريبي - مصلح',
                  ),
                ),
              ),
            ),
            
            const SizedBox(height: 30),
            
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.green[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.green[200]!),
              ),
              child: const Column(
                children: [
                  Icon(Icons.check_circle, color: Colors.green, size: 32),
                  SizedBox(height: 8),
                  Text(
                    'جميع الصفحات مصلحة ومستقرة',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    'لا مزيد من مشاكل Overflow في أي صفحة',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.green,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPageCard(String title, String description, IconData icon, Color color, VoidCallback onTap) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: color.withOpacity(0.3)),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(icon, color: color, size: 24),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              Icon(Icons.arrow_forward_ios, color: color, size: 16),
            ],
          ),
        ),
      ),
    );
  }
}

void main() {
  runApp(const TestAllPagesFixed());
}
