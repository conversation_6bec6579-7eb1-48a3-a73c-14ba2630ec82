// lib/screens/financial/extra_work_screen.dart

import 'package:flutter/material.dart';
import '../../models/db_helper.dart';
import '../../models/financial_models.dart';
import '../../measurements/measurement_styles.dart';
import '../../utils/number_utils.dart';
import '../../services/financial_state_manager.dart';
import 'widgets/financial_status_card.dart';

class ExtraWorkScreen extends StatefulWidget {
  final int tailorId;
  final String tailorName;

  const ExtraWorkScreen({
    super.key,
    required this.tailorId,
    required this.tailorName,
  });

  @override
  State<ExtraWorkScreen> createState() => _ExtraWorkScreenState();
}

class _ExtraWorkScreenState extends State<ExtraWorkScreen> {
  List<ExtraWork> _extraWorks = [];
  bool _isLoading = false;
  final FinancialStateManager _financialManager = FinancialStateManager();

  @override
  void initState() {
    super.initState();
    _loadExtraWorks();
    // تهيئة مدير الحالة المالية
    _financialManager.setCurrentWorkshop(widget.tailorId);
  }

  Future<void> _loadExtraWorks() async {
    setState(() => _isLoading = true);

    try {
      final extraWorksData = await DBHelper.getExtraWorkByTailorId(widget.tailorId);
      _extraWorks = extraWorksData.map((data) => ExtraWork.fromMap(data)).toList();
    } catch (e) {
      _showErrorSnackBar('خطأ في تحميل البيانات: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _addExtraWork() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddEditExtraWorkScreen(
          tailorId: widget.tailorId,
          tailorName: widget.tailorName,
        ),
      ),
    );

    if (result == true) {
      _loadExtraWorks();
      // تحديث فوري للحالة المالية
      await _financialManager.refresh();

      // إظهار إشعار فوري بالحالة المالية
      if (result is Map && result['amount'] != null) {
        _showFinancialUpdateSnackBar('تم إضافة عمل إضافي', result['amount']);
      } else {
        _showFinancialUpdateSnackBar('تم إضافة عمل إضافي', 0.0);
      }
    }
  }

  Future<void> _editExtraWork(ExtraWork extraWork) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddEditExtraWorkScreen(
          tailorId: widget.tailorId,
          tailorName: widget.tailorName,
          extraWork: extraWork,
        ),
      ),
    );

    if (result == true) {
      _loadExtraWorks();
      // تحديث فوري للحالة المالية
      await _financialManager.refresh();

      // إظهار إشعار فوري بالحالة المالية
      _showFinancialUpdateSnackBar('تم تعديل عمل إضافي', extraWork.extraWorkAmount);
    }
  }

  Future<void> _deleteExtraWork(ExtraWork extraWork) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف العمل الإضافي "${extraWork.extraWorkDescription}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: MeasurementStyles.errorColor,
              foregroundColor: MeasurementStyles.whiteColor,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await DBHelper.deleteExtraWork(extraWork.id!);
        _showSuccessSnackBar('تم حذف العمل الإضافي بنجاح');
        _loadExtraWorks();
        // تحديث فوري للحالة المالية
        await _financialManager.refresh();

        // إظهار إشعار فوري بالحالة المالية
        _showFinancialUpdateSnackBar('تم حذف عمل إضافي', extraWork.extraWorkAmount);
      } catch (e) {
        _showErrorSnackBar('خطأ في حذف العمل الإضافي: $e');
      }
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: MeasurementStyles.successColor,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showFinancialUpdateSnackBar(String action, double amount) {
    final summary = _financialManager.getFinancialSummary();
    final statusMessage = summary['isInDebt']
        ? 'أصبحت مديون: ${summary['amount'].toStringAsFixed(0)} ريال ⚠️'
        : 'رصيدك الآن: ${summary['amount'].toStringAsFixed(0)} ريال 💰';

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('$action ${amount.toStringAsFixed(0)} ريال'),
            const SizedBox(height: 4),
            Text(
              statusMessage,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: summary['isInDebt'] ? Colors.red[100] : Colors.green[100],
              ),
            ),
          ],
        ),
        backgroundColor: summary['isInDebt'] ? Colors.red[600] : Colors.green[600],
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 4),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: MeasurementStyles.errorColor,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: MeasurementStyles.backgroundColor,
      appBar: AppBar(
        title: Text(
          'الأعمال الإضافية - ${widget.tailorName}',
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 18,
          ),
        ),
        backgroundColor: MeasurementStyles.primaryColor,
        foregroundColor: MeasurementStyles.whiteColor,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _addExtraWork,
            icon: const Icon(Icons.add_rounded),
            tooltip: 'إضافة عمل إضافي',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // كارد الحالة المالية
                FinancialStatusCard(
                  workshopId: widget.tailorId,
                  onTap: () {
                    // يمكن إضافة عملية الانتقال لصفحة الإحصائيات
                  },
                ),

                // ملخص الأعمال الإضافية
                _buildExtraWorkSummary(),

                // قائمة الأعمال الإضافية
                Expanded(
                  child: _extraWorks.isEmpty
                      ? _buildEmptyState()
                      : ListView.builder(
                          padding: const EdgeInsets.all(16),
                          itemCount: _extraWorks.length,
                          itemBuilder: (context, index) {
                            return _buildExtraWorkCard(_extraWorks[index]);
                          },
                        ),
                ),
              ],
            ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _addExtraWork,
        backgroundColor: MeasurementStyles.successColor,
        foregroundColor: MeasurementStyles.whiteColor,
        icon: const Icon(Icons.work_rounded),
        label: const Text('عمل إضافي'),
      ),
    );
  }

  Widget _buildExtraWorkSummary() {
    final totalExtraWork = _extraWorks.fold(0.0, (sum, work) => sum + work.extraWorkAmount);

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: MeasurementStyles.successColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: MeasurementStyles.successColor.withOpacity(0.3),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: MeasurementStyles.successColor,
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.work_rounded,
              color: MeasurementStyles.whiteColor,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'إجمالي الأعمال الإضافية',
                  style: MeasurementStyles.cardSubtitleStyle,
                ),
                Text(
                  '${totalExtraWork.toStringAsFixed(2)} ريال',
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w700,
                    color: MeasurementStyles.successColor,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: MeasurementStyles.successColor,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              '${_extraWorks.length} عمل',
              style: const TextStyle(
                color: MeasurementStyles.whiteColor,
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.work_off_rounded,
            size: 80,
            color: MeasurementStyles.textSecondaryColor.withOpacity(0.5),
          ),
          const SizedBox(height: 16),
          const Text(
            'لا توجد أعمال إضافية',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: MeasurementStyles.textSecondaryColor,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'لم يتم تسجيل أي أعمال إضافية لهذا الخياط',
            style: MeasurementStyles.cardSubtitleStyle,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildExtraWorkCard(ExtraWork extraWork) {
    return MeasurementStyles.modernCard(
      margin: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // رأس البطاقة
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: MeasurementStyles.successColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.work_rounded,
                  color: MeasurementStyles.successColor,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      extraWork.extraWorkDescription,
                      style: MeasurementStyles.cardTitleStyle,
                    ),
                    Text(
                      extraWork.formattedDate,
                      style: MeasurementStyles.cardSubtitleStyle,
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: MeasurementStyles.successColor,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${NumberUtils.formatIntegerDisplay(extraWork.extraWorkAmount)} ريال',
                  style: const TextStyle(
                    color: MeasurementStyles.whiteColor,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // أزرار التحكم
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _editExtraWork(extraWork),
                  icon: const Icon(Icons.edit_rounded, size: 18),
                  label: const Text('تعديل'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: MeasurementStyles.primaryColor,
                    foregroundColor: MeasurementStyles.whiteColor,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _deleteExtraWork(extraWork),
                  icon: const Icon(Icons.delete_rounded, size: 18),
                  label: const Text('حذف'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: MeasurementStyles.errorColor,
                    foregroundColor: MeasurementStyles.whiteColor,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

// شاشة إضافة/تعديل العمل الإضافي
class AddEditExtraWorkScreen extends StatefulWidget {
  final int tailorId;
  final String tailorName;
  final ExtraWork? extraWork;

  const AddEditExtraWorkScreen({
    super.key,
    required this.tailorId,
    required this.tailorName,
    this.extraWork,
  });

  @override
  State<AddEditExtraWorkScreen> createState() => _AddEditExtraWorkScreenState();
}

class _AddEditExtraWorkScreenState extends State<AddEditExtraWorkScreen> {
  final _formKey = GlobalKey<FormState>();
  final _descriptionController = TextEditingController();
  final _amountController = TextEditingController();

  DateTime _selectedDate = DateTime.now();
  bool _isLoading = false;

  // قائمة بأمثلة الأعمال الإضافية الشائعة
  final List<String> _commonExtraWorks = [
    'تعديلات ملابس خارجية',
    'خياطة طارئة',
    'عمل إضافي في العطلة',
    'تصليح ملابس',
    'تفصيل قطعة خاصة',
    'عمل ساعات إضافية',
  ];

  @override
  void initState() {
    super.initState();
    if (widget.extraWork != null) {
      _descriptionController.text = widget.extraWork!.extraWorkDescription;
      _amountController.text = NumberUtils.formatForInput(widget.extraWork!.extraWorkAmount);
      _selectedDate = widget.extraWork!.dateTime;
    }
  }

  @override
  void dispose() {
    _descriptionController.dispose();
    _amountController.dispose();
    super.dispose();
  }

  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );

    if (date != null) {
      setState(() => _selectedDate = date);
    }
  }

  void _selectCommonWork(String work) {
    _descriptionController.text = work;
  }

  Future<void> _saveExtraWork() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final extraWorkData = {
        'tailorId': widget.tailorId,
        'extraWorkDescription': _descriptionController.text.trim(),
        'extraWorkAmount': NumberUtils.parseDouble(_amountController.text),
        'extraWorkDate': _selectedDate.toIso8601String(),
      };

      if (widget.extraWork != null) {
        // تحديث العمل الإضافي الموجود
        await DBHelper.updateExtraWork(widget.extraWork!.id!, extraWorkData);
        _showSuccessSnackBar('تم تحديث العمل الإضافي بنجاح');
      } else {
        // إضافة عمل إضافي جديد
        await DBHelper.insertExtraWork(extraWorkData);
        _showSuccessSnackBar('تم إضافة العمل الإضافي بنجاح');
      }

      Navigator.pop(context, true);
    } catch (e) {
      _showErrorSnackBar('خطأ في حفظ البيانات: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: MeasurementStyles.successColor,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: MeasurementStyles.errorColor,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isEdit = widget.extraWork != null;

    return Scaffold(
      backgroundColor: MeasurementStyles.backgroundColor,
      appBar: AppBar(
        title: Text(
          '${isEdit ? 'تعديل' : 'إضافة'} عمل إضافي - ${widget.tailorName}',
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 18,
          ),
        ),
        backgroundColor: MeasurementStyles.primaryColor,
        foregroundColor: MeasurementStyles.whiteColor,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              // نموذج الإدخال
              MeasurementStyles.modernCard(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    MeasurementStyles.sectionTitle(
                      'بيانات العمل الإضافي',
                      icon: Icons.work_rounded,
                    ),

                    const SizedBox(height: 16),

                    // وصف العمل الإضافي
                    TextFormField(
                      controller: _descriptionController,
                      keyboardType: TextInputType.text,
                      textAlign: TextAlign.right,
                      style: MeasurementStyles.normalTextStyle,
                      decoration: MeasurementStyles.inputDecoration(
                        'وصف العمل الإضافي',
                        icon: Icons.description_rounded,
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'يرجى إدخال وصف العمل الإضافي';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 16),

                    // مبلغ العمل الإضافي
                    TextFormField(
                      controller: _amountController,
                      keyboardType: TextInputType.number,
                      inputFormatters: NumberUtils.integerInputFormatters,
                      textAlign: TextAlign.center,
                      style: MeasurementStyles.normalTextStyle,
                      decoration: MeasurementStyles.inputDecoration(
                        'مبلغ العمل الإضافي (ريال)',
                        icon: Icons.attach_money_rounded,
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'يرجى إدخال مبلغ العمل الإضافي';
                        }
                        final amount = NumberUtils.parseInteger(value);
                        if (amount <= 0) {
                          return 'يرجى إدخال مبلغ صحيح';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 16),

                    // تاريخ العمل الإضافي
                    InkWell(
                      onTap: _selectDate,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                        decoration: BoxDecoration(
                          color: MeasurementStyles.surfaceColor,
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(color: const Color(0xFFE2E8F0)),
                        ),
                        child: Row(
                          children: [
                            const Icon(
                              Icons.calendar_today_rounded,
                              color: MeasurementStyles.textSecondaryColor,
                              size: 20,
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Text(
                                    'تاريخ العمل الإضافي',
                                    style: TextStyle(
                                      color: MeasurementStyles.textSecondaryColor,
                                      fontSize: 14,
                                    ),
                                  ),
                                  Text(
                                    '${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}',
                                    style: MeasurementStyles.normalTextStyle,
                                  ),
                                ],
                              ),
                            ),
                            const Icon(
                              Icons.arrow_drop_down_rounded,
                              color: MeasurementStyles.textSecondaryColor,
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 20),

                    // زر الحفظ
                    MeasurementStyles.saveButton(
                      isEdit ? 'تحديث العمل الإضافي' : 'إضافة العمل الإضافي',
                      _saveExtraWork,
                      isLoading: _isLoading,
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // أمثلة الأعمال الإضافية الشائعة
              MeasurementStyles.modernCard(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    MeasurementStyles.sectionTitle(
                      'أمثلة شائعة',
                      icon: Icons.lightbulb_outline_rounded,
                    ),
                    const SizedBox(height: 12),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: _commonExtraWorks.map((work) {
                        return InkWell(
                          onTap: () => _selectCommonWork(work),
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                            decoration: BoxDecoration(
                              color: MeasurementStyles.secondaryColor,
                              borderRadius: BorderRadius.circular(20),
                              border: Border.all(
                                color: MeasurementStyles.primaryColor.withOpacity(0.2),
                              ),
                            ),
                            child: Text(
                              work,
                              style: const TextStyle(
                                color: MeasurementStyles.primaryColor,
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
