import 'package:flutter/material.dart';
import '../../models/db_helper.dart';
import '../../measurements/measurement_styles.dart';
import '../measurement_detail_options_screen.dart';
import '../../utils/date_utils.dart';

class AddManagerMeasurementScreen extends StatefulWidget {
  final String measurementType; // 'clothing', 'suits', 'women'
  final Map<String, dynamic>? measurementToEdit;

  const AddManagerMeasurementScreen({
    super.key,
    required this.measurementType,
    this.measurementToEdit,
  });

  @override
  State<AddManagerMeasurementScreen> createState() => _AddManagerMeasurementScreenState();
}

class _AddManagerMeasurementScreenState extends State<AddManagerMeasurementScreen> {
  final _formKey = GlobalKey<FormState>();

  // Controllers للحقول الأساسية
  final _billNumberController = TextEditingController();
  final _customerNameController = TextEditingController();
  final _phoneNumberController = TextEditingController();
  final _fabricTypeController = TextEditingController();
  final _quantityController = TextEditingController();
  final _receivedDateController = TextEditingController();
  final _deliveryDateController = TextEditingController();
  final _priceController = TextEditingController();
  final _paidController = TextEditingController();
  final _notesController = TextEditingController();

  // Controllers للمقاسات - الثياب
  final Map<String, TextEditingController> _clothingMeasurements = {
    'height': TextEditingController(),
    'shoulder': TextEditingController(),
    'sleeveLength': TextEditingController(),
    'chest': TextEditingController(),
    'neck': TextEditingController(),
    'handDrop': TextEditingController(),
    'middleHand': TextEditingController(),
    'cuffLength': TextEditingController(),
    'cuffHeight': TextEditingController(),
    'step': TextEditingController(),
  };

  // Controllers للمقاسات - البدلات
  final Map<String, TextEditingController> _suitMeasurements = {
    // قياسات القميص
    'shirtHeight': TextEditingController(),
    'shirtShoulder': TextEditingController(),
    'shirtSleeveLength': TextEditingController(),
    'shirtChest': TextEditingController(),
    'shirtStomach': TextEditingController(),
    'shirtNeck': TextEditingController(),
    'shirtHandDrop': TextEditingController(),
    'shirtCuffLength': TextEditingController(),
    'shirtCuffWidth': TextEditingController(),
    // قياسات البنطلون
    'pantsHeight': TextEditingController(),
    'pantsBelt': TextEditingController(),
    'pantsHip': TextEditingController(),
    'pantsThigh': TextEditingController(),
    'pantsKnee': TextEditingController(),
    'pantsOpening': TextEditingController(),
    // قياسات الكوت
    'coatHeight': TextEditingController(),
    'coatShoulder': TextEditingController(),
    'coatHandLength': TextEditingController(),
    'coatChest': TextEditingController(),
    'coatStomach': TextEditingController(),
    'coatHandWidth': TextEditingController(),
    'coatMiddleHand': TextEditingController(),
    'coatHandOpening': TextEditingController(),
  };

  // Controllers للمقاسات - النسائية
  final Map<String, TextEditingController> _womenMeasurements = {
    'height': TextEditingController(),
    'shoulder': TextEditingController(),
    'sleeve': TextEditingController(),
    'chest': TextEditingController(),
    'waist': TextEditingController(),
    'hip': TextEditingController(),
    'dressLength': TextEditingController(),
    'neckOpening': TextEditingController(),
    'sleeveWidth': TextEditingController(),
    'sleeveLengthFromShoulder': TextEditingController(),
    'armCircumference': TextEditingController(),
    'legOpening': TextEditingController(),
  };

  // خيارات للثياب
  String? _neckType;
  String? _zipperType;
  String? _handType;
  String? _pocketType;
  String? _styleType;
  String? _buttonType;
  String? _cuffType;

  // قوائم الخيارات من قاعدة البيانات
  Map<String, List<String>> _detailOptions = {
    'neck': [],
    'zipper': [],
    'hand': [],
    'pocket': [],
    'style': [],
    'button': [],
    'cuff': [],
  };

  bool _isLoading = false;
  bool _isEdit = false;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  Future<void> _initializeData() async {
    await _loadDetailOptions();
    if (widget.measurementToEdit != null) {
      _isEdit = true;
      _loadMeasurementData();
    }
  }

  Future<void> _loadDetailOptions() async {
    try {
      final options = await DBHelper.getAllDetailOptions();
      setState(() {
        _detailOptions = options;
      });
    } catch (e) {
      // في حالة الخطأ، استخدم قيم افتراضية
      setState(() {
        _detailOptions = {
          'neck': ['عادية', 'مفتوحة', 'مغلقة', 'بياقة'],
          'zipper': ['بدون', 'عادي', 'مخفي', 'جانبي'],
          'hand': ['عادية', 'ضيقة', 'واسعة', 'مطاطية'],
          'pocket': ['بدون', 'جيب واحد', 'جيبين', 'ثلاثة جيوب'],
          'style': ['عادي', 'رسمي', 'رياضي', 'عصري'],
          'button': ['بدون', 'زر عادي', 'زر معدني', 'زر خشبي'],
          'cuff': ['بدون', 'كبك عادي', 'كبك فرنسي', 'كبك مربع'],
        };
      });
    }
  }

  void _loadMeasurementData() {
    final measurement = widget.measurementToEdit!;

    // تحميل البيانات الأساسية
    _billNumberController.text = measurement['billNumber'] ?? '';
    _customerNameController.text = measurement['customerName'] ?? '';
    _phoneNumberController.text = measurement['phoneNumber'] ?? '';
    _fabricTypeController.text = measurement['fabricType'] ?? '';
    _quantityController.text = (measurement['quantity'] ?? '').toString();
    _receivedDateController.text = measurement['receivedDate'] ?? '';
    _deliveryDateController.text = measurement['deliveryDate'] ?? '';
    _priceController.text = (measurement['price'] ?? '').toString();
    _paidController.text = (measurement['paid'] ?? '').toString();
    _notesController.text = measurement['notes'] ?? '';

    // تحميل المقاسات حسب النوع
    if (widget.measurementType == 'clothing') {
      for (final entry in _clothingMeasurements.entries) {
        entry.value.text = measurement[entry.key] ?? '';
      }

      // تحميل خيارات التفصيل مع التحقق من وجودها
      _neckType = _validateDetailOption('neck', measurement['neckType']);
      _zipperType = _validateDetailOption('zipper', measurement['zipperType']);
      _handType = _validateDetailOption('hand', measurement['handType']);
      _pocketType = _validateDetailOption('pocket', measurement['pocketType']);
      _styleType = _validateDetailOption('style', measurement['styleType']);
      _buttonType = _validateDetailOption('button', measurement['buttonType']);
      _cuffType = _validateDetailOption('cuff', measurement['cuffType']);
    } else if (widget.measurementType == 'suits') {
      for (final entry in _suitMeasurements.entries) {
        entry.value.text = measurement[entry.key] ?? '';
      }
    } else if (widget.measurementType == 'women') {
      for (final entry in _womenMeasurements.entries) {
        entry.value.text = measurement[entry.key] ?? '';
      }
    }
  }

  // دالة للتحقق من وجود خيار التفصيل
  String? _validateDetailOption(String type, dynamic value) {
    if (value == null) return null;

    final options = _detailOptions[type] ?? [];
    if (options.contains(value.toString())) {
      return value.toString();
    }

    // إذا لم توجد القيمة، أضفها إلى القائمة
    if (value.toString().isNotEmpty) {
      _detailOptions[type] = [...options, value.toString()];
      return value.toString();
    }

    return null;
  }

  @override
  void dispose() {
    _billNumberController.dispose();
    _customerNameController.dispose();
    _phoneNumberController.dispose();
    _fabricTypeController.dispose();
    _quantityController.dispose();
    _receivedDateController.dispose();
    _deliveryDateController.dispose();
    _priceController.dispose();
    _paidController.dispose();
    _notesController.dispose();

    for (final controller in _clothingMeasurements.values) {
      controller.dispose();
    }
    for (final controller in _suitMeasurements.values) {
      controller.dispose();
    }
    for (final controller in _womenMeasurements.values) {
      controller.dispose();
    }

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    String title;
    switch (widget.measurementType) {
      case 'clothing':
        title = _isEdit ? 'تعديل مقاس ثوب' : 'إضافة مقاس ثوب';
        break;
      case 'suits':
        title = _isEdit ? 'تعديل مقاس بدلة' : 'إضافة مقاس بدلة';
        break;
      case 'women':
        title = _isEdit ? 'تعديل مقاس نسائي' : 'إضافة مقاس نسائي';
        break;
      default:
        title = _isEdit ? 'تعديل مقاس' : 'إضافة مقاس';
    }

    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: MeasurementStyles.backgroundColor,
        appBar: AppBar(
          title: Text(
            title,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          backgroundColor: Colors.purple[700],
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.white),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ),
        body: Form(
          key: _formKey,
          child: Stack(
            children: [
              ListView(
                padding: const EdgeInsets.all(16),
                children: [
                  // بطاقة المعلومات الأساسية
                  _buildBasicInfoCard(),

                  const SizedBox(height: 16),

                  // بطاقة المعلومات المالية
                  _buildFinancialInfoCard(),

                  const SizedBox(height: 16),

                  // بطاقة المقاسات
                  _buildMeasurementsCard(),

                  const SizedBox(height: 16),

                  // بطاقة الملاحظات والتواريخ
                  _buildNotesAndDatesCard(),

                  const SizedBox(height: 24),

                  // زر الحفظ
                  _buildSaveButton(),

                  const SizedBox(height: 100), // مساحة إضافية للتمرير
                ],
              ),

              if (_isLoading)
                Container(
                  color: Colors.black.withOpacity(0.3),
                  child: const Center(
                    child: CircularProgressIndicator(),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBasicInfoCard() {
    return MeasurementStyles.modernCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          MeasurementStyles.sectionTitle('المعلومات الأساسية'),
          const SizedBox(height: 16),

          // رقم الفاتورة المميز
          MeasurementStyles.specialInvoiceField(_billNumberController),

          const SizedBox(height: 16),

          // اسم العميل
          MeasurementStyles.buildInput('اسم العميل', _customerNameController),

          const SizedBox(height: 16),

          // رقم الهاتف ونوع القماش
          MeasurementStyles.buildDoubleInput(
            'رقم الهاتف',
            'نوع القماش',
            _phoneNumberController,
            _fabricTypeController,
          ),

          const SizedBox(height: 16),

          // الكمية
          MeasurementStyles.buildInput('الكمية', _quantityController),
        ],
      ),
    );
  }

  Widget _buildFinancialInfoCard() {
    return MeasurementStyles.modernCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          MeasurementStyles.sectionTitle('المعلومات المالية'),
          const SizedBox(height: 16),

          // المبلغ والمدفوع
          MeasurementStyles.buildDoubleInput(
            'المبلغ الإجمالي',
            'المبلغ المدفوع',
            _priceController,
            _paidController,
          ),

          const SizedBox(height: 16),

          // عرض المتبقي
          _buildRemainingAmount(),
        ],
      ),
    );
  }

  Widget _buildRemainingAmount() {
    return ValueListenableBuilder(
      valueListenable: _priceController,
      child: ValueListenableBuilder(
        valueListenable: _paidController,
        builder: (context, paidValue, child) {
          final price = double.tryParse(_priceController.text) ?? 0;
          final paid = double.tryParse(_paidController.text) ?? 0;
          final remaining = price - paid;

          return Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: remaining > 0 ? Colors.orange[50] : Colors.green[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: remaining > 0 ? Colors.orange[200]! : Colors.green[200]!,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  remaining > 0 ? Icons.warning : Icons.check_circle,
                  color: remaining > 0 ? Colors.orange[700] : Colors.green[700],
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'المبلغ المتبقي: ${remaining.toStringAsFixed(2)} ريال',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: remaining > 0 ? Colors.orange[700] : Colors.green[700],
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
      builder: (context, priceValue, child) => child!,
    );
  }

  Widget _buildMeasurementsCard() {
    return MeasurementStyles.modernCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          MeasurementStyles.sectionTitle('المقاسات'),
          const SizedBox(height: 16),

          if (widget.measurementType == 'clothing') _buildClothingMeasurements(),
          if (widget.measurementType == 'suits') _buildSuitMeasurements(),
          if (widget.measurementType == 'women') _buildWomenMeasurements(),
        ],
      ),
    );
  }

  Widget _buildClothingMeasurements() {
    return Column(
      children: [
        // المقاسات الأساسية
        MeasurementStyles.buildDoubleInput(
          'الطول',
          'الكتف',
          _clothingMeasurements['height']!,
          _clothingMeasurements['shoulder']!,
        ),
        const SizedBox(height: 16),

        MeasurementStyles.buildDoubleInput(
          'طول الكم',
          'الصدر',
          _clothingMeasurements['sleeveLength']!,
          _clothingMeasurements['chest']!,
        ),
        const SizedBox(height: 16),

        MeasurementStyles.buildDoubleInput(
          'الرقبة',
          'نزول اليد',
          _clothingMeasurements['neck']!,
          _clothingMeasurements['handDrop']!,
        ),
        const SizedBox(height: 16),

        MeasurementStyles.buildDoubleInput(
          'وسط اليد',
          'طول الكف',
          _clothingMeasurements['middleHand']!,
          _clothingMeasurements['cuffLength']!,
        ),
        const SizedBox(height: 16),

        MeasurementStyles.buildDoubleInput(
          'ارتفاع الكف',
          'الخطوة',
          _clothingMeasurements['cuffHeight']!,
          _clothingMeasurements['step']!,
        ),

        const SizedBox(height: 20),

        // الخيارات
        MeasurementStyles.sectionTitle('خيارات التفصيل'),
        const SizedBox(height: 16),

        _buildDetailDropdown('نوع الرقبة', 'neck', _neckType, (value) => setState(() => _neckType = value)),

        _buildDetailDropdown('نوع السحاب', 'zipper', _zipperType, (value) => setState(() => _zipperType = value)),

        _buildDetailDropdown('نوع اليد', 'hand', _handType, (value) => setState(() => _handType = value)),

        _buildDetailDropdown('نوع الجيوب', 'pocket', _pocketType, (value) => setState(() => _pocketType = value)),

        _buildDetailDropdown('نوع التفصيل', 'style', _styleType, (value) => setState(() => _styleType = value)),

        _buildDetailDropdown('نوع الأزرار', 'button', _buttonType, (value) => setState(() => _buttonType = value)),

        _buildDetailDropdown('نوع الكبك', 'cuff', _cuffType, (value) => setState(() => _cuffType = value)),
      ],
    );
  }

  Widget _buildSuitMeasurements() {
    return Column(
      children: [
        // قياسات القميص
        MeasurementStyles.sectionTitle('قياسات القميص'),
        const SizedBox(height: 16),

        MeasurementStyles.buildDoubleInput(
          'طول القميص',
          'كتف القميص',
          _suitMeasurements['shirtHeight']!,
          _suitMeasurements['shirtShoulder']!,
        ),
        const SizedBox(height: 16),

        MeasurementStyles.buildDoubleInput(
          'كم القميص',
          'صدر القميص',
          _suitMeasurements['shirtSleeveLength']!,
          _suitMeasurements['shirtChest']!,
        ),
        const SizedBox(height: 16),

        MeasurementStyles.buildDoubleInput(
          'بطن القميص',
          'رقبة القميص',
          _suitMeasurements['shirtStomach']!,
          _suitMeasurements['shirtNeck']!,
        ),
        const SizedBox(height: 16),

        MeasurementStyles.buildDoubleInput(
          'نزول يد القميص',
          'طول كف القميص',
          _suitMeasurements['shirtHandDrop']!,
          _suitMeasurements['shirtCuffLength']!,
        ),
        const SizedBox(height: 16),

        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 50),
          child: MeasurementStyles.buildInput('عرض كف القميص', _suitMeasurements['shirtCuffWidth']!),
        ),

        const SizedBox(height: 20),

        // قياسات البنطلون
        MeasurementStyles.sectionTitle('قياسات البنطلون'),
        const SizedBox(height: 16),

        MeasurementStyles.buildDoubleInput(
          'طول البنطلون',
          'حزام البنطلون',
          _suitMeasurements['pantsHeight']!,
          _suitMeasurements['pantsBelt']!,
        ),
        const SizedBox(height: 16),

        MeasurementStyles.buildDoubleInput(
          'ورك البنطلون',
          'فخذ البنطلون',
          _suitMeasurements['pantsHip']!,
          _suitMeasurements['pantsThigh']!,
        ),
        const SizedBox(height: 16),

        MeasurementStyles.buildDoubleInput(
          'ركبة البنطلون',
          'فتحة البنطلون',
          _suitMeasurements['pantsKnee']!,
          _suitMeasurements['pantsOpening']!,
        ),

        const SizedBox(height: 20),

        // قياسات الكوت
        MeasurementStyles.sectionTitle('قياسات الكوت'),
        const SizedBox(height: 16),

        MeasurementStyles.buildDoubleInput(
          'طول الكوت',
          'كتف الكوت',
          _suitMeasurements['coatHeight']!,
          _suitMeasurements['coatShoulder']!,
        ),
        const SizedBox(height: 16),

        MeasurementStyles.buildDoubleInput(
          'طول يد الكوت',
          'صدر الكوت',
          _suitMeasurements['coatHandLength']!,
          _suitMeasurements['coatChest']!,
        ),
        const SizedBox(height: 16),

        MeasurementStyles.buildDoubleInput(
          'بطن الكوت',
          'عرض يد الكوت',
          _suitMeasurements['coatStomach']!,
          _suitMeasurements['coatHandWidth']!,
        ),
        const SizedBox(height: 16),

        MeasurementStyles.buildDoubleInput(
          'وسط يد الكوت',
          'فتحة يد الكوت',
          _suitMeasurements['coatMiddleHand']!,
          _suitMeasurements['coatHandOpening']!,
        ),
      ],
    );
  }

  Widget _buildWomenMeasurements() {
    return Column(
      children: [
        MeasurementStyles.buildDoubleInput(
          'الطول',
          'الكتف',
          _womenMeasurements['height']!,
          _womenMeasurements['shoulder']!,
        ),
        const SizedBox(height: 16),

        MeasurementStyles.buildDoubleInput(
          'الكم',
          'الصدر',
          _womenMeasurements['sleeve']!,
          _womenMeasurements['chest']!,
        ),
        const SizedBox(height: 16),

        MeasurementStyles.buildDoubleInput(
          'الوسط',
          'الورك',
          _womenMeasurements['waist']!,
          _womenMeasurements['hip']!,
        ),
        const SizedBox(height: 16),

        MeasurementStyles.buildDoubleInput(
          'طول الثوب',
          'فتحة الرقبة',
          _womenMeasurements['dressLength']!,
          _womenMeasurements['neckOpening']!,
        ),
        const SizedBox(height: 16),

        MeasurementStyles.buildDoubleInput(
          'عرض الكم',
          'طول الكم من الكتف',
          _womenMeasurements['sleeveWidth']!,
          _womenMeasurements['sleeveLengthFromShoulder']!,
        ),
        const SizedBox(height: 16),

        MeasurementStyles.buildDoubleInput(
          'محيط الذراع',
          'الساق فتحه الرجل',
          _womenMeasurements['armCircumference']!,
          _womenMeasurements['legOpening']!,
        ),
      ],
    );
  }

  Widget _buildNotesAndDatesCard() {
    return MeasurementStyles.modernCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          MeasurementStyles.sectionTitle('التواريخ والملاحظات'),
          const SizedBox(height: 16),

          // التواريخ
          MeasurementStyles.buildDoubleInput(
            'تاريخ الاستلام',
            'موعد التسليم',
            _receivedDateController,
            _deliveryDateController,
          ),

          const SizedBox(height: 16),

          // الملاحظات
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: TextFormField(
              controller: _notesController,
              maxLines: 3,
              textAlign: TextAlign.right,
              textDirection: TextDirection.rtl,
              keyboardType: TextInputType.text,
              decoration: MeasurementStyles.inputDecoration('ملاحظات'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailDropdown(String label, String type, String? value, Function(String?) onChanged) {
    final options = _detailOptions[type] ?? [];

    // التأكد من وجود خيارات
    if (options.isEmpty) {
      // إضافة خيار افتراضي إذا لم توجد خيارات
      _detailOptions[type] = ['بدون تحديد'];
    }

    // التحقق من صحة القيمة
    String? validValue = value;
    if (value != null && value.isNotEmpty && !_detailOptions[type]!.contains(value)) {
      // إضافة القيمة إلى القائمة إذا لم تكن موجودة
      _detailOptions[type] = [..._detailOptions[type]!, value];
    } else if (value != null && value.isEmpty) {
      validValue = null;
    }

    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: MeasurementStyles.buildDropdown<String>(
                label: label,
                value: validValue,
                items: _detailOptions[type]!,
                onChanged: onChanged,
              ),
            ),
            const SizedBox(width: 8),
            IconButton(
              onPressed: () => _openDetailOptionsScreen(),
              icon: Icon(Icons.settings, color: Colors.purple[600]),
              tooltip: 'إدارة الخيارات',
            ),
          ],
        ),
        const SizedBox(height: 16),
      ],
    );
  }

  Future<void> _openDetailOptionsScreen() async {
    await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => const MeasurementDetailOptionsScreen(),
      ),
    );

    // تحديث فوري للخيارات عند العودة
    await _loadDetailOptions();
  }

  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _saveMeasurement,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.purple[600],
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          elevation: 4,
        ),
        child: _isLoading
            ? const SizedBox(
                height: 24,
                width: 24,
                child: CircularProgressIndicator(
                  color: Colors.white,
                  strokeWidth: 2,
                ),
              )
            : Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(_isEdit ? Icons.update : Icons.save, size: 24),
                  const SizedBox(width: 12),
                  Text(
                    _isEdit ? 'تحديث المقاس' : 'حفظ المقاس',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  Future<void> _saveMeasurement() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final price = double.tryParse(_priceController.text) ?? 0;
      final paid = double.tryParse(_paidController.text) ?? 0;
      final remaining = price - paid;

      final data = {
        'billNumber': _billNumberController.text.trim(),
        'customerName': _customerNameController.text.trim(),
        'phoneNumber': _phoneNumberController.text.trim(),
        'fabricType': _fabricTypeController.text.trim(),
        'quantity': int.tryParse(_quantityController.text.trim()) ?? 1,
        'receivedDate': _receivedDateController.text.trim(),
        'deliveryDate': _deliveryDateController.text.trim(),
        'price': price,
        'paid': paid,
        'remaining': remaining,
        'notes': _notesController.text.trim(),
        'dayName': AppDateUtils.getCurrentDayName(), // إضافة اسم اليوم
        'createdAt': DateTime.now().toIso8601String(),
      };

      // إضافة المقاسات حسب النوع
      if (widget.measurementType == 'clothing') {
        for (final entry in _clothingMeasurements.entries) {
          data[entry.key] = entry.value.text.trim();
        }
        data['neckType'] = _neckType ?? '';
        data['zipperType'] = _zipperType ?? '';
        data['handType'] = _handType ?? '';
        data['pocketType'] = _pocketType ?? '';
        data['styleType'] = _styleType ?? '';
        data['buttonType'] = _buttonType ?? '';
        data['cuffType'] = _cuffType ?? '';
      } else if (widget.measurementType == 'suits') {
        for (final entry in _suitMeasurements.entries) {
          data[entry.key] = entry.value.text.trim();
        }
      } else if (widget.measurementType == 'women') {
        for (final entry in _womenMeasurements.entries) {
          data[entry.key] = entry.value.text.trim();
        }
      }

      if (_isEdit) {
        await DBHelper.updateManagerMeasurement(
          widget.measurementToEdit!['id'],
          widget.measurementType,
          data,
        );
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.white),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(AppDateUtils.getMeasurementUpdatedMessage()),
                  ),
                ],
              ),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          );
        }
      } else {
        await DBHelper.insertManagerMeasurement(widget.measurementType, data);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.white),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(AppDateUtils.getMeasurementAddedMessage()),
                  ),
                ],
              ),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          );
        }
      }

      if (mounted) {
        Navigator.of(context).pop(true);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(
                  child: Text('خطأ في ${_isEdit ? 'تحديث' : 'إضافة'} المقاس: $e'),
                ),
              ],
            ),
            backgroundColor: Colors.red[600],
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
