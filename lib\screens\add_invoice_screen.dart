import 'package:flutter/material.dart';
import '../models/db_helper.dart';
import '../utils/date_utils.dart';
import '../widgets/export_print_buttons.dart';

class AddInvoiceScreen extends StatefulWidget {
  final Map<String, dynamic> workshop;

  const AddInvoiceScreen({super.key, required this.workshop});

  @override
  State<AddInvoiceScreen> createState() => _AddInvoiceScreenState();
}

class _AddInvoiceScreenState extends State<AddInvoiceScreen> {
  bool _showForm = false;
  bool _isLoading = false;
  bool _isEdit = false;
  Map<String, dynamic>? _invoiceToEdit;
  List<Map<String, dynamic>> _invoices = [];
  List<Map<String, dynamic>> _filteredInvoices = [];

  // Controllers للنموذج
  final _shopNameController = TextEditingController();
  final _invoiceNumberController = TextEditingController();
  final _customerNameController = TextEditingController();
  final _clothesCountController = TextEditingController();
  final _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadInvoices();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _shopNameController.dispose();
    _invoiceNumberController.dispose();
    _customerNameController.dispose();
    _clothesCountController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    final query = _searchController.text.trim();
    if (query.isEmpty) {
      setState(() {
        _filteredInvoices = List.from(_invoices);
      });
    } else {
      setState(() {
        _filteredInvoices = _invoices.where((invoice) {
          final customerName = invoice['customerName'].toString().toLowerCase();
          final invoiceNumber = invoice['invoiceNumber'].toString().toLowerCase();
          final searchQuery = query.toLowerCase();
          return customerName.contains(searchQuery) || invoiceNumber.contains(searchQuery);
        }).toList();
      });
    }
  }

  Future<void> _loadInvoices() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final workshopId = widget.workshop['id'] as int;
      final invoices = await DBHelper.getInvoicesForWorkshop(workshopId);
      setState(() {
        _invoices = invoices;
        _filteredInvoices = List.from(invoices);
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل الفواتير: $e')),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showAddForm() {
    setState(() {
      _showForm = true;
      _isEdit = false;
      _invoiceToEdit = null;
    });
  }

  void _showEditForm(Map<String, dynamic> invoice) {
    setState(() {
      _showForm = true;
      _isEdit = true;
      _invoiceToEdit = invoice;
    });
    _loadInvoiceData(invoice);
  }

  void _hideForm() {
    setState(() {
      _showForm = false;
      _isEdit = false;
      _invoiceToEdit = null;
    });
    _clearForm();
  }

  void _loadInvoiceData(Map<String, dynamic> invoice) {
    _shopNameController.text = invoice['shopName'] ?? '';
    _invoiceNumberController.text = invoice['invoiceNumber'] ?? '';
    _customerNameController.text = invoice['customerName'] ?? '';
    _clothesCountController.text = invoice['clothesCount']?.toString() ?? '';
  }

  void _clearForm() {
    _shopNameController.clear();
    _invoiceNumberController.clear();
    _customerNameController.clear();
    _clothesCountController.clear();
  }

  Future<void> _saveInvoice() async {
    if (_invoiceNumberController.text.trim().isEmpty ||
        _customerNameController.text.trim().isEmpty ||
        _clothesCountController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى ملء جميع الحقول')),
      );
      return;
    }

    final clothesCount = int.tryParse(_clothesCountController.text.trim());
    if (clothesCount == null || clothesCount <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى إدخال عدد صحيح للثياب')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final workshopId = widget.workshop['id'] as int;
      final workshopName = widget.workshop['name'] as String;

      final data = {
        'workshopId': workshopId,
        'shopName': workshopName, // يتم تعبئته تلقائياً من اسم المعمل
        'invoiceNumber': _invoiceNumberController.text.trim(),
        'customerName': _customerNameController.text.trim(),
        'clothesCount': clothesCount,
        'isReady': _isEdit ? (_invoiceToEdit!['isReady'] ?? 0) : 0,
        'dayName': _isEdit ? (_invoiceToEdit!['dayName'] ?? AppDateUtils.getCurrentDayName()) : AppDateUtils.getCurrentDayName(),
        'createdAt': _isEdit ? (_invoiceToEdit!['createdAt'] ?? DateTime.now().toIso8601String()) : DateTime.now().toIso8601String(),
      };

      if (_isEdit) {
        await DBHelper.updateInvoice(_invoiceToEdit!['id'], data);
      } else {
        await DBHelper.insertInvoice(data);
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(_isEdit ? AppDateUtils.getInvoiceUpdatedMessage() : AppDateUtils.getInvoiceAddedMessage()),
                ),
              ],
            ),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
        _hideForm();
        _loadInvoices(); // إعادة تحميل الفواتير
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في حفظ الفاتورة: $e')),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _toggleInvoiceStatus(int invoiceId, bool currentStatus) async {
    try {
      await DBHelper.updateInvoiceStatus(invoiceId, !currentStatus);
      _loadInvoices(); // إعادة تحميل الفواتير

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(!currentStatus ? 'تم تحديد الفاتورة كجاهزة' : 'تم تحديد الفاتورة كغير جاهزة'),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحديث حالة الفاتورة: $e')),
        );
      }
    }
  }

  Future<void> _deleteInvoice(int invoiceId) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل أنت متأكد من حذف هذه الفاتورة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await DBHelper.deleteInvoice(invoiceId);
        _loadInvoices(); // إعادة تحميل الفواتير

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم حذف الفاتورة بنجاح')),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('خطأ في حذف الفاتورة: $e')),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final workshopName = widget.workshop['name'] as String;

    return Scaffold(
      appBar: AppBar(
        title: Text('فواتير $workshopName'),
        backgroundColor: Colors.blue[700],
        foregroundColor: Colors.white,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // شريط البحث
                if (!_showForm && _invoices.isNotEmpty)
                  Container(
                    padding: const EdgeInsets.all(16),
                    child: TextField(
                      controller: _searchController,
                      decoration: InputDecoration(
                        hintText: 'البحث باسم الزبون أو رقم الفاتورة...',
                        prefixIcon: const Icon(Icons.search),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                        filled: true,
                        fillColor: Colors.grey[100],
                      ),
                    ),
                  ),

                // زر التصدير الشامل للفواتير
                if (!_showForm && _invoices.isNotEmpty)
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    child: ExportPrintButtons(
                      data: _invoices,
                      type: 'invoices',
                      title: 'جميع فواتير معمل ${widget.workshop['name']}',
                      showIndividual: false,
                      showBulk: true,
                    ),
                  ),

                // المحتوى الرئيسي
                Expanded(
                  child: _showForm ? _buildForm() : _buildInvoicesList(),
                ),
              ],
            ),

      // زر الإضافة العائم
      floatingActionButton: _showForm
          ? null
          : FloatingActionButton(
              onPressed: _showAddForm,
              backgroundColor: Colors.blue[700],
              child: const Icon(Icons.add, color: Colors.white),
            ),
    );
  }

  Widget _buildForm() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Card(
        elevation: 4,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'إضافة فاتورة جديدة - ${widget.workshop['name']}',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                  ),
                  IconButton(
                    onPressed: _hideForm,
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
              const SizedBox(height: 20),

              // عرض اسم المعمل (غير قابل للتعديل)
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.store, color: Colors.grey),
                    const SizedBox(width: 12),
                    Text(
                      'اسم المعمل: ${widget.workshop['name']}',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),

              // رقم الفاتورة
              TextField(
                controller: _invoiceNumberController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  labelText: 'رقم الفاتورة',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                  prefixIcon: const Icon(Icons.receipt),
                ),
              ),
              const SizedBox(height: 16),

              // اسم الزبون
              TextField(
                controller: _customerNameController,
                decoration: InputDecoration(
                  labelText: 'اسم الزبون',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                  prefixIcon: const Icon(Icons.person),
                ),
              ),
              const SizedBox(height: 16),

              // عدد الثياب
              TextField(
                controller: _clothesCountController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  labelText: 'عدد الثياب',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                  prefixIcon: const Icon(Icons.checkroom),
                ),
              ),
              const SizedBox(height: 24),

              // أزرار الحفظ والإلغاء
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _saveInvoice,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                      child: _isLoading
                          ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : Text(_isEdit ? 'تحديث الفاتورة' : 'حفظ الفاتورة'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: OutlinedButton(
                      onPressed: _hideForm,
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                      child: const Text('إلغاء'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInvoicesList() {
    if (_filteredInvoices.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.receipt_long,
              size: 80,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              _searchController.text.trim().isNotEmpty
                  ? 'لا توجد فواتير تطابق البحث'
                  : 'لا توجد فواتير بعد',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
              ),
            ),
            if (_searchController.text.trim().isEmpty) ...[
              const SizedBox(height: 8),
              Text(
                'اضغط على زر + لإضافة فاتورة جديدة',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[500],
                ),
              ),
            ],
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _filteredInvoices.length,
      itemBuilder: (context, index) {
        final invoice = _filteredInvoices[index];
        final isReady = invoice['isReady'] == 1;
        final createdAt = DateTime.parse(invoice['createdAt']);

        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          elevation: 2,
          child: ListTile(
            contentPadding: const EdgeInsets.all(16),
            leading: CircleAvatar(
              backgroundColor: isReady ? Colors.green : Colors.orange,
              child: Icon(
                isReady ? Icons.check : Icons.schedule,
                color: Colors.white,
              ),
            ),
            title: Text(
              invoice['customerName'],
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 4),
                Text('المحل: ${invoice['shopName']}'),
                Text('رقم الفاتورة: ${invoice['invoiceNumber']}'),
                Text('عدد الثياب: ${invoice['clothesCount']}'),
                Text(
                  'التاريخ: ${createdAt.day}/${createdAt.month}/${createdAt.year}',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
                if (invoice['dayName'] != null)
                  Text(
                    'اليوم: ${invoice['dayName']}',
                    style: TextStyle(
                      color: Colors.blue[600],
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),

                const SizedBox(height: 8),

                // أزرار التصدير
                ExportPrintButtons(
                  data: invoice,
                  type: 'invoices',
                  title: 'فاتورة ${invoice['customerName']} - ${invoice['invoiceNumber']}',
                  showIndividual: true,
                ),
              ],
            ),
            trailing: PopupMenuButton<String>(
              onSelected: (value) {
                switch (value) {
                  case 'edit':
                    _showEditForm(invoice);
                    break;
                  case 'toggle_status':
                    _toggleInvoiceStatus(invoice['id'], isReady);
                    break;
                  case 'delete':
                    _deleteInvoice(invoice['id']);
                    break;
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'edit',
                  child: Row(
                    children: [
                      Icon(Icons.edit, color: Colors.blue),
                      SizedBox(width: 8),
                      Text('تعديل', style: TextStyle(color: Colors.blue)),
                    ],
                  ),
                ),
                PopupMenuItem(
                  value: 'toggle_status',
                  child: Row(
                    children: [
                      Icon(isReady ? Icons.schedule : Icons.check),
                      const SizedBox(width: 8),
                      Text(isReady ? 'تحديد كغير جاهزة' : 'تحديد كجاهزة'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'delete',
                  child: Row(
                    children: [
                      Icon(Icons.delete, color: Colors.red),
                      SizedBox(width: 8),
                      Text('حذف', style: TextStyle(color: Colors.red)),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
