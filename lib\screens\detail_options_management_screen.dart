import 'package:flutter/material.dart';
import '../models/db_helper.dart';

class DetailOptionsManagementScreen extends StatefulWidget {
  const DetailOptionsManagementScreen({super.key});

  @override
  State<DetailOptionsManagementScreen> createState() => _DetailOptionsManagementScreenState();
}

class _DetailOptionsManagementScreenState extends State<DetailOptionsManagementScreen>
    with TickerProviderStateMixin {

  late TabController _tabController;
  bool _isLoading = true;

  // خريطة لحفظ الخيارات لكل نوع
  final Map<String, List<Map<String, dynamic>>> _options = {};

  // خريطة لحفظ controllers لكل نوع
  final Map<String, TextEditingController> _controllers = {};

  // خريطة لحفظ FocusNode لكل نوع
  final Map<String, FocusNode> _focusNodes = {};

  // قائمة أنواع الخيارات
  final List<Map<String, dynamic>> _optionTypes = [
    {'key': 'neck', 'title': 'الرقاب', 'icon': Icons.accessibility_new, 'color': Colors.blue},
    {'key': 'zipper', 'title': 'الجبازير', 'icon': Icons.vertical_align_center, 'color': Colors.green},
    {'key': 'hand', 'title': 'الأيادي', 'icon': Icons.back_hand, 'color': Colors.orange},
    {'key': 'pocket', 'title': 'الجيوب', 'icon': Icons.inventory_2, 'color': Colors.purple},
    {'key': 'style', 'title': 'التفصيل', 'icon': Icons.style, 'color': Colors.teal},
    {'key': 'button', 'title': 'الأزرار', 'icon': Icons.radio_button_checked, 'color': Colors.red},
    {'key': 'cuff', 'title': 'الكبك', 'icon': Icons.watch, 'color': Colors.indigo},
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _optionTypes.length, vsync: this);

    // تهيئة controllers و focus nodes
    for (final type in _optionTypes) {
      _controllers[type['key']] = TextEditingController();
      _focusNodes[type['key']] = FocusNode();
    }

    _loadAllOptions();
  }

  @override
  void dispose() {
    _tabController.dispose();
    for (final controller in _controllers.values) {
      controller.dispose();
    }
    for (final focusNode in _focusNodes.values) {
      focusNode.dispose();
    }
    super.dispose();
  }

  Future<void> _loadAllOptions() async {
    setState(() => _isLoading = true);

    try {
      for (final type in _optionTypes) {
        final options = await DBHelper.getDetailOptions(type['key']);
        _options[type['key']] = options;
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('خطأ في تحميل البيانات: $e');
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  // دالة للتركيز على حقل الإدخال الحالي
  void _focusOnCurrentInput() {
    final currentKey = _optionTypes[_tabController.index]['key'];
    final focusNode = _focusNodes[currentKey];
    if (focusNode != null) {
      // استخدام Future.delayed لضمان التركيز الصحيح
      Future.delayed(const Duration(milliseconds: 100), () {
        if (mounted) {
          focusNode.requestFocus();
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: Colors.grey[50],
        appBar: _buildAppBar(),
        body: _isLoading ? _buildLoadingWidget() : _buildBody(),
        floatingActionButton: _buildFloatingActionButton(),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text(
        'إدارة خيارات التفصيل',
        style: TextStyle(
          fontWeight: FontWeight.bold,
          color: Colors.white,
          fontSize: 20,
        ),
      ),
      backgroundColor: Colors.deepPurple[700],
      elevation: 0,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back, color: Colors.white),
        onPressed: () => Navigator.of(context).pop(),
        tooltip: 'العودة',
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.refresh, color: Colors.white),
          onPressed: _loadAllOptions,
          tooltip: 'تحديث البيانات',
        ),
        IconButton(
          icon: const Icon(Icons.info_outline, color: Colors.white),
          onPressed: _showInfoDialog,
          tooltip: 'معلومات',
        ),
      ],
      bottom: TabBar(
        controller: _tabController,
        isScrollable: true,
        labelColor: Colors.white,
        unselectedLabelColor: Colors.white70,
        indicatorColor: Colors.white,
        indicatorWeight: 3,
        indicatorSize: TabBarIndicatorSize.tab,
        labelStyle: const TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: 14,
        ),
        unselectedLabelStyle: const TextStyle(
          fontWeight: FontWeight.normal,
          fontSize: 13,
        ),
        tabs: _optionTypes.map((type) => Tab(
          text: type['title'],
          icon: Icon(type['icon'], size: 20),
        )).toList(),
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Colors.deepPurple),
          ),
          SizedBox(height: 16),
          Text(
            'جاري تحميل البيانات...',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBody() {
    return TabBarView(
      controller: _tabController,
      children: _optionTypes.map((type) => _buildOptionTab(
        type['key'],
        type['title'],
        type['icon'],
        type['color'],
      )).toList(),
    );
  }

  Widget _buildOptionTab(String type, String title, IconData icon, Color color) {
    final options = _options[type] ?? [];
    final controller = _controllers[type]!;

    return RefreshIndicator(
      onRefresh: _loadAllOptions,
      color: color,
      child: CustomScrollView(
        slivers: [
          // قسم إضافة خيار جديد
          SliverToBoxAdapter(
            child: _buildAddOptionSection(type, title, icon, color, controller),
          ),

          // إحصائيات سريعة
          SliverToBoxAdapter(
            child: _buildStatsSection(type, title, options.length, color),
          ),

          // قائمة الخيارات
          options.isEmpty
              ? SliverFillRemaining(
                  child: _buildEmptyState(title, icon, color),
                )
              : SliverList(
                  delegate: SliverChildBuilderDelegate(
                    (context, index) => _buildOptionCard(type, options[index], index, color),
                    childCount: options.length,
                  ),
                ),

          // مساحة إضافية في الأسفل
          const SliverToBoxAdapter(
            child: SizedBox(height: 100),
          ),
        ],
      ),
    );
  }

  Widget _buildAddOptionSection(String type, String title, IconData icon, Color color, TextEditingController controller) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.1),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
        border: Border.all(color: color.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(icon, color: color, size: 28),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'إضافة $title جديد',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.grey[800],
                      ),
                    ),
                    Text(
                      'أضف خيار جديد لقائمة $title',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),

          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: controller,
                  focusNode: _focusNodes[type],
                  decoration: InputDecoration(
                    hintText: 'مثال: ${_getExampleText(type)}',
                    prefixIcon: Icon(icon, color: color),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(15),
                      borderSide: BorderSide(color: Colors.grey[300]!),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(15),
                      borderSide: BorderSide(color: color, width: 2),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(15),
                      borderSide: BorderSide(color: Colors.grey[300]!),
                    ),
                    filled: true,
                    fillColor: Colors.grey[50],
                    contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                    hintStyle: TextStyle(color: Colors.grey[500]),
                  ),
                  onSubmitted: (_) => _addOption(type, controller, color),
                  textInputAction: TextInputAction.done,
                ),
              ),
              const SizedBox(width: 16),
              ElevatedButton.icon(
                onPressed: () => _addOption(type, controller, color),
                icon: const Icon(Icons.add, size: 20),
                label: const Text(
                  'إضافة',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: color,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(15),
                  ),
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                  elevation: 3,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatsSection(String type, String title, int count, Color color) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(Icons.analytics, color: color, size: 24),
          const SizedBox(width: 12),
          Text(
            'إجمالي خيارات $title:',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.grey[700],
            ),
          ),
          const Spacer(),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              '$count',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(String title, IconData icon, Color color) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              size: 80,
              color: color.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'لا توجد خيارات $title',
            style: TextStyle(
              fontSize: 22,
              color: Colors.grey[600],
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            'أضف خيار جديد من الأعلى لبدء الاستخدام',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              // التركيز على حقل الإدخال
              _focusOnCurrentInput();
            },
            icon: const Icon(Icons.add),
            label: const Text('إضافة خيار جديد'),
            style: ElevatedButton.styleFrom(
              backgroundColor: color,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOptionCard(String type, Map<String, dynamic> option, int index, Color color) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(color: color.withOpacity(0.1)),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
        leading: Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Center(
            child: Text(
              '${index + 1}',
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.bold,
                fontSize: 18,
              ),
            ),
          ),
        ),
        title: Text(
          option['name'],
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Padding(
          padding: const EdgeInsets.only(top: 8),
          child: Row(
            children: [
              Icon(Icons.access_time, size: 16, color: Colors.grey[500]),
              const SizedBox(width: 6),
              Text(
                'تم الإنشاء: ${_formatDate(option['createdAt'])}',
                style: TextStyle(
                  fontSize: 13,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) {
            if (value == 'edit') {
              _editOption(type, option, color);
            } else if (value == 'delete') {
              _deleteOption(type, option, color);
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit, size: 20, color: Colors.blue),
                  SizedBox(width: 12),
                  Text('تعديل', style: TextStyle(fontSize: 16)),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, size: 20, color: Colors.red),
                  SizedBox(width: 12),
                  Text('حذف', style: TextStyle(color: Colors.red, fontSize: 16)),
                ],
              ),
            ),
          ],
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(Icons.more_vert, color: Colors.grey[600]),
          ),
        ),
      ),
    );
  }

  Widget _buildFloatingActionButton() {
    final currentType = _optionTypes[_tabController.index];
    return FloatingActionButton.extended(
      onPressed: () {
        // تركيز محسن على حقل الإدخال
        _focusOnCurrentInput();
      },
      backgroundColor: currentType['color'],
      foregroundColor: Colors.white,
      icon: Icon(currentType['icon']),
      label: Text('إضافة ${currentType['title']}'),
      elevation: 6,
    );
  }

  String _getExampleText(String type) {
    switch (type) {
      case 'neck':
        return 'رقبة دائرية، رقبة V، رقبة مربعة';
      case 'zipper':
        return 'سحاب ذهبي، سحاب فضي، سحاب مخفي';
      case 'hand':
        return 'كم طويل، كم قصير، كم ثلاثة أرباع';
      case 'pocket':
        return 'جيب مخفي، جيب ظاهر، جيب مزدوج';
      case 'style':
        return 'طراز عصري، طراز كلاسيكي، طراز رياضي';
      case 'button':
        return 'زر لؤلؤي، زر ذهبي، زر خشبي';
      case 'cuff':
        return 'كبك فرنسي، كبك عادي، كبك مزدوج';
      default:
        return 'خيار جديد';
    }
  }

  String _formatDate(String? dateString) {
    if (dateString == null) return 'غير محدد';

    try {
      final date = DateTime.parse(dateString);
      final now = DateTime.now();
      final difference = now.difference(date).inDays;

      if (difference == 0) {
        return 'اليوم';
      } else if (difference == 1) {
        return 'أمس';
      } else if (difference < 7) {
        return 'منذ $difference أيام';
      } else {
        return '${date.day}/${date.month}/${date.year}';
      }
    } catch (e) {
      return 'غير محدد';
    }
  }

  Future<void> _addOption(String type, TextEditingController controller, Color color) async {
    final name = controller.text.trim();
    if (name.isEmpty) {
      _showWarningSnackBar('يرجى إدخال اسم الخيار');
      return;
    }

    // التحقق من عدم وجود خيار بنفس الاسم
    final existingOptions = _options[type] ?? [];
    if (existingOptions.any((option) => option['name'].toLowerCase() == name.toLowerCase())) {
      _showWarningSnackBar('هذا الخيار موجود بالفعل');
      return;
    }

    try {
      await DBHelper.addDetailOption(type, name);
      controller.clear();
      await _loadAllOptions();

      if (mounted) {
        _showSuccessSnackBar('تم إضافة الخيار بنجاح');
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('خطأ في إضافة الخيار: $e');
      }
    }
  }

  Future<void> _editOption(String type, Map<String, dynamic> option, Color color) async {
    final controller = TextEditingController(text: option['name']);

    final result = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: Row(
          children: [
            Icon(Icons.edit, color: color),
            const SizedBox(width: 12),
            const Text('تعديل الخيار'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: controller,
              decoration: InputDecoration(
                labelText: 'اسم الخيار',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: color, width: 2),
                ),
                prefixIcon: Icon(Icons.label, color: color),
              ),
              autofocus: true,
              textInputAction: TextInputAction.done,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, controller.text.trim()),
            style: ElevatedButton.styleFrom(
              backgroundColor: color,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('حفظ'),
          ),
        ],
      ),
    );

    if (result != null && result.isNotEmpty && result != option['name']) {
      // التحقق من عدم وجود خيار بنفس الاسم الجديد
      final existingOptions = _options[type] ?? [];
      if (existingOptions.any((opt) => opt['name'].toLowerCase() == result.toLowerCase() && opt['id'] != option['id'])) {
        _showWarningSnackBar('هذا الاسم موجود بالفعل');
        return;
      }

      try {
        await DBHelper.updateDetailOption(type, option['id'], result);
        await _loadAllOptions();

        if (mounted) {
          _showSuccessSnackBar('تم تحديث الخيار بنجاح');
        }
      } catch (e) {
        if (mounted) {
          _showErrorSnackBar('خطأ في تحديث الخيار: $e');
        }
      }
    }

    controller.dispose();
  }

  Future<void> _deleteOption(String type, Map<String, dynamic> option, Color color) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: const Row(
          children: [
            Icon(Icons.warning, color: Colors.red),
            SizedBox(width: 12),
            Text('تأكيد الحذف'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('هل أنت متأكد من حذف "${option['name']}"؟'),
            const SizedBox(height: 12),
            const Text(
              'لا يمكن التراجع عن هذا الإجراء',
              style: TextStyle(
                color: Colors.red,
                fontSize: 14,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await DBHelper.deleteDetailOption(type, option['id']);
        await _loadAllOptions();

        if (mounted) {
          _showSuccessSnackBar('تم حذف الخيار بنجاح');
        }
      } catch (e) {
        if (mounted) {
          _showErrorSnackBar('خطأ في حذف الخيار: $e');
        }
      }
    }
  }

  void _showInfoDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: const Row(
          children: [
            Icon(Icons.info, color: Colors.blue),
            SizedBox(width: 12),
            Text('معلومات الصفحة'),
          ],
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'هذه الصفحة تتيح لك إدارة خيارات التفصيل المختلفة:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 12),
            Text('• إضافة خيارات جديدة لكل نوع'),
            Text('• تعديل الخيارات الموجودة'),
            Text('• حذف الخيارات غير المرغوبة'),
            Text('• عرض إحصائيات سريعة'),
            SizedBox(height: 12),
            Text(
              'يمكن استخدام هذه الخيارات في نماذج المقاسات لتسهيل عملية الإدخال.',
              style: TextStyle(
                fontStyle: FontStyle.italic,
                color: Colors.grey,
              ),
            ),
          ],
        ),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('فهمت'),
          ),
        ],
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white),
            const SizedBox(width: 12),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error, color: Colors.white),
            const SizedBox(width: 12),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.red[600],
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        duration: const Duration(seconds: 4),
      ),
    );
  }

  void _showWarningSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.warning, color: Colors.white),
            const SizedBox(width: 12),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.orange,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        duration: const Duration(seconds: 3),
      ),
    );
  }
}
