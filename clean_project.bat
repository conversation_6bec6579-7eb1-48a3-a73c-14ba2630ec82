@echo off
echo ========================================
echo تنظيف شامل للمشروع وحل مشاكل Kotlin
echo ========================================

echo.
echo 1. تنظيف Flutter...
flutter clean

echo.
echo 2. حذف ملفات التخزين المؤقت...
if exist "build" rmdir /s /q "build"
if exist "android\build" rmdir /s /q "android\build"
if exist "android\app\build" rmdir /s /q "android\app\build"
if exist ".dart_tool" rmdir /s /q ".dart_tool"

echo.
echo 3. حذف ملفات Gradle المؤقتة...
if exist "android\.gradle" rmdir /s /q "android\.gradle"

echo.
echo 4. تحديث packages...
flutter pub get

echo.
echo 5. إعادة بناء المشروع...
cd android
call gradlew clean
cd ..

echo.
echo ========================================
echo تم الانتهاء من التنظيف!
echo يمكنك الآن تشغيل التطبيق بـ: flutter run
echo ========================================
pause
