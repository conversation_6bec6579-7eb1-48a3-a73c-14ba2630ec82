import 'package:flutter/material.dart';
import 'measurement_styles.dart';
import '../widgets/export_print_buttons.dart';

class MeasurementDetailsScreen extends StatelessWidget {
  final Map<String, dynamic> measurement;
  final String measurementType; // 'clothing', 'suits', 'women'

  const MeasurementDetailsScreen({
    super.key,
    required this.measurement,
    required this.measurementType,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: MeasurementStyles.backgroundColor,
      appBar: AppBar(
        title: const Text("تفاصيل المقاس"),
        backgroundColor: MeasurementStyles.primaryColor,
        centerTitle: true,
      ),
      body: Directionality(
        textDirection: TextDirection.rtl,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildDetailsCard(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailsCard(BuildContext context) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // معلومات العميل
            _buildSectionTitle("معلومات العميل"),
            _buildDetailRow("اسم العميل", measurement['customerName'] ?? '-'),
            _buildDetailRow("رقم الهاتف", measurement['phoneNumber'] ?? '-'),
            _buildDetailRow("رقم الفاتورة", measurement['billNumber'] ?? '-'),
            _buildDetailRow("تاريخ الاستلام", measurement['receivedDate'] ?? '-'),
            _buildDetailRow("موعد التسليم", measurement['deliveryDate'] ?? '-'),
            if (measurement['dayName'] != null)
              _buildDetailRow("يوم الإضافة", measurement['dayName']),

            const SizedBox(height: 16),

            // معلومات المبلغ
            _buildSectionTitle("معلومات المبلغ"),
            _buildDetailRow("المبلغ", "${measurement['price'] ?? '0'} ريال"),
            _buildDetailRow("المدفوع", "${measurement['paid'] ?? '0'} ريال"),
            _buildDetailRow("المتبقي", "${measurement['remaining'] ?? '0'} ريال"),

            const SizedBox(height: 16),

            // تفاصيل المقاسات حسب النوع
            _buildSectionTitle("تفاصيل المقاسات"),
            if (measurementType == 'clothing') _buildClothingMeasurements(),
            if (measurementType == 'suits') _buildSuitsMeasurements(),
            if (measurementType == 'women') _buildWomenMeasurements(),

            const SizedBox(height: 16),

            // معلومات إضافية
            _buildSectionTitle("معلومات إضافية"),
            if (measurementType == 'clothing') _buildClothingOptions(),
            if (measurementType == 'suits') _buildSuitsOptions(),
            if (measurementType == 'women') _buildWomenOptions(),

            const SizedBox(height: 16),

            // ملاحظات
            _buildSectionTitle("ملاحظات"),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: Text(
                measurement['notes'] ?? 'لا توجد ملاحظات',
                style: const TextStyle(fontSize: 16),
              ),
            ),

            const SizedBox(height: 20),

            // أزرار التصدير والطباعة
            ExportPrintButtons(
              data: measurement,
              type: 'measurements',
              title: 'مقاس ${measurement['customerName']} - $measurementType',
              showIndividual: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildClothingMeasurements() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildMeasurementsGrid([
          {"label": "الطول", "value": measurement['height']},
          {"label": "الكتف", "value": measurement['shoulder']},
          {"label": "طول الكم", "value": measurement['sleeveLength']},
          {"label": "وسع الصدر", "value": measurement['chest']},
          {"label": "الرقبه", "value": measurement['neck']},
          {"label": "تنزيله اليد", "value": measurement['handDrop']},
          {"label": "وسط اليد", "value": measurement['middleHand']},
          {"label": "طول الكبك", "value": measurement['cuffLength']},
          {"label": "ارتفاع الكبك", "value": measurement['cuffHeight']},
          {"label": "الخطوة", "value": measurement['step']},
        ]),
      ],
    );
  }

  Widget _buildSuitsMeasurements() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSubSectionTitle("القميص"),
        _buildMeasurementsGrid([
          {"label": "الطول", "value": measurement['shirtHeight']},
          {"label": "الكتف", "value": measurement['shirtShoulder']},
          {"label": "طول الكم", "value": measurement['shirtSleeveLength']},
          {"label": "وسع الصدر", "value": measurement['shirtChest']},
          {"label": "الرقبه", "value": measurement['shirtNeck']},
        ]),

        const SizedBox(height: 12),
        _buildSubSectionTitle("البنطلون"),
        _buildMeasurementsGrid([
          {"label": "الطول", "value": measurement['pantsHeight']},
          {"label": "الوسط", "value": measurement['pantsWaist']},
          {"label": "الجلسة", "value": measurement['pantsSeat']},
          {"label": "الرجل", "value": measurement['pantsLeg']},
          {"label": "الركبة", "value": measurement['pantsKnee']},
        ]),

        const SizedBox(height: 12),
        _buildSubSectionTitle("الجاكيت"),
        _buildMeasurementsGrid([
          {"label": "الطول", "value": measurement['coatHeight']},
          {"label": "الكتف", "value": measurement['coatShoulder']},
          {"label": "طول الكم", "value": measurement['coatSleeveLength']},
          {"label": "وسع الصدر", "value": measurement['coatChest']},
          {"label": "الوسط", "value": measurement['coatWaist']},
        ]),
      ],
    );
  }

  Widget _buildWomenMeasurements() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildMeasurementsGrid([
          {"label": "الطول", "value": measurement['height']},
          {"label": "الكتف", "value": measurement['shoulder']},
          {"label": "طول الكم", "value": measurement['sleeveLength']},
          {"label": "محيط الصدر", "value": measurement['chestCircumference']},
          {"label": "الخصر", "value": measurement['waist']},
          {"label": "الورك", "value": measurement['hip']},
          {"label": "طول الكورساج", "value": measurement['corsageLength']},
          {"label": "طول الجيبة", "value": measurement['skirtLength']},
          {"label": "طول البنطلون", "value": measurement['pantsLength']},
          {"label": "طول الفستان", "value": measurement['dressLength']},
        ]),
      ],
    );
  }

  Widget _buildClothingOptions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildDetailRow("نوع القماش", measurement['fabricType'] ?? '-'),
        _buildDetailRow("الكمية", measurement['quantity']?.toString() ?? '-'),
        _buildDetailRow("الرقبة", measurement['neckType'] ?? '-'),
        _buildDetailRow("الجبزور", measurement['zipperType'] ?? '-'),
        _buildDetailRow("اليد", measurement['handType'] ?? '-'),
        _buildDetailRow("الجيوب", measurement['pocketType'] ?? '-'),
        _buildDetailRow("نوع التفصيل", measurement['styleType'] ?? '-'),
        _buildDetailRow("الزرارات", measurement['buttonType'] ?? '-'),
      ],
    );
  }

  Widget _buildSuitsOptions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildDetailRow("نوع القماش", measurement['fabricType'] ?? '-'),
        _buildDetailRow("الكمية", measurement['quantity']?.toString() ?? '-'),
        _buildDetailRow("نوع البدلة", measurement['suitType'] ?? '-'),
        _buildDetailRow("لون البدلة", measurement['suitColor'] ?? '-'),
        _buildDetailRow("نوع البطانة", measurement['liningType'] ?? '-'),
        _buildDetailRow("نوع الأزرار", measurement['buttonType'] ?? '-'),
      ],
    );
  }

  Widget _buildWomenOptions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildDetailRow("نوع القماش", measurement['fabricType'] ?? '-'),
        _buildDetailRow("الكمية", measurement['quantity']?.toString() ?? '-'),
        _buildDetailRow("نوع التصميم", measurement['designType'] ?? '-'),
        _buildDetailRow("لون القماش", measurement['fabricColor'] ?? '-'),
        _buildDetailRow("نوع التطريز", measurement['embroideryType'] ?? '-'),
      ],
    );
  }

  Widget _buildMeasurementsGrid(List<Map<String, dynamic>> measurements) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 3,
        crossAxisSpacing: 10,
        mainAxisSpacing: 10,
      ),
      itemCount: measurements.length,
      itemBuilder: (context, index) {
        final item = measurements[index];
        return Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: Row(
            children: [
              Text(
                "${item["label"]}: ",
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
              ),
              Expanded(
                child: Text(
                  item["value"]?.toString() ?? '-',
                  style: const TextStyle(fontSize: 14),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: MeasurementStyles.primaryColor,
        ),
      ),
    );
  }

  Widget _buildSubSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              "$label:",
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 15,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 15),
            ),
          ),
        ],
      ),
    );
  }
}
