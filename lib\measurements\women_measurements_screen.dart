import 'package:flutter/material.dart';
import 'measurement_styles.dart';
import '../models/db_helper.dart';
import '../utils/number_utils.dart';
import '../utils/date_utils.dart';

class WomenMeasurementsScreen extends StatefulWidget {
  final int? measurementId;
  final int? workshopId;

  const WomenMeasurementsScreen({super.key, this.measurementId, this.workshopId});

  @override
  State<WomenMeasurementsScreen> createState() => _WomenMeasurementsScreenState();
}

class _WomenMeasurementsScreenState extends State<WomenMeasurementsScreen> {
  bool _isLoading = false;
  bool _isEdit = false;
  final _formKey = GlobalKey<FormState>();
  final billController = TextEditingController();
  final nameController = TextEditingController();
  final phoneController = TextEditingController();
  final fabricController = TextEditingController();
  final quantityController = TextEditingController();
  final dateController = TextEditingController();
  final priceController = TextEditingController();
  final paidController = TextEditingController();
  final remainController = TextEditingController();
  final noteController = TextEditingController();
  final deliveryDateController = TextEditingController();

  final Map<String, TextEditingController> measurements = {
    'الطول الكامل': TextEditingController(),
    'الكتف': TextEditingController(),
    'طول الكم': TextEditingController(),
    'محيط الصدر': TextEditingController(),
    'الخصر': TextEditingController(),
    'الورك': TextEditingController(),
    'طول الموديل': TextEditingController(),
    'طول الامام': TextEditingController(),
    'طول الظهر': TextEditingController(),
    'ياقه الظهر': TextEditingController(),
    'نزول الصدر': TextEditingController(),
    'نزول الورك': TextEditingController(),
    'نزول الظهر': TextEditingController(),
    'محيط الرقبة': TextEditingController(),
    'عرض الظهر': TextEditingController(),
    'عرض الصدر': TextEditingController(),
    'عرض مقعد الكم': TextEditingController(),
    'عرض المعصم': TextEditingController(),
    'طول البنطلون': TextEditingController(),
    'حزام البنطلون': TextEditingController(),
    'ورك البنطلون': TextEditingController(),
    'الفخد': TextEditingController(),
    'ارتفاع مقعد الجلوس': TextEditingController(),
    'الركبه': TextEditingController(),
    'الساق فتحه الرجل': TextEditingController(),
  };

  @override
  void initState() {
    super.initState();
    _isEdit = widget.measurementId != null;
    if (_isEdit) {
      _loadMeasurement();
    }
  }

  Future<void> _loadMeasurement() async {
    setState(() => _isLoading = true);
    try {
      final measurement = await DBHelper.getWomenMeasurementById(widget.measurementId!);
      if (measurement != null) {
        // ملء النموذج بالبيانات المسترجعة
        billController.text = measurement['billNumber'] ?? '';
        nameController.text = measurement['customerName'] ?? '';
        phoneController.text = measurement['phoneNumber'] ?? '';
        fabricController.text = measurement['fabricType'] ?? '';
        quantityController.text = measurement['quantity']?.toString() ?? '';
        dateController.text = measurement['receivedDate'] ?? '';
        priceController.text = measurement['price']?.toString() ?? '';
        paidController.text = measurement['paid']?.toString() ?? '';
        remainController.text = measurement['remaining']?.toString() ?? '';
        deliveryDateController.text = measurement['deliveryDate'] ?? '';
        noteController.text = measurement['notes'] ?? '';

        // ملء قيم المقاسات
        measurements['الطول الكامل']!.text = measurement['fullHeight'] ?? '';
        measurements['الكتف']!.text = measurement['shoulder'] ?? '';
        measurements['طول الكم']!.text = measurement['sleeveLength'] ?? '';
        measurements['محيط الصدر']!.text = measurement['chestCircumference'] ?? '';
        measurements['الخصر']!.text = measurement['waist'] ?? '';
        measurements['الورك']!.text = measurement['hip'] ?? '';
        measurements['طول الموديل']!.text = measurement['modelLength'] ?? '';
        measurements['طول الامام']!.text = measurement['frontLength'] ?? '';
        measurements['طول الظهر']!.text = measurement['backLength'] ?? '';
        measurements['ياقه الظهر']!.text = measurement['backCollar'] ?? '';
        measurements['نزول الصدر']!.text = measurement['chestDrop'] ?? '';
        measurements['نزول الورك']!.text = measurement['hipDrop'] ?? '';
        measurements['نزول الظهر']!.text = measurement['backDrop'] ?? '';
        measurements['محيط الرقبة']!.text = measurement['neckCircumference'] ?? '';
        measurements['عرض الظهر']!.text = measurement['backWidth'] ?? '';
        measurements['عرض الصدر']!.text = measurement['chestWidth'] ?? '';
        measurements['عرض مقعد الكم']!.text = measurement['sleeveSeatWidth'] ?? '';
        measurements['عرض المعصم']!.text = measurement['wristWidth'] ?? '';
        measurements['طول البنطلون']!.text = measurement['pantsLength'] ?? '';
        measurements['حزام البنطلون']!.text = measurement['pantsBelt'] ?? '';
        measurements['ورك البنطلون']!.text = measurement['pantsHip'] ?? '';
        measurements['الفخد']!.text = measurement['pantsThigh'] ?? '';
        measurements['ارتفاع مقعد الجلوس']!.text = measurement['sittingHeight'] ?? '';
        measurements['الركبه']!.text = measurement['knee'] ?? '';
        measurements['الساق فتحه الرجل']!.text = measurement['legOpening'] ?? '';
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('حدث خطأ أثناء تحميل البيانات: ${e.toString()}')),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _saveMeasurement() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);
    try {
      // حساب المتبقي إذا لم يكن مدخلاً
      if (priceController.text.isNotEmpty && paidController.text.isNotEmpty) {
        final price = double.tryParse(priceController.text) ?? 0;
        final paid = double.tryParse(paidController.text) ?? 0;
        remainController.text = (price - paid).toString();
      }

      // جمع البيانات من النموذج
      final data = {
        'workshopId': widget.workshopId, // ربط المقاس بالمعمل
        'billNumber': billController.text,
        'customerName': nameController.text,
        'phoneNumber': phoneController.text,
        'fabricType': fabricController.text,
        'quantity': quantityController.text.isNotEmpty ? NumberUtils.parseInteger(quantityController.text) : null,
        'receivedDate': dateController.text,
        'price': priceController.text.isNotEmpty ? NumberUtils.parseDouble(priceController.text) : null,
        'paid': paidController.text.isNotEmpty ? NumberUtils.parseDouble(paidController.text) : null,
        'remaining': remainController.text.isNotEmpty ? NumberUtils.parseDouble(remainController.text) : null,
        'deliveryDate': deliveryDateController.text,
        'notes': noteController.text,

        // بيانات المقاسات
        'fullHeight': measurements['الطول الكامل']!.text,
        'shoulder': measurements['الكتف']!.text,
        'sleeveLength': measurements['طول الكم']!.text,
        'chestCircumference': measurements['محيط الصدر']!.text,
        'waist': measurements['الخصر']!.text,
        'hip': measurements['الورك']!.text,
        'modelLength': measurements['طول الموديل']!.text,
        'frontLength': measurements['طول الامام']!.text,
        'backLength': measurements['طول الظهر']!.text,
        'backCollar': measurements['ياقه الظهر']!.text,
        'chestDrop': measurements['نزول الصدر']!.text,
        'hipDrop': measurements['نزول الورك']!.text,
        'backDrop': measurements['نزول الظهر']!.text,
        'neckCircumference': measurements['محيط الرقبة']!.text,
        'backWidth': measurements['عرض الظهر']!.text,
        'chestWidth': measurements['عرض الصدر']!.text,
        'sleeveSeatWidth': measurements['عرض مقعد الكم']!.text,
        'wristWidth': measurements['عرض المعصم']!.text,
        'pantsLength': measurements['طول البنطلون']!.text,
        'pantsBelt': measurements['حزام البنطلون']!.text,
        'pantsHip': measurements['ورك البنطلون']!.text,
        'pantsThigh': measurements['الفخد']!.text,
        'sittingHeight': measurements['ارتفاع مقعد الجلوس']!.text,
        'knee': measurements['الركبه']!.text,
        'legOpening': measurements['الساق فتحه الرجل']!.text,

        // إضافة اسم اليوم
        'dayName': AppDateUtils.getCurrentDayName(),
        'createdAt': DateTime.now().toIso8601String(),
      };

      if (_isEdit) {
        await DBHelper.updateWomenMeasurement(widget.measurementId!, data);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.white),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(AppDateUtils.getMeasurementUpdatedMessage()),
                  ),
                ],
              ),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          );
        }
      } else {
        await DBHelper.insertWomenMeasurement(data);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.white),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(AppDateUtils.getMeasurementAddedMessage()),
                  ),
                ],
              ),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          );
        }
      }

      if (!mounted) return;
      Navigator.pop(context, true); // إرجاع true للإشارة إلى نجاح العملية
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('حدث خطأ أثناء الحفظ: ${e.toString()}')),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  // بناء صف مع ثلاثة حقول إدخال
  Widget buildTripleInput(String label1, String label2, String label3) {
    return Row(
      children: [
        Expanded(child: MeasurementStyles.buildInput(label1, measurements[label1]!)),
        const SizedBox(width: 10),
        Expanded(child: MeasurementStyles.buildInput(label2, measurements[label2]!)),
        const SizedBox(width: 10),
        Expanded(child: MeasurementStyles.buildInput(label3, measurements[label3]!)),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: MeasurementStyles.backgroundColor,
      appBar: AppBar(
        title: Text(_isEdit ? "تعديل قياس نسائي" : "قياس نسائي جديد"),
        backgroundColor: MeasurementStyles.primaryColor,
        centerTitle: true,
      ),
      body: Directionality(
        textDirection: TextDirection.rtl,
        child: Form(
          key: _formKey,
          child: Stack(
            children: [
              ListView(
                padding: const EdgeInsets.all(16),
                children: [
                  Card(
                    elevation: 4,
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // رقم الفاتورة المميز
                          MeasurementStyles.specialInvoiceField(billController),

                          // بيانات العميل
                          MeasurementStyles.sectionTitle("بيانات العميلة"),
                          MeasurementStyles.buildInput("اسم العميلة", nameController),
                          MeasurementStyles.buildDoubleInput(
                            "رقم الهاتف",
                            "نوع القماش",
                            phoneController,
                            fabricController
                          ),
                          MeasurementStyles.buildDoubleInput(
                            "الكمية",
                            "تاريخ الاستلام",
                            quantityController,
                            dateController
                          ),
                          MeasurementStyles.buildDoubleInput(
                            "المبلغ",
                            "المدفوع",
                            priceController,
                            paidController
                          ),
                          MeasurementStyles.buildInput("المتبقي", remainController),

                          // القياسات
                          MeasurementStyles.sectionTitle("القياسات"),

                          // القياسات بتنسيق ثلاثة في كل صف
                          buildTripleInput('الطول الكامل', 'الكتف', 'طول الكم'),
                          const SizedBox(height: 10),
                          buildTripleInput('محيط الصدر', 'الخصر', 'الورك'),
                          const SizedBox(height: 10),
                          buildTripleInput('طول الموديل', 'طول الامام', 'طول الظهر'),
                          const SizedBox(height: 10),
                          buildTripleInput('ياقه الظهر', 'نزول الصدر', 'نزول الورك'),
                          const SizedBox(height: 10),
                          buildTripleInput('نزول الظهر', 'محيط الرقبة', 'عرض الظهر'),
                          const SizedBox(height: 10),
                          buildTripleInput('عرض الصدر', 'عرض مقعد الكم', 'عرض المعصم'),
                          const SizedBox(height: 10),
                          buildTripleInput('طول البنطلون', 'حزام البنطلون', 'ورك البنطلون'),
                          const SizedBox(height: 10),
                          buildTripleInput('الفخد', 'ارتفاع مقعد الجلوس', 'الركبه'),
                          const SizedBox(height: 10),

                          // آخر قياس منفرد
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 50),
                            child: MeasurementStyles.buildInput('الساق فتحه الرجل', measurements['الساق فتحه الرجل']!),
                          ),

                          // الملاحظات
                          MeasurementStyles.sectionTitle("ملاحظات"),
                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: 8),
                            child: TextFormField(
                              controller: noteController,
                              maxLines: 3,
                              textAlign: TextAlign.right,
                              textDirection: TextDirection.rtl,
                              keyboardType: TextInputType.text, // لوحة مفاتيح نصوص للملاحظات
                              decoration: MeasurementStyles.inputDecoration("ملاحظة"),
                            ),
                          ),
                          MeasurementStyles.buildInput("موعد التسليم", deliveryDateController),

                          const SizedBox(height: 20),
                          MeasurementStyles.saveButton(
                            _isEdit ? "تحديث القياس" : "حفظ القياس",
                            _saveMeasurement
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
              if (_isLoading)
                Container(
                  color: Colors.black.withOpacity(0.3),
                  child: const Center(
                    child: CircularProgressIndicator(),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
