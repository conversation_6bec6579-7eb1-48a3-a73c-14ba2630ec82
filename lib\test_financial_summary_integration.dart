// ملف اختبار لصفحة الإحصائيات المالية المحسنة
import 'package:flutter/material.dart';
import 'screens/financial/financial_summary_screen.dart';

class TestFinancialSummaryIntegration extends StatelessWidget {
  const TestFinancialSummaryIntegration({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'اختبار الإحصائيات المالية المحسنة',
      theme: ThemeData(
        primarySwatch: Colors.teal,
        fontFamily: 'Cairo',
      ),
      home: const FinancialSummaryScreen(
        workshopId: 1, // معرف تجريبي
        workshopName: 'معمل تجريبي - الإحصائيات المالية',
      ),
    );
  }
}

void main() {
  runApp(const TestFinancialSummaryIntegration());
}
