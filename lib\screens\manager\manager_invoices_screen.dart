import 'package:flutter/material.dart';
import '../../models/db_helper.dart';
import 'add_manager_invoice_screen.dart';
import '../../widgets/export_print_buttons.dart';

class ManagerInvoicesScreen extends StatefulWidget {
  const ManagerInvoicesScreen({super.key});

  @override
  State<ManagerInvoicesScreen> createState() => _ManagerInvoicesScreenState();
}

class _ManagerInvoicesScreenState extends State<ManagerInvoicesScreen> {
  List<Map<String, dynamic>> _invoices = [];
  List<Map<String, dynamic>> _filteredInvoices = [];
  bool _isLoading = true;
  final _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadInvoices();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      _filteredInvoices = _invoices.where((invoice) {
        final customerName = (invoice['customerName'] ?? '').toString().toLowerCase();
        final invoiceNumber = (invoice['invoiceNumber'] ?? '').toString().toLowerCase();
        final shopName = (invoice['shopName'] ?? '').toString().toLowerCase();
        return customerName.contains(query) ||
               invoiceNumber.contains(query) ||
               shopName.contains(query);
      }).toList();
    });
  }

  Future<void> _loadInvoices() async {
    setState(() => _isLoading = true);
    try {
      final invoices = await DBHelper.getAllManagerInvoices();
      setState(() {
        _invoices = invoices;
        _filteredInvoices = List.from(invoices);
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل الفواتير: $e'),
            backgroundColor: Colors.red[600],
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: Colors.grey[50],
        body: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : RefreshIndicator(
                onRefresh: _loadInvoices,
                child: Column(
                  children: [
                    // عنوان الصفحة
                    _buildPageHeader(),

                    // شريط البحث
                    if (_invoices.isNotEmpty) _buildSearchBar(),

                    // زر التصدير الشامل للفواتير
                    if (_invoices.isNotEmpty)
                      Container(
                        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        child: ExportPrintButtons(
                          data: _invoices,
                          type: 'invoices',
                          title: 'جميع فواتير نظام المدير',
                          showIndividual: false,
                          showBulk: true,
                        ),
                      ),

                    // قائمة الفواتير
                    Expanded(
                      child: _filteredInvoices.isEmpty
                          ? _buildEmptyState()
                          : _buildInvoicesList(),
                    ),
                  ],
                ),
              ),
        floatingActionButton: FloatingActionButton.extended(
          onPressed: _addNewInvoice,
          backgroundColor: Colors.purple[600],
          foregroundColor: Colors.white,
          icon: const Icon(Icons.add),
          label: const Text('إضافة فاتورة'),
          heroTag: 'manager_invoices_fab', // إضافة heroTag فريد
        ),
      ),
    );
  }

  Widget _buildPageHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      decoration: BoxDecoration(
        color: Colors.purple[700],
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
      ),
      child: SafeArea(
        child: Row(
          children: [
            const Expanded(
              child: Text(
                'إدارة الفواتير',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
            IconButton(
              icon: const Icon(Icons.refresh, color: Colors.white),
              onPressed: _loadInvoices,
              tooltip: 'تحديث',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'البحث باسم الزبون أو رقم الفاتورة أو اسم المحل...',
          prefixIcon: Icon(Icons.search, color: Colors.purple[600]),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.grey[300]!),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.purple[600]!, width: 2),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.grey[300]!),
          ),
          filled: true,
          fillColor: Colors.white,
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.receipt_long,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            _invoices.isEmpty ? 'لا توجد فواتير مسجلة' : 'لا توجد نتائج للبحث',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _invoices.isEmpty
                ? 'اضغط على "إضافة فاتورة" لبدء التسجيل'
                : 'جرب البحث بكلمات أخرى',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildInvoicesList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _filteredInvoices.length,
      itemBuilder: (context, index) {
        final invoice = _filteredInvoices[index];
        return _buildInvoiceCard(invoice);
      },
    );
  }

  Widget _buildInvoiceCard(Map<String, dynamic> invoice) {
    final isReady = (invoice['isReady'] ?? 0) == 1;
    final createdAt = DateTime.tryParse(invoice['createdAt'] ?? '') ?? DateTime.now();
    final formattedDate = '${createdAt.day}/${createdAt.month}/${createdAt.year}';

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: isReady ? Colors.green[200]! : Colors.orange[200]!,
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () => _editInvoice(invoice),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: isReady ? Colors.green[100] : Colors.orange[100],
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        isReady ? 'جاهزة' : 'غير جاهزة',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: isReady ? Colors.green[700] : Colors.orange[700],
                        ),
                      ),
                    ),
                    const Spacer(),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          formattedDate,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                        if (invoice['dayName'] != null)
                          Text(
                            invoice['dayName'],
                            style: TextStyle(
                              fontSize: 11,
                              color: Colors.purple[600],
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                      ],
                    ),
                    const SizedBox(width: 8),
                    PopupMenuButton<String>(
                      onSelected: (value) {
                        if (value == 'edit') {
                          _editInvoice(invoice);
                        } else if (value == 'toggle') {
                          _toggleInvoiceStatus(invoice);
                        } else if (value == 'delete') {
                          _deleteInvoice(invoice);
                        }
                      },
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'edit',
                          child: Row(
                            children: [
                              Icon(Icons.edit, size: 18),
                              SizedBox(width: 8),
                              Text('تعديل'),
                            ],
                          ),
                        ),
                        PopupMenuItem(
                          value: 'toggle',
                          child: Row(
                            children: [
                              Icon(isReady ? Icons.schedule : Icons.check, size: 18),
                              const SizedBox(width: 8),
                              Text(isReady ? 'تحديد كغير جاهزة' : 'تحديد كجاهزة'),
                            ],
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'delete',
                          child: Row(
                            children: [
                              Icon(Icons.delete, size: 18, color: Colors.red),
                              SizedBox(width: 8),
                              Text('حذف', style: TextStyle(color: Colors.red)),
                            ],
                          ),
                        ),
                      ],
                      child: Icon(Icons.more_vert, color: Colors.grey[600]),
                    ),
                  ],
                ),

                const SizedBox(height: 12),

                Row(
                  children: [
                    Icon(Icons.receipt, color: Colors.purple[600], size: 20),
                    const SizedBox(width: 8),
                    Text(
                      'رقم الفاتورة: ${invoice['invoiceNumber'] ?? ''}',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 8),

                Row(
                  children: [
                    Icon(Icons.person, color: Colors.grey[600], size: 18),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'الزبون: ${invoice['customerName'] ?? ''}',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[700],
                        ),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 4),

                Row(
                  children: [
                    Icon(Icons.store, color: Colors.grey[600], size: 18),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'المحل: ${invoice['shopName'] ?? ''}',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[700],
                        ),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 4),

                Row(
                  children: [
                    Icon(Icons.checkroom, color: Colors.grey[600], size: 18),
                    const SizedBox(width: 8),
                    Text(
                      'عدد الثياب: ${invoice['clothesCount'] ?? 0}',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[700],
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 12),

                // أزرار التصدير للفاتورة
                ExportPrintButtons(
                  data: invoice,
                  type: 'invoices',
                  title: 'فاتورة ${invoice['customerName']} - ${invoice['invoiceNumber']}',
                  showIndividual: true,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _addNewInvoice() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => const AddManagerInvoiceScreen(),
      ),
    );

    if (result == true) {
      _loadInvoices();
    }
  }

  Future<void> _editInvoice(Map<String, dynamic> invoice) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => AddManagerInvoiceScreen(invoiceToEdit: invoice),
      ),
    );

    if (result == true) {
      _loadInvoices();
    }
  }

  Future<void> _toggleInvoiceStatus(Map<String, dynamic> invoice) async {
    try {
      final newStatus = (invoice['isReady'] ?? 0) == 1 ? 0 : 1;
      await DBHelper.updateManagerInvoice(invoice['id'], {'isReady': newStatus});
      _loadInvoices();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              newStatus == 1
                  ? 'تم تحديد الفاتورة كجاهزة'
                  : 'تم تحديد الفاتورة كغير جاهزة',
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحديث حالة الفاتورة: $e'),
            backgroundColor: Colors.red[600],
          ),
        );
      }
    }
  }

  Future<void> _deleteInvoice(Map<String, dynamic> invoice) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف فاتورة رقم "${invoice['invoiceNumber']}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await DBHelper.deleteManagerInvoice(invoice['id']);
        _loadInvoices();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم حذف الفاتورة بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في حذف الفاتورة: $e'),
              backgroundColor: Colors.red[600],
            ),
          );
        }
      }
    }
  }
}
