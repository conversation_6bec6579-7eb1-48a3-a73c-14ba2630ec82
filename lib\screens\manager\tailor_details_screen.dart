import 'package:flutter/material.dart';
import '../../models/db_helper.dart';
import 'add_tailor_work_screen.dart';
import 'add_tailor_screen.dart';

class TailorDetailsScreen extends StatefulWidget {
  final Map<String, dynamic> tailor;

  const TailorDetailsScreen({
    super.key,
    required this.tailor,
  });

  @override
  State<TailorDetailsScreen> createState() => _TailorDetailsScreenState();
}

class _TailorDetailsScreenState extends State<TailorDetailsScreen> {
  List<Map<String, dynamic>> _works = [];
  bool _isLoading = true;
  Map<String, dynamic> _statistics = {};
  Map<String, dynamic> _currentTailor = {};

  @override
  void initState() {
    super.initState();
    _currentTailor = Map<String, dynamic>.from(widget.tailor);
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);
    try {
      final works = await DBHelper.getTailorWorks(widget.tailor['id']);
      final stats = await _calculateStatistics(works);

      setState(() {
        _works = works;
        _statistics = stats;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل البيانات: $e'),
            backgroundColor: Colors.red[600],
          ),
        );
      }
    }
  }

  Future<Map<String, dynamic>> _calculateStatistics(
      List<Map<String, dynamic>> works) async {
    double totalEarnings = 0;
    double totalExpenses = 0;
    int totalQuantity = 0;
    int totalCutQuantity = 0;
    int totalOtherQuantity = 0;

    for (final work in works) {
      totalEarnings += (work['totalPrice'] as double?) ?? 0.0;
      totalExpenses += (work['dailyExpense'] as double?) ?? 0.0;
      totalQuantity += (work['quantity'] as int?) ?? 0;
      totalCutQuantity += (work['cutQuantity'] as int?) ?? 0;
      totalOtherQuantity += (work['otherQuantity'] as int?) ?? 0;
    }

    return {
      'totalWorks': works.length,
      'totalEarnings': totalEarnings,
      'totalExpenses': totalExpenses,
      'netProfit': totalEarnings - totalExpenses,
      'totalQuantity': totalQuantity,
      'totalCutQuantity': totalCutQuantity,
      'totalOtherQuantity': totalOtherQuantity,
      'averagePerWork': works.isNotEmpty ? totalEarnings / works.length : 0.0,
    };
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: Colors.grey[50],
        appBar: AppBar(
          title: Text(
            'تفاصيل ${_currentTailor['name'] ?? widget.tailor['name']}',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          backgroundColor: Colors.purple[700],
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.white),
            onPressed: () => Navigator.pop(context),
          ),
          actions: [
            // IconButton(
            //   icon: const Icon(Icons.edit, color: Colors.white),
            //   onPressed: _editTailor,
            //   tooltip: 'تعديل الخياط',
            // ),
            IconButton(
              icon: const Icon(Icons.refresh, color: Colors.white),
              onPressed: _loadData,
              tooltip: 'تحديث',
            ),
          ],
        ),
        body: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : RefreshIndicator(
                onRefresh: _loadData,
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      // بطاقة معلومات الخياط
                      _buildTailorInfoCard(),

                      const SizedBox(height: 16),

                      // بطاقة الإحصائيات
                      _buildStatisticsCard(),

                      const SizedBox(height: 16),

                      // قائمة الأعمال
                      _buildWorksSection(),
                    ],
                  ),
                ),
              ),
        floatingActionButton: FloatingActionButton.extended(
          onPressed: _addNewWork,
          backgroundColor: Colors.purple[600],
          foregroundColor: Colors.white,
          icon: const Icon(Icons.add),
          label: const Text('إضافة عمل جديد'),
          heroTag: 'tailor_details_fab', // إضافة heroTag فريد
        ),
      ),
    );
  }

  Widget _buildTailorInfoCard() {
    final tailor = _currentTailor;
    final isCutter = (tailor['isCutter'] ?? 0) == 1;
    final hasOtherWork = (tailor['hasOtherWork'] ?? 0) == 1;

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            colors: [Colors.purple[600]!, Colors.purple[400]!],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.person, color: Colors.white, size: 28),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    tailor['name'] ?? '',
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            _buildInfoRow(Icons.phone, 'الجوال', tailor['phone'] ?? 'غير محدد'),
            _buildInfoRow(
                Icons.work, 'نوع الشغل', tailor['workType'] ?? 'غير محدد'),
            // _buildInfoRow(Icons.attach_money, 'سعر القطعة', '${(tailor['pricePerPiece'] ?? 0.0).toStringAsFixed(2)} ريال'),
            _buildInfoRow(
              Icons.attach_money,
              'سعر القطعة',
              '${(double.tryParse(tailor['pricePerPiece'].toString())?.toInt() ?? 0)} ريال',
            ),

            if (isCutter)
              _buildInfoRow(Icons.content_cut, 'سعر القصة',
                  '${(tailor['cutPrice'] ?? 0.0).toStringAsFixed(2)} ريال'),

            if (hasOtherWork) ...[
              _buildInfoRow(Icons.category, 'العمل الإضافي',
                  tailor['otherWorkType'] ?? 'غير محدد'),
              _buildInfoRow(Icons.monetization_on, 'سعر العمل الإضافي',
                  '${(tailor['otherWorkPrice'] ?? 0.0).toStringAsFixed(2)} ريال'),
            ],

            if (tailor['address'] != null &&
                tailor['address'].toString().isNotEmpty)
              _buildInfoRow(Icons.location_on, 'العنوان', tailor['address']),

            if (tailor['notes'] != null &&
                tailor['notes'].toString().isNotEmpty)
              _buildInfoRow(Icons.note, 'ملاحظات', tailor['notes']),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(icon, color: Colors.white70, size: 20),
          const SizedBox(width: 12),
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatisticsCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.analytics, color: Colors.purple[600], size: 24),
                const SizedBox(width: 12),
                Text(
                  'الإحصائيات',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[800],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'إجمالي الأعمال',
                    '${_statistics['totalWorks'] ?? 0}',
                    Icons.work,
                    Colors.blue,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'إجمالي القطع',
                    '${_statistics['totalQuantity'] ?? 0}',
                    Icons.format_list_numbered,
                    Colors.green,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // إضافة إحصائيات مفصلة للقطع المقصوصة والأعمال الإضافية
            if ((_statistics['totalCutQuantity'] ?? 0) > 0 ||
                (_statistics['totalOtherQuantity'] ?? 0) > 0)
              Row(
                children: [
                  if ((_statistics['totalCutQuantity'] ?? 0) > 0)
                    Expanded(
                      child: _buildStatCard(
                        'قطع مقصوصة',
                        '${_statistics['totalCutQuantity'] ?? 0}',
                        Icons.content_cut,
                        Colors.orange,
                      ),
                    ),
                  if ((_statistics['totalCutQuantity'] ?? 0) > 0 &&
                      (_statistics['totalOtherQuantity'] ?? 0) > 0)
                    const SizedBox(width: 12),
                  if ((_statistics['totalOtherQuantity'] ?? 0) > 0)
                    Expanded(
                      child: _buildStatCard(
                        'أعمال إضافية',
                        '${_statistics['totalOtherQuantity'] ?? 0}',
                        Icons.category,
                        Colors.purple,
                      ),
                    ),
                ],
              ),

            const SizedBox(height: 12),

            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'إجمالي الأرباح',
                    '${(_statistics['totalEarnings'] ?? 0.0).toInt()} ر.س', // إزالة الفاصلة العشرية
                    Icons.trending_up,
                    Colors.green,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'إجمالي المصروفات',
                    '${(_statistics['totalExpenses'] ?? 0.0).toInt()} ر.س', // إزالة الفاصلة العشرية
                    Icons.trending_down,
                    Colors.red,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // عرض صافي الربح أو الدين حسب الحالة
            _buildProfitOrDebtCard(),

            // إضافة متوسط الربح لكل عمل إذا كان هناك أعمال
            // if ((_statistics['totalWorks'] ?? 0) > 0)
            //   Column(
            //     children: [
            //       const SizedBox(height: 12),
            //       _buildStatCard(
            //         'متوسط الربح لكل عمل',
            //         '${(_statistics['averagePerWork'] ?? 0.0).toInt()} ر.س', // إزالة الفاصلة العشرية
            //         Icons.trending_up,
            //         Colors.indigo,
            //         isFullWidth: true,
            //       ),
            //     ],
            //   ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color,
      {bool isFullWidth = false}) {
    return Container(
      width: isFullWidth ? double.infinity : null,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // دالة عرض صافي الربح أو الدين
  Widget _buildProfitOrDebtCard() {
    final netProfit = (_statistics['netProfit'] ?? 0.0).toDouble();
    final isDebt = netProfit < 0;
    final absoluteValue = netProfit.abs();

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDebt
            ? Colors.red.withOpacity(0.1)
            : Colors.green.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isDebt
              ? Colors.red.withOpacity(0.3)
              : Colors.green.withOpacity(0.3),
          width: 2,
        ),
      ),
      child: Column(
        children: [
          Icon(
            isDebt ? Icons.warning : Icons.account_balance_wallet,
            color: isDebt ? Colors.red : Colors.green,
            size: 32,
          ),
          const SizedBox(height: 12),
          Text(
            isDebt ? 'أنت مديون' : 'صافي الربح',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[700],
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            '${absoluteValue.toInt()} ر.س', // إزالة الفاصلة العشرية
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: isDebt ? Colors.red : Colors.green,
            ),
            textAlign: TextAlign.center,
          ),
          if (isDebt)
            Column(
              children: [
                const SizedBox(height: 8),
                Text(
                  'يجب تسديد هذا المبلغ',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.red[700],
                    fontStyle: FontStyle.italic,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
        ],
      ),
    );
  }

  Widget _buildWorksSection() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.list, color: Colors.purple[600], size: 24),
                const SizedBox(width: 12),
                Text(
                  'الأعمال (${_works.length})',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[800],
                  ),
                ),
                const Spacer(),
                TextButton.icon(
                  onPressed: _addNewWork,
                  icon: const Icon(Icons.add, size: 18),
                  label: const Text('إضافة'),
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.purple[600],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_works.isEmpty)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(32),
                child: Column(
                  children: [
                    Icon(
                      Icons.work_off,
                      size: 64,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'لا توجد أعمال مسجلة',
                      style: TextStyle(
                        fontSize: 18,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'اضغط على "إضافة عمل جديد" لبدء التسجيل',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[500],
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              )
            else
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _works.length,
                separatorBuilder: (context, index) =>
                    const SizedBox(height: 12),
                itemBuilder: (context, index) {
                  final work = _works[index];
                  return _buildWorkCard(work);
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildWorkCard(Map<String, dynamic> work) {
    final quantity = work['quantity'] ?? 0;
    final cutQuantity = work['cutQuantity'] ?? 0;
    final otherQuantity = work['otherQuantity'] ?? 0;
    final totalPrice = (work['totalPrice'] ?? 0.0).toDouble();
    final dailyExpense = (work['dailyExpense'] ?? 0.0).toDouble();
    final executionDate = work['executionDate'] ?? '';

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () => _editWork(work),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.purple[100],
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        work['workType'] ?? '',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Colors.purple[700],
                        ),
                      ),
                    ),
                    const Spacer(),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          executionDate,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                        if (work['dayName'] != null)
                          Text(
                            work['dayName'],
                            style: TextStyle(
                              fontSize: 11,
                              color: Colors.purple[600],
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                      ],
                    ),
                    const SizedBox(width: 8),
                    PopupMenuButton<String>(
                      onSelected: (value) {
                        if (value == 'edit') {
                          _editWork(work);
                        } else if (value == 'delete') {
                          _deleteWork(work);
                        }
                      },
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'edit',
                          child: Row(
                            children: [
                              Icon(Icons.edit, size: 18),
                              SizedBox(width: 8),
                              Text('تعديل'),
                            ],
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'delete',
                          child: Row(
                            children: [
                              Icon(Icons.delete, size: 18, color: Colors.red),
                              SizedBox(width: 8),
                              Text('حذف', style: TextStyle(color: Colors.red)),
                            ],
                          ),
                        ),
                      ],
                      child: Icon(Icons.more_vert, color: Colors.grey[600]),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: _buildWorkDetail('القطع', '$quantity'),
                    ),
                    if (cutQuantity > 0)
                      Expanded(
                        child: _buildWorkDetail('القصة', '$cutQuantity'),
                      ),
                    if (otherQuantity > 0)
                      Expanded(
                        child: _buildWorkDetail('إضافي', '$otherQuantity'),
                      ),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: _buildWorkDetail('الإجمالي',
                          '${totalPrice.toInt()} ر.س'), // إزالة الفاصلة العشرية
                    ),
                    if (dailyExpense > 0)
                      Expanded(
                        child: _buildWorkDetail('المصروف',
                            '${dailyExpense.toInt()} ر.س'), // إزالة الفاصلة العشرية
                      ),
                  ],
                ),
                if (work['notes'] != null &&
                    work['notes'].toString().isNotEmpty) ...[
                  const SizedBox(height: 12),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.grey[50],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      work['notes'],
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[700],
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildWorkDetail(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Future<void> _addNewWork() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => AddTailorWorkScreen(tailor: _currentTailor),
      ),
    );

    if (result == true) {
      _loadData();
    }
  }

  Future<void> _editWork(Map<String, dynamic> work) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => AddTailorWorkScreen(
          tailor: _currentTailor,
          workToEdit: work,
        ),
      ),
    );

    if (result == true) {
      _loadData();
    }
  }

  Future<void> _editTailor() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => AddTailorScreen(tailorToEdit: _currentTailor),
      ),
    );

    if (result == true) {
      setState(() => _isLoading = true);

      try {
        // جلب بيانات الخياط المحدثة
        final updatedTailor =
            await DBHelper.getTailorById(_currentTailor['id']);

        if (updatedTailor != null && mounted) {
          // تحديث بيانات الخياط المحلية
          setState(() {
            _currentTailor.clear();
            _currentTailor.addAll(updatedTailor);
            widget.tailor.clear();
            widget.tailor.addAll(updatedTailor);
          });

          // تحديث الأعمال والإحصائيات فورياً
          await _loadData();

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: const Row(
                  children: [
                    Icon(Icons.check_circle, color: Colors.white),
                    SizedBox(width: 8),
                    Text('تم تحديث بيانات الخياط بنجاح'),
                  ],
                ),
                backgroundColor: Colors.green[600],
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                duration: const Duration(seconds: 3),
              ),
            );
          }
        }
      } catch (e) {
        setState(() => _isLoading = false);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في تحديث البيانات: $e'),
              backgroundColor: Colors.red[600],
            ),
          );
        }
      }
    }
  }

  Future<void> _deleteWork(Map<String, dynamic> work) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل أنت متأكد من حذف هذا العمل؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await DBHelper.deleteTailorWork(work['id']);
        _loadData();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم حذف العمل بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في حذف العمل: $e'),
              backgroundColor: Colors.red[600],
            ),
          );
        }
      }
    }
  }
}
