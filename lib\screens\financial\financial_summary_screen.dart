import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:databasflutter/models/db_helper.dart';
import '../../widgets/export_print_buttons.dart';

class FinancialSummaryScreen extends StatefulWidget {
  final int workshopId;
  final String workshopName;

  const FinancialSummaryScreen({
    super.key,
    required this.workshopId,
    required this.workshopName,
  });

  @override
  State<FinancialSummaryScreen> createState() => _FinancialSummaryScreenState();
}

class _FinancialSummaryScreenState extends State<FinancialSummaryScreen> {
  bool _isLoading = true;

  // بيانات الورشة
  double pricePerPiece = 0.0;
  double qassasPrice = 0.0;
  String otherTypeName = '';
  double otherTypePrice = 0.0;
  bool isQassas = false;

  // ملخص الأرقام
  int totalQuantity = 0;
  double totalIncomeFromProduction = 0.0;

  int totalCutQuantity = 0;
  double totalIncomeFromCuts = 0.0;

  int totalOtherCount = 0;
  double totalIncomeFromOthers = 0.0;

  double totalExpense = 0.0;

  double grossIncome = 0.0;
  double netProfitOrDebt = 0.0;
  bool isInDebt = false;

  // بيانات النظام المالي الجديدة
  double totalPenalties = 0.0;
  double totalExtraWork = 0.0;
  double initialBalance = 0.0;
  double finalBalance = 0.0;

  @override
  void initState() {
    super.initState();
    _calculateSummary();
  }

  Future<void> _calculateSummary() async {
    // 1. جلب بيانات الورشة المحلية
    final workshopMap = await DBHelper.getWorkshopById(widget.workshopId);
    if (workshopMap == null) {
      setState(() { _isLoading = false; });
      return;
    }
    
    final map = workshopMap;
    pricePerPiece  = (map['pricePerPiece'] as num? ?? 0).toDouble();
    qassasPrice    = (map['qassasPrice']   as num? ?? 0).toDouble();
    otherTypeName  = map['otherTypeName']  as String? ?? '';
    otherTypePrice = (map['otherTypePrice'] as num? ?? 0).toDouble();
    isQassas       = (map['isQassas']     as int? ?? 0) == 1;

    final hasOtherType = otherTypeName.isNotEmpty;

    // 2. جلب الأعمال المرتبطة
    final works = await DBHelper.getWorksForWorkshop(widget.workshopId);

    // متغيرات للتجميع
    int qty      = 0;
    int cutQty   = 0;
    int otherCnt = 0;
    double exp   = 0.0;

    for (var w in works) {
      qty    += w['quantity'] as int? ?? 0;
      exp    += double.tryParse(w['expense'].toString()) ?? 0.0;
      if (isQassas) {
        cutQty += w['cutQuantity'] as int? ?? 0;
      }
      if (hasOtherType) {
        otherCnt += w['otherCount'] as int? ?? 0;
      }
    }

    // 3. جلب البيانات المالية الإضافية
    await _loadFinancialData();

    // 4. حساب المجاميع
    totalQuantity             = qty;
    totalIncomeFromProduction = qty * pricePerPiece;

    totalCutQuantity   = cutQty;
    totalIncomeFromCuts = cutQty * qassasPrice;

    totalOtherCount       = otherCnt;
    totalIncomeFromOthers = otherCnt * otherTypePrice;

    totalExpense = exp;

    grossIncome      = totalIncomeFromProduction
                     + totalIncomeFromCuts
                     + totalIncomeFromOthers;
    
    // حساب الرصيد النهائي مع النظام المالي
    final totalIncome = grossIncome + totalExtraWork + initialBalance;
    final totalExpenses = totalExpense + totalPenalties;
    final net = totalIncome - totalExpenses;
    
    finalBalance = net;
    netProfitOrDebt = net.abs();
    isInDebt = net < 0;

    setState(() { _isLoading = false; });
  }

  Future<void> _loadFinancialData() async {
    try {
      // جلب الغرامات
      final penalties = await DBHelper.getPenaltiesForWorkshop(widget.workshopId);
      totalPenalties = penalties.fold(0.0, (sum, penalty) => 
        sum + (penalty['amount'] as num? ?? 0).toDouble());

      // جلب الأعمال الإضافية
      final extraWorks = await DBHelper.getExtraWorksForWorkshop(widget.workshopId);
      totalExtraWork = extraWorks.fold(0.0, (sum, work) => 
        sum + (work['amount'] as num? ?? 0).toDouble());

      // جلب الرصيد المرحل
      final balance = await DBHelper.getInitialBalanceForWorkshop(widget.workshopId);
      initialBalance = (balance?['amount'] as num? ?? 0).toDouble();
    } catch (e) {
      // في حالة عدم وجود الجداول أو الدوال، نضع القيم الافتراضية
      totalPenalties = 0.0;
      totalExtraWork = 0.0;
      initialBalance = 0.0;
    }
  }

  /// صياغة الرقم بدون الكسور الزائدة
  String formatNum(double v) {
    if (v == v.roundToDouble()) return v.toInt().toString();
    String s = v.toStringAsFixed(2);
    return s.replaceAll(RegExp(r'\.?0+$'), '');
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;

    // شروط العرض
    final showCuts   = isQassas && totalCutQuantity > 0;
    final showOthers = otherTypeName.isNotEmpty && totalOtherCount > 0;

    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: Colors.green.shade50,
        body: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : Column(
                children: [
                  // هيدر محسن
                  _buildEnhancedHeader(screenHeight),

                  // إحصائيات
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                      child: SingleChildScrollView(
                        child: Column(
                          children: [
                            // كارد الحالة المالية الجديد
                            _buildFinancialStatusCard(),
                            
                            const SizedBox(height: 16),
                            
                            // إحصائيات الإنتاج
                            _buildSectionTitle('إحصائيات الإنتاج'),
                            const SizedBox(height: 8),
                            
                            _buildStatCard(
                              title: 'إجمالي القطع المنتجة',
                              value: totalQuantity.toString(),
                              icon: Icons.confirmation_number,
                              color: Colors.blue.shade100,
                            ),
                            const SizedBox(height: 8),
                            _buildStatCard(
                              title: 'دخل الإنتاج',
                              value: '${formatNum(totalIncomeFromProduction)} ريال',
                              icon: Icons.production_quantity_limits,
                              color: Colors.lightBlue.shade100,
                            ),

                            // بطاقات القصّ
                            if (showCuts) ...[
                              const SizedBox(height: 8),
                              _buildStatCard(
                                title: 'عدد الثياب المقصوصة',
                                value: totalCutQuantity.toString(),
                                icon: Icons.content_cut,
                                color: Colors.orange.shade100,
                              ),
                              const SizedBox(height: 8),
                              _buildStatCard(
                                title: 'دخل القصّ',
                                value: '${formatNum(totalIncomeFromCuts)} ريال',
                                icon: Icons.monetization_on,
                                color: Colors.deepOrange.shade100,
                              ),
                            ],

                            // بطاقات الأنواع الأخرى
                            if (showOthers) ...[
                              const SizedBox(height: 8),
                              _buildStatCard(
                                title: 'عدد $otherTypeName',
                                value: totalOtherCount.toString(),
                                icon: Icons.category,
                                color: Colors.purple.shade100,
                              ),
                              const SizedBox(height: 8),
                              _buildStatCard(
                                title: 'دخل $otherTypeName',
                                value: '${formatNum(totalIncomeFromOthers)} ريال',
                                icon: Icons.monetization_on_outlined,
                                color: Colors.purple.shade200,
                              ),
                            ],

                            const SizedBox(height: 16),
                            
                            // النظام المالي
                            _buildSectionTitle('النظام المالي'),
                            const SizedBox(height: 8),
                            
                            if (initialBalance != 0) ...[
                              _buildStatCard(
                                title: 'الرصيد المرحل',
                                value: '${formatNum(initialBalance)} ريال',
                                icon: Icons.account_balance_wallet,
                                color: Colors.cyan.shade100,
                              ),
                              const SizedBox(height: 8),
                            ],
                            
                            if (totalExtraWork > 0) ...[
                              _buildStatCard(
                                title: 'الأعمال الإضافية',
                                value: '${formatNum(totalExtraWork)} ريال',
                                icon: Icons.add_business,
                                color: Colors.green.shade100,
                              ),
                              const SizedBox(height: 8),
                            ],
                            
                            if (totalPenalties > 0) ...[
                              _buildStatCard(
                                title: 'الغرامات',
                                value: '${formatNum(totalPenalties)} ريال',
                                icon: Icons.warning,
                                color: Colors.red.shade100,
                              ),
                              const SizedBox(height: 8),
                            ],
                            
                            _buildStatCard(
                              title: 'إجمالي المصروفات',
                              value: '${formatNum(totalExpense)} ريال',
                              icon: Icons.money_off,
                              color: Colors.red.shade100,
                            ),

                            const SizedBox(height: 16),

                            // أزرار التصدير والطباعة
                            ExportPrintButtons(
                              data: {
                                'workshopName': widget.workshopName,
                                'totalQuantity': totalQuantity,
                                'totalIncomeFromProduction': totalIncomeFromProduction,
                                'totalCutQuantity': totalCutQuantity,
                                'totalIncomeFromCuts': totalIncomeFromCuts,
                                'totalOtherCount': totalOtherCount,
                                'totalIncomeFromOthers': totalIncomeFromOthers,
                                'otherTypeName': otherTypeName,
                                'totalExpense': totalExpense,
                                'totalPenalties': totalPenalties,
                                'totalExtraWork': totalExtraWork,
                                'initialBalance': initialBalance,
                                'grossIncome': grossIncome,
                                'finalBalance': finalBalance,
                                'netProfitOrDebt': netProfitOrDebt,
                                'isInDebt': isInDebt,
                                'showCuts': showCuts,
                                'showOthers': showOthers,
                              },
                              type: 'statistics',
                              title: 'الإحصائيات المالية - معمل ${widget.workshopName}',
                              showIndividual: true,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),

                  // إعلان
                  Container(
                    height: 60,
                    margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade300,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.teal.shade200),
                    ),
                    child: Center(
                      child: Text(
                        'النظام المالي المتكامل - إحصائيات شاملة',
                        style: GoogleFonts.cairo(fontSize: 14, color: Colors.black54),
                      ),
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  Widget _buildEnhancedHeader(double screenHeight) {
    return Container(
      height: screenHeight * 0.25,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: isInDebt 
            ? [Colors.red.shade600, Colors.red.shade400]
            : [Colors.teal, Colors.green],
          begin: Alignment.topRight,
          end: Alignment.bottomLeft,
        ),
        borderRadius: const BorderRadius.vertical(bottom: Radius.circular(40)),
      ),
      padding: const EdgeInsets.only(top: 60, right: 20, left: 20, bottom: 20),
      child: Column(
        children: [
          Row(
            children: [
              GestureDetector(
                onTap: () => Navigator.pop(context),
                child: Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(Icons.arrow_back, color: Colors.white),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'النظام المالي المتكامل',
                  style: GoogleFonts.cairo(
                    color: Colors.white,
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            'معمل: ${widget.workshopName}',
            style: GoogleFonts.cairo(
              color: Colors.white.withOpacity(0.9),
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFinancialStatusCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: isInDebt 
            ? [Colors.red.shade100, Colors.red.shade50]
            : [Colors.green.shade100, Colors.green.shade50],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: (isInDebt ? Colors.red : Colors.green).withOpacity(0.2),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(
            isInDebt ? Icons.warning_amber : Icons.account_balance_wallet,
            size: 48,
            color: isInDebt ? Colors.red.shade600 : Colors.green.shade600,
          ),
          const SizedBox(height: 12),
          Text(
            isInDebt ? 'لديك مديونية' : 'رصيدك موجب',
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: isInDebt ? Colors.red.shade700 : Colors.green.shade700,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '${isInDebt ? '-' : '+'}${formatNum(netProfitOrDebt)} ريال',
            style: GoogleFonts.cairo(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: isInDebt ? Colors.red.shade800 : Colors.green.shade800,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            isInDebt 
              ? 'تحتاج لتسديد المديونية' 
              : 'وضعك المالي ممتاز',
            style: GoogleFonts.cairo(
              fontSize: 14,
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.teal.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.teal.shade200),
      ),
      child: Text(
        title,
        style: GoogleFonts.cairo(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: Colors.teal.shade700,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.95),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.teal.withOpacity(0.1), 
            blurRadius: 6, 
            offset: const Offset(0, 2)
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Icon(icon, color: Colors.teal, size: 24),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title, 
                  style: GoogleFonts.cairo(
                    fontSize: 14, 
                    fontWeight: FontWeight.bold,
                    color: Colors.grey.shade700,
                  )
                ),
                const SizedBox(height: 4),
                Text(
                  value, 
                  style: GoogleFonts.cairo(
                    fontSize: 18, 
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  )
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
