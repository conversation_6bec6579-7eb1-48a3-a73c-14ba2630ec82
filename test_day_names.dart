import 'lib/utils/date_utils.dart';

void main() {
  // اختبار أسماء الأيام
  print('اليوم الحالي: ${DateTime.now().weekday}');
  print('اسم اليوم الحالي: ${AppDateUtils.getCurrentDayName()}');
  
  // اختبار جميع الأيام
  for (int i = 1; i <= 7; i++) {
    final testDate = DateTime(2024, 1, i); // أسبوع في يناير 2024
    print('اليوم $i (${testDate.weekday}): ${AppDateUtils.getDayName(testDate)}');
  }
  
  // اختبار الرسائل
  print('\nاختبار الرسائل:');
  print('رسالة العمل: ${AppDateUtils.getWorkAddedMessage()}');
  print('رسالة المعمل: ${AppDateUtils.getWorkshopAddedMessage()}');
  print('رسالة الفاتورة: ${AppDateUtils.getInvoiceAddedMessage()}');
  print('رسالة المقاس: ${AppDateUtils.getMeasurementAddedMessage()}');
  print('رسالة الخياط: ${AppDateUtils.getTailorAddedMessage()}');
}
