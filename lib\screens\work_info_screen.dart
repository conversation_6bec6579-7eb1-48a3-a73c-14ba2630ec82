// lib/screens/work_info_screen.dart

import 'package:flutter/material.dart';
import '../models/db_helper.dart';
import '../measurements/measurement_styles.dart';
import '../widgets/export_print_buttons.dart';
import '../utils/number_utils.dart';

class WorkInfoScreen extends StatefulWidget {
  final Map<String, dynamic> work;

  const WorkInfoScreen({
    super.key,
    required this.work,
  });

  @override
  State<WorkInfoScreen> createState() => _WorkInfoScreenState();
}

class _WorkInfoScreenState extends State<WorkInfoScreen> {
  bool _isLoading = false;
  Map<String, dynamic>? _workshop;

  @override
  void initState() {
    super.initState();
    _loadWorkshopInfo();
  }

  Future<void> _loadWorkshopInfo() async {
    setState(() => _isLoading = true);

    try {
      _workshop = await DBHelper.getWorkshopById(widget.work['workshopId']);
    } catch (e) {
      _showErrorSnackBar('خطأ في تحميل بيانات المعمل: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: MeasurementStyles.errorColor,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: MeasurementStyles.backgroundColor,
      appBar: AppBar(
        title: const Text(
          'تفاصيل العمل',
          style: TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 20,
          ),
        ),
        backgroundColor: MeasurementStyles.primaryColor,
        foregroundColor: MeasurementStyles.whiteColor,
        elevation: 0,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // معلومات العمل الأساسية
                  _buildBasicInfoCard(),

                  const SizedBox(height: 16),

                  // تفاصيل الكميات والأسعار
                  _buildQuantityPriceCard(),

                  const SizedBox(height: 16),

                  // الحسابات المالية
                  _buildFinancialCard(),

                  const SizedBox(height: 16),

                  // معلومات المعمل
                  if (_workshop != null) _buildWorkshopInfoCard(),

                  const SizedBox(height: 16),

                  // معلومات إضافية
                  _buildAdditionalInfoCard(),

                  const SizedBox(height: 16),

                  // أزرار التصدير والطباعة
                  ExportPrintButtons(
                    data: widget.work,
                    type: 'works',
                    title: 'عمل ${widget.work['workType']} - ${widget.work['date']}',
                    showIndividual: true,
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildBasicInfoCard() {
    final workDate = DateTime.tryParse(widget.work['date'] ?? '');

    return MeasurementStyles.modernCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: MeasurementStyles.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.work_rounded,
                  color: MeasurementStyles.primaryColor,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'معلومات العمل',
                      style: MeasurementStyles.cardSubtitleStyle,
                    ),
                    Text(
                      widget.work['workType'] ?? 'غير محدد',
                      style: MeasurementStyles.cardTitleStyle,
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          _buildInfoRow(
            'نوع العمل',
            widget.work['workType'] ?? 'غير محدد',
            Icons.category_rounded,
          ),

          if (workDate != null) ...[
            const SizedBox(height: 12),
            _buildInfoRow(
              'تاريخ العمل',
              '${workDate.day}/${workDate.month}/${workDate.year}',
              Icons.calendar_today_rounded,
            ),
          ],

          if (widget.work['notes'] != null && (widget.work['notes'] as String).isNotEmpty) ...[
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: MeasurementStyles.secondaryColor,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Row(
                    children: [
                      Icon(
                        Icons.note_rounded,
                        color: MeasurementStyles.primaryColor,
                        size: 16,
                      ),
                      SizedBox(width: 8),
                      Text(
                        'ملاحظات',
                        style: MeasurementStyles.cardSubtitleStyle,
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    widget.work['notes'] ?? '',
                    style: MeasurementStyles.normalTextStyle,
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildQuantityPriceCard() {
    final quantity = (widget.work['quantity'] as num?)?.toInt() ?? 0;
    final cutQuantity = (widget.work['cutQuantity'] as num?)?.toInt() ?? 0;
    final otherCount = (widget.work['otherCount'] as num?)?.toInt() ?? 0;
    final pricePerPiece = (widget.work['pricePerPiece'] as num?)?.toDouble() ?? 0.0;
    final netQuantity = quantity - cutQuantity;

    return MeasurementStyles.modernCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          MeasurementStyles.sectionTitle(
            'الكميات والأسعار',
            icon: Icons.calculate_rounded,
          ),

          const SizedBox(height: 16),

          // الكمية الإجمالية
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: MeasurementStyles.primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.inventory_rounded,
                  color: MeasurementStyles.primaryColor,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'الكمية الإجمالية',
                        style: MeasurementStyles.cardSubtitleStyle,
                      ),
                      Text(
                        '$quantity قطعة',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: MeasurementStyles.primaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 12),

          // الكمية المقطوعة
          if (cutQuantity > 0) ...[
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: MeasurementStyles.warningColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.content_cut_rounded,
                    color: MeasurementStyles.warningColor,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'الكمية المقطوعة',
                          style: MeasurementStyles.cardSubtitleStyle,
                        ),
                        Text(
                          '$cutQuantity قطعة',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: MeasurementStyles.warningColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),
          ],

          // الكمية الصافية
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: MeasurementStyles.successColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.check_circle_rounded,
                  color: MeasurementStyles.successColor,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'الكمية الصافية',
                        style: MeasurementStyles.cardSubtitleStyle,
                      ),
                      Text(
                        '$netQuantity قطعة',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: MeasurementStyles.successColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 12),

          // سعر القطعة
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: MeasurementStyles.secondaryColor,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Row(
                  children: [
                    Icon(
                      Icons.attach_money_rounded,
                      color: MeasurementStyles.primaryColor,
                      size: 20,
                    ),
                    SizedBox(width: 12),
                    Text(
                      'سعر القطعة',
                      style: MeasurementStyles.normalTextStyle,
                    ),
                  ],
                ),
                Text(
                  '${NumberUtils.formatIntegerDisplay(pricePerPiece)} ريال',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: MeasurementStyles.primaryColor,
                  ),
                ),
              ],
            ),
          ),

          // العدد الإضافي إن وجد
          if (otherCount > 0) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: MeasurementStyles.primaryLightColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.add_circle_rounded,
                    color: MeasurementStyles.primaryLightColor,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'العدد الإضافي',
                          style: MeasurementStyles.cardSubtitleStyle,
                        ),
                        Text(
                          '$otherCount قطعة',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: MeasurementStyles.primaryLightColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildFinancialCard() {
    final quantity = (widget.work['quantity'] as num?)?.toInt() ?? 0;
    final cutQuantity = (widget.work['cutQuantity'] as num?)?.toInt() ?? 0;
    final pricePerPiece = (widget.work['pricePerPiece'] as num?)?.toDouble() ?? 0.0;
    final expense = (widget.work['expense'] as num?)?.toDouble() ?? 0.0;

    final netQuantity = quantity - cutQuantity;
    final totalIncome = netQuantity * pricePerPiece;
    final netProfit = totalIncome - expense;

    return MeasurementStyles.modernCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          MeasurementStyles.sectionTitle(
            'الحسابات المالية',
            icon: Icons.account_balance_wallet_rounded,
          ),

          const SizedBox(height: 16),

          // إجمالي الدخل
          _buildFinancialRow(
            'إجمالي الدخل',
            '${NumberUtils.formatIntegerDisplay(totalIncome)} ريال',
            Icons.trending_up_rounded,
            MeasurementStyles.successColor,
          ),

          const SizedBox(height: 12),

          // المصروفات
          _buildFinancialRow(
            'المصروفات',
            '${NumberUtils.formatIntegerDisplay(expense)} ريال',
            Icons.trending_down_rounded,
            MeasurementStyles.errorColor,
          ),

          const SizedBox(height: 16),

          // صافي الربح
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: netProfit >= 0
                  ? MeasurementStyles.successColor.withOpacity(0.1)
                  : MeasurementStyles.errorColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: netProfit >= 0
                    ? MeasurementStyles.successColor.withOpacity(0.3)
                    : MeasurementStyles.errorColor.withOpacity(0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  netProfit >= 0
                      ? Icons.account_balance_wallet_rounded
                      : Icons.warning_rounded,
                  color: netProfit >= 0
                      ? MeasurementStyles.successColor
                      : MeasurementStyles.errorColor,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'صافي الربح',
                        style: MeasurementStyles.cardSubtitleStyle,
                      ),
                      Text(
                        '${NumberUtils.formatIntegerDisplay(netProfit.abs())} ريال',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.w700,
                          color: netProfit >= 0
                              ? MeasurementStyles.successColor
                              : MeasurementStyles.errorColor,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: netProfit >= 0
                        ? MeasurementStyles.successColor
                        : MeasurementStyles.errorColor,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    netProfit >= 0 ? 'ربح' : 'خسارة',
                    style: const TextStyle(
                      color: MeasurementStyles.whiteColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWorkshopInfoCard() {
    return MeasurementStyles.modernCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          MeasurementStyles.sectionTitle(
            'معلومات المعمل',
            icon: Icons.factory_rounded,
          ),

          const SizedBox(height: 16),

          _buildInfoRow(
            'اسم المعمل',
            _workshop!['name'] ?? 'غير محدد',
            Icons.factory_rounded,
          ),

          const SizedBox(height: 12),

          _buildInfoRow(
            'رقم صاحب المعمل',
            _workshop!['ownerPhone'] ?? 'غير محدد',
            Icons.phone_rounded,
          ),

          const SizedBox(height: 12),

          _buildInfoRow(
            'نوع العمل',
            _workshop!['workType'] ?? 'غير محدد',
            Icons.work_rounded,
          ),
        ],
      ),
    );
  }

  Widget _buildAdditionalInfoCard() {
    final createdAt = DateTime.tryParse(widget.work['createdAt'] ?? '');
    final updatedAt = DateTime.tryParse(widget.work['updatedAt'] ?? '');

    return MeasurementStyles.modernCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          MeasurementStyles.sectionTitle(
            'معلومات إضافية',
            icon: Icons.info_outline_rounded,
          ),

          const SizedBox(height: 16),

          _buildInfoRow(
            'معرف العمل',
            '#${widget.work['id'] ?? 'غير محدد'}',
            Icons.tag_rounded,
          ),

          if (createdAt != null) ...[
            const SizedBox(height: 12),
            _buildInfoRow(
              'تاريخ الإنشاء',
              '${createdAt.day}/${createdAt.month}/${createdAt.year}',
              Icons.calendar_today_rounded,
            ),
          ],

          if (updatedAt != null) ...[
            const SizedBox(height: 12),
            _buildInfoRow(
              'آخر تحديث',
              '${updatedAt.day}/${updatedAt.month}/${updatedAt.year}',
              Icons.update_rounded,
            ),
          ],

          if (widget.work['dayName'] != null) ...[
            const SizedBox(height: 12),
            _buildInfoRow(
              'يوم الإنشاء',
              widget.work['dayName'],
              Icons.today_rounded,
            ),
          ],

          const SizedBox(height: 12),

          _buildInfoRow(
            'حالة المزامنة',
            (widget.work['isSynced'] ?? 0) == 1 ? 'مزامن' : 'غير مزامن',
            (widget.work['isSynced'] ?? 0) == 1
                ? Icons.cloud_done_rounded
                : Icons.cloud_off_rounded,
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String title, String value, IconData icon) {
    return Row(
      children: [
        Icon(icon, color: MeasurementStyles.primaryColor, size: 20),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            title,
            style: MeasurementStyles.normalTextStyle,
          ),
        ),
        Flexible(
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: MeasurementStyles.primaryColor,
            ),
            textAlign: TextAlign.end,
          ),
        ),
      ],
    );
  }

  Widget _buildFinancialRow(String title, String value, IconData icon, Color color) {
    return Row(
      children: [
        Icon(icon, color: color, size: 20),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            title,
            style: MeasurementStyles.normalTextStyle,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: color,
          ),
        ),
      ],
    );
  }
}
