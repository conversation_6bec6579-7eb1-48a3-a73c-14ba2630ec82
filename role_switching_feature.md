# ميزة التبديل بين الأدوار (خياط ↔ مدير)

## 🎯 الهدف:
إضافة إمكانية التنقل السهل بين نظام الخياط ونظام المدير من داخل التطبيق دون الحاجة للعودة لصفحة اختيار الدور.

## ✅ ما تم إضافته:

### 1. **في صفحة الخياط** (`tailor_home_screen.dart`):

#### أزرار AppBar الجديدة:
- 🔙 **زر العودة**: للرجوع لصفحة اختيار الدور
- 👥 **زر التبديل للمدير**: للانتقال المباشر لنظام المدير
- ⚙️ **زر الإعدادات**: إدارة خيارات التفصيل (موجود مسبقاً)

#### حوار التبديل:
```dart
void _showRoleSwitchDialog() {
  // حوار أنيق مع خيارين:
  // 1. نظام مدير الخياطين (بنفسجي)
  // 2. اختيار الدور (تركوازي)
}
```

### 2. **في صفحة المدير** (`manager_main_screen.dart`):

#### أزرار AppBar المحدثة:
- 🔙 **زر العودة**: للرجوع لصفحة اختيار الدور (موجود مسبقاً)
- 👤 **زر التبديل للخياط**: للانتقال المباشر لنظام الخياط (جديد)
- ⚙️ **زر الإعدادات**: إدارة خيارات التفصيل
- 🔄 **زر التحديث**: تحديث الخياطين
- 🗑️ **زر إعادة التعيين**: إعادة تعيين قاعدة البيانات

#### حوار التبديل:
```dart
void _showRoleSwitchDialog() {
  // حوار أنيق مع خيارين:
  // 1. نظام الخياط (تركوازي)
  // 2. اختيار الدور (بنفسجي)
}
```

## 🎨 التصميم:

### ألوان الأزرار:
- **نظام الخياط**: 🟢 تركوازي (`Colors.teal`)
- **نظام المدير**: 🟣 بنفسجي (`Colors.purple`)
- **اختيار الدور**: 🏠 رمادي محايد

### الأيقونات:
- **الخياط**: `Icons.person` 👤
- **المدير**: `Icons.supervisor_account` 👥
- **التبديل**: `Icons.swap_horiz` ↔️
- **العودة**: `Icons.arrow_back` ←
- **الرئيسية**: `Icons.home` 🏠

## 🚀 كيفية الاستخدام:

### من نظام الخياط:
1. **اضغط على أيقونة المدير** (👥) في AppBar
2. **اختر "نظام مدير الخياطين"** للانتقال المباشر
3. **أو اختر "اختيار الدور"** للعودة للصفحة الرئيسية

### من نظام المدير:
1. **اضغط على أيقونة الخياط** (👤) في AppBar
2. **اختر "نظام الخياط"** للانتقال المباشر
3. **أو اختر "اختيار الدور"** للعودة للصفحة الرئيسية

### العودة للرئيسية:
- **اضغط على زر العودة** (←) في أي من النظامين
- **سيعود للصفحة الرئيسية** لاختيار الدور

## 🎯 الفوائد:

### 1. **سهولة التنقل**:
- ✅ تبديل سريع بين الأنظمة
- ✅ لا حاجة للعودة للصفحة الرئيسية
- ✅ وصول مباشر لكلا النظامين

### 2. **تجربة مستخدم محسنة**:
- ✅ حوارات أنيقة ومفهومة
- ✅ ألوان مميزة لكل نظام
- ✅ أيقونات واضحة ومعبرة

### 3. **مرونة في الاستخدام**:
- ✅ يمكن للخياط استخدام نظام المدير
- ✅ يمكن للمدير استخدام نظام الخياط
- ✅ تبديل سلس بدون فقدان البيانات

## 🔧 التفاصيل التقنية:

### Navigation المستخدم:
```dart
Navigator.pushReplacement(
  context,
  MaterialPageRoute(
    builder: (_) => const TargetScreen(),
  ),
);
```

### حوار التبديل:
- **Directionality**: RTL للنصوص العربية
- **AlertDialog**: تصميم أنيق مع حواف مدورة
- **ElevatedButton**: للخيار الأساسي
- **OutlinedButton**: للخيار الثانوي

### الحماية:
- **Navigator.pop()**: إغلاق الحوار قبل التنقل
- **pushReplacement**: استبدال الصفحة بدلاً من إضافة جديدة
- **mounted check**: التأكد من وجود Widget قبل التحديث

## 🎉 النتيجة النهائية:

### قبل الإضافة:
- ❌ للتبديل بين الأنظمة: العودة → اختيار الدور → النظام الآخر
- ❌ 3 خطوات للتبديل
- ❌ تجربة مستخدم معقدة

### بعد الإضافة:
- ✅ للتبديل بين الأنظمة: زر التبديل → اختيار النظام
- ✅ خطوتان فقط للتبديل
- ✅ تجربة مستخدم سلسة ومريحة

## 🚀 اختبر الميزة الآن:

### الخطوة 1: من نظام الخياط
1. **افتح نظام الخياط**
2. **اضغط على أيقونة المدير** (👥) في الأعلى
3. **اختر "نظام مدير الخياطين"**
4. **النتيجة**: انتقال فوري لنظام المدير! ✅

### الخطوة 2: من نظام المدير
1. **في نظام المدير**
2. **اضغط على أيقونة الخياط** (👤) في الأعلى
3. **اختر "نظام الخياط"**
4. **النتيجة**: انتقال فوري لنظام الخياط! ✅

### الخطوة 3: العودة للرئيسية
1. **من أي نظام**
2. **اضغط على زر العودة** (←)
3. **النتيجة**: العودة لصفحة اختيار الدور! ✅

## 🎯 الخلاصة:

الآن يمكن للمستخدمين:
- 🔄 **التبديل السريع** بين الأنظمة
- 🎯 **الوصول المباشر** لأي نظام
- 🏠 **العودة السهلة** للصفحة الرئيسية
- 💼 **استخدام كلا النظامين** بمرونة كاملة

**ميزة التبديل بين الأدوار جاهزة ومفعلة!** 🚀✨
