// ملف اختبار لصفحة الغرامات المحسنة
import 'package:flutter/material.dart';
import 'screens/financial/penalties_screen.dart';

class TestPenaltiesIntegration extends StatelessWidget {
  const TestPenaltiesIntegration({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'اختبار صفحة الغرامات المحسنة',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        fontFamily: 'Cairo',
      ),
      home: const PenaltiesScreen(
        tailorId: 1, // معرف تجريبي
        tailorName: 'خياط تجريبي',
      ),
    );
  }
}

void main() {
  runApp(const TestPenaltiesIntegration());
}
