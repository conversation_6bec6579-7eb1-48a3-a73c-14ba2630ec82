// معالج إنشاء التصفية الشهرية المحسن
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../services/financial_state_manager.dart';

class CreateSettlementWizard extends StatefulWidget {
  final int tailorId;
  final String tailorName;

  const CreateSettlementWizard({
    super.key,
    required this.tailorId,
    required this.tailorName,
  });

  @override
  State<CreateSettlementWizard> createState() => _CreateSettlementWizardState();
}

class _CreateSettlementWizardState extends State<CreateSettlementWizard>
    with TickerProviderStateMixin {
  
  // متحكمات الأنيميشن
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  late Animation<double> _fadeAnimation;
  
  // بيانات التصفية
  DateTime _selectedDate = DateTime.now();
  int _currentStep = 0;
  bool _isLoading = false;
  
  // البيانات المالية
  double _totalIncome = 0.0;
  double _totalExpenses = 0.0;
  double _totalPenalties = 0.0;
  double _totalExtraWork = 0.0;
  double _initialBalance = 0.0;
  double _finalBalance = 0.0;
  
  final FinancialStateManager _financialManager = FinancialStateManager();

  @override
  void initState() {
    super.initState();
    
    // إعداد الأنيميشن
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    _slideAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _animationController.forward();
    
    // تهيئة مدير الحالة المالية
    _financialManager.setCurrentWorkshop(widget.tailorId);
    _loadFinancialData();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadFinancialData() async {
    setState(() => _isLoading = true);
    
    try {
      // جلب البيانات المالية الحالية
      final summary = _financialManager.getFinancialSummary();
      
      setState(() {
        _totalPenalties = summary['totalPenalties'] ?? 0.0;
        _totalExtraWork = summary['totalExtraWork'] ?? 0.0;
        _initialBalance = summary['initialBalance'] ?? 0.0;
        _finalBalance = summary['finalBalance'] ?? 0.0;
        
        // حساب الدخل والمصروفات (يمكن تحسينها لاحقاً)
        _totalIncome = _totalExtraWork + _initialBalance;
        _totalExpenses = _totalPenalties;
      });
    } catch (e) {
      debugPrint('خطأ في تحميل البيانات المالية: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: Colors.grey[50],
        body: FadeTransition(
          opacity: _fadeAnimation,
          child: Column(
            children: [
              // هيدر
              _buildHeader(),
              
              // مؤشر التقدم
              _buildProgressIndicator(),
              
              // المحتوى الرئيسي
              Expanded(
                child: SlideTransition(
                  position: Tween<Offset>(
                    begin: Offset(_slideAnimation.value, 0),
                    end: Offset.zero,
                  ).animate(_animationController),
                  child: _buildStepContent(),
                ),
              ),
              
              // أزرار التنقل
              _buildNavigationButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      height: 180,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.purple[700]!, Colors.purple[500]!],
          begin: Alignment.topRight,
          end: Alignment.bottomLeft,
        ),
        borderRadius: const BorderRadius.vertical(bottom: Radius.circular(30)),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              Row(
                children: [
                  GestureDetector(
                    onTap: () => Navigator.pop(context),
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(Icons.arrow_back, color: Colors.white),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Text(
                      'معالج التصفية الشهرية',
                      style: GoogleFonts.cairo(
                        color: Colors.white,
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Text(
                'خياط: ${widget.tailorName}',
                style: GoogleFonts.cairo(
                  color: Colors.white.withOpacity(0.9),
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'إنشاء تصفية شهرية جديدة بخطوات بسيطة',
                style: GoogleFonts.cairo(
                  color: Colors.white.withOpacity(0.8),
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return Container(
      margin: const EdgeInsets.all(20),
      child: Row(
        children: [
          _buildStepIndicator(0, 'التاريخ', Icons.calendar_today),
          _buildStepConnector(0),
          _buildStepIndicator(1, 'المراجعة', Icons.preview),
          _buildStepConnector(1),
          _buildStepIndicator(2, 'التأكيد', Icons.check_circle),
        ],
      ),
    );
  }

  Widget _buildStepIndicator(int step, String title, IconData icon) {
    final isActive = _currentStep >= step;
    final isCurrent = _currentStep == step;
    
    return Expanded(
      child: Column(
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: isActive ? Colors.purple[600] : Colors.grey[300],
              shape: BoxShape.circle,
              boxShadow: isCurrent ? [
                BoxShadow(
                  color: Colors.purple.withOpacity(0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ] : null,
            ),
            child: Icon(
              icon,
              color: isActive ? Colors.white : Colors.grey[600],
              size: 24,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: GoogleFonts.cairo(
              fontSize: 12,
              fontWeight: isCurrent ? FontWeight.bold : FontWeight.normal,
              color: isActive ? Colors.purple[600] : Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStepConnector(int step) {
    final isActive = _currentStep > step;
    
    return Container(
      height: 2,
      width: 40,
      margin: const EdgeInsets.only(bottom: 30),
      color: isActive ? Colors.purple[600] : Colors.grey[300],
    );
  }

  Widget _buildStepContent() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }
    
    switch (_currentStep) {
      case 0:
        return _buildDateSelectionStep();
      case 1:
        return _buildReviewStep();
      case 2:
        return _buildConfirmationStep();
      default:
        return _buildDateSelectionStep();
    }
  }

  Widget _buildDateSelectionStep() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'اختر تاريخ التصفية',
            style: GoogleFonts.cairo(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.grey[800],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'حدد اليوم والشهر والسنة لإجراء التصفية الشهرية',
            style: GoogleFonts.cairo(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
          
          const SizedBox(height: 40),
          
          // اختيار التاريخ
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              children: [
                // عرض التاريخ المختار
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [Colors.purple[50]!, Colors.purple[100]!],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.calendar_today, color: Colors.purple[600], size: 32),
                      const SizedBox(width: 16),
                      Column(
                        children: [
                          Text(
                            'التاريخ المختار',
                            style: GoogleFonts.cairo(
                              fontSize: 14,
                              color: Colors.grey[600],
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            '${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}',
                            style: GoogleFonts.cairo(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: Colors.purple[700],
                            ),
                          ),
                          Text(
                            _getArabicDayName(_selectedDate.weekday),
                            style: GoogleFonts.cairo(
                              fontSize: 14,
                              color: Colors.purple[600],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 24),
                
                // زر اختيار التاريخ
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: _selectDate,
                    icon: const Icon(Icons.edit_calendar),
                    label: const Text('تغيير التاريخ'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple[600],
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 24),
          
          // نصائح
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.blue[200]!),
            ),
            child: Row(
              children: [
                Icon(Icons.info, color: Colors.blue[600], size: 24),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'يُنصح بإجراء التصفية في نهاية كل شهر للحصول على أفضل النتائج',
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color: Colors.blue[700],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReviewStep() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'مراجعة البيانات المالية',
            style: GoogleFonts.cairo(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.grey[800],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'تحقق من البيانات المالية قبل إجراء التصفية',
            style: GoogleFonts.cairo(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
          
          const SizedBox(height: 24),
          
          // ملخص البيانات المالية
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              children: [
                _buildFinancialSummaryItem(
                  'الرصيد المرحل',
                  _initialBalance,
                  Icons.account_balance_wallet,
                  Colors.blue,
                ),
                const Divider(height: 32),
                _buildFinancialSummaryItem(
                  'الأعمال الإضافية',
                  _totalExtraWork,
                  Icons.add_business,
                  Colors.green,
                ),
                const Divider(height: 32),
                _buildFinancialSummaryItem(
                  'الغرامات',
                  _totalPenalties,
                  Icons.warning,
                  Colors.red,
                ),
                const Divider(height: 32),
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: _finalBalance >= 0 
                        ? [Colors.green[50]!, Colors.green[100]!]
                        : [Colors.red[50]!, Colors.red[100]!],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          Icon(
                            _finalBalance >= 0 ? Icons.trending_up : Icons.trending_down,
                            color: _finalBalance >= 0 ? Colors.green[700] : Colors.red[700],
                            size: 24,
                          ),
                          const SizedBox(width: 12),
                          Text(
                            'الرصيد النهائي',
                            style: GoogleFonts.cairo(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.grey[700],
                            ),
                          ),
                        ],
                      ),
                      Text(
                        '${_finalBalance >= 0 ? '+' : '-'}${_finalBalance.abs().toStringAsFixed(0)} ريال',
                        style: GoogleFonts.cairo(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: _finalBalance >= 0 ? Colors.green[700] : Colors.red[700],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildConfirmationStep() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(40),
            decoration: BoxDecoration(
              color: Colors.green[50],
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.check_circle,
              size: 80,
              color: Colors.green[600],
            ),
          ),
          const SizedBox(height: 32),
          Text(
            'تم إنشاء التصفية بنجاح!',
            style: GoogleFonts.cairo(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.green[700],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          Text(
            'تم حفظ التصفية الشهرية وترحيل الرصيد للشهر التالي',
            style: GoogleFonts.cairo(
              fontSize: 16,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildFinancialSummaryItem(String title, double amount, IconData icon, Color color) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(icon, color: color, size: 24),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Text(
            title,
            style: GoogleFonts.cairo(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.grey[700],
            ),
          ),
        ),
        Text(
          '${amount.toStringAsFixed(0)} ريال',
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  Widget _buildNavigationButtons() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          if (_currentStep > 0)
            Expanded(
              child: OutlinedButton(
                onPressed: _previousStep,
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text('السابق'),
              ),
            ),
          if (_currentStep > 0) const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: _currentStep < 2 ? _nextStep : _finish,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.purple[600],
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Text(_currentStep < 2 ? 'التالي' : 'إنهاء'),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
      locale: const Locale('ar'),
    );
    
    if (date != null) {
      setState(() {
        _selectedDate = date;
      });
    }
  }

  void _nextStep() {
    if (_currentStep < 2) {
      setState(() {
        _currentStep++;
      });
      _animationController.reset();
      _animationController.forward();
    }
  }

  void _previousStep() {
    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
      _animationController.reset();
      _animationController.forward();
    }
  }

  void _finish() {
    Navigator.pop(context, true);
  }

  String _getArabicDayName(int weekday) {
    const days = [
      'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 
      'الجمعة', 'السبت', 'الأحد'
    ];
    return days[weekday - 1];
  }
}
