import 'package:flutter/services.dart';

/// مساعد للتعامل مع الأرقام الصحيحة في التطبيق
class NumberUtils {
  
  /// تحويل الرقم إلى عدد صحيح للعرض (إزالة الفاصلة العشرية)
  static String formatIntegerDisplay(dynamic value) {
    if (value == null) return '0';
    
    if (value is String) {
      // تحويل النص إلى رقم أولاً
      final parsed = double.tryParse(value);
      if (parsed == null) return '0';
      return parsed.toInt().toString();
    }
    
    if (value is double) {
      return value.toInt().toString();
    }
    
    if (value is int) {
      return value.toString();
    }
    
    return '0';
  }
  
  /// تحويل الرقم للإدخال في TextField (إزالة الفاصلة العشرية)
  static String formatForInput(dynamic value) {
    if (value == null) return '';
    
    if (value is String) {
      if (value.isEmpty) return '';
      final parsed = double.tryParse(value);
      if (parsed == null) return '';
      return parsed.toInt().toString();
    }
    
    if (value is double) {
      return value.toInt().toString();
    }
    
    if (value is int) {
      return value.toString();
    }
    
    return '';
  }
  
  /// تحويل النص المدخل إلى رقم صحيح
  static int parseInteger(String value) {
    if (value.isEmpty) return 0;
    final parsed = double.tryParse(value);
    if (parsed == null) return 0;
    return parsed.toInt();
  }
  
  /// تحويل النص المدخل إلى رقم عشري (للحفظ في قاعدة البيانات)
  static double parseDouble(String value) {
    if (value.isEmpty) return 0.0;
    final parsed = double.tryParse(value);
    if (parsed == null) return 0.0;
    return parsed;
  }
  
  /// InputFormatter للسماح بالأرقام الصحيحة فقط
  static List<TextInputFormatter> get integerInputFormatters => [
    FilteringTextInputFormatter.digitsOnly, // أرقام فقط
  ];
  
  /// InputFormatter للسماح بالأرقام مع فاصلة عشرية (للحالات الخاصة)
  static List<TextInputFormatter> get decimalInputFormatters => [
    FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
  ];
}

/// TextInputFormatter مخصص للأرقام الصحيحة
class IntegerInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    // السماح بالأرقام فقط
    if (newValue.text.isEmpty) {
      return newValue;
    }
    
    // التحقق من أن النص يحتوي على أرقام فقط
    if (RegExp(r'^\d+$').hasMatch(newValue.text)) {
      return newValue;
    }
    
    // إذا كان النص يحتوي على فاصلة عشرية، إزالتها
    final cleanText = newValue.text.replaceAll(RegExp(r'[^\d]'), '');
    
    return TextEditingValue(
      text: cleanText,
      selection: TextSelection.collapsed(offset: cleanText.length),
    );
  }
}
