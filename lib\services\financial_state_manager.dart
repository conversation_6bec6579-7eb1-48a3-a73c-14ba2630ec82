import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/db_helper.dart';

/// مدير الحالة المالية للتحديث الفوري
class FinancialStateManager extends ChangeNotifier {
  static final FinancialStateManager _instance = FinancialStateManager._internal();
  factory FinancialStateManager() => _instance;
  FinancialStateManager._internal();

  // البيانات المالية الحالية
  double _totalPenalties = 0.0;
  double _totalExtraWork = 0.0;
  double _initialBalance = 0.0;
  double _finalBalance = 0.0;
  bool _isInDebt = false;
  int? _currentWorkshopId;

  // Getters
  double get totalPenalties => _totalPenalties;
  double get totalExtraWork => _totalExtraWork;
  double get initialBalance => _initialBalance;
  double get finalBalance => _finalBalance;
  bool get isInDebt => _isInDebt;

  // Stream Controllers للتحديث الفوري
  final StreamController<Map<String, dynamic>> _financialDataController =
      StreamController<Map<String, dynamic>>.broadcast();

  Stream<Map<String, dynamic>> get financialDataStream => _financialDataController.stream;

  /// تحديث معرف المعمل الحالي
  void setCurrentWorkshop(int workshopId) {
    _currentWorkshopId = workshopId;
    _loadFinancialData();
  }

  /// تحميل البيانات المالية
  Future<void> _loadFinancialData() async {
    if (_currentWorkshopId == null) return;

    try {
      // جلب الغرامات
      final penalties = await DBHelper.getPenaltiesByTailorId(_currentWorkshopId!);
      _totalPenalties = penalties.fold(0.0, (sum, penalty) =>
        sum + (penalty['penaltyAmount'] as num? ?? 0).toDouble());

      // جلب الأعمال الإضافية
      final extraWorks = await DBHelper.getExtraWorkByTailorId(_currentWorkshopId!);
      _totalExtraWork = extraWorks.fold(0.0, (sum, work) =>
        sum + (work['extraWorkAmount'] as num? ?? 0).toDouble());

      // جلب الرصيد المرحل
      final balance = await DBHelper.getInitialBalanceByTailorId(_currentWorkshopId!);
      if (balance != null) {
        final extra = (balance['extraFromFirstMonth'] as num? ?? 0).toDouble();
        final debt = (balance['debtFromFirstMonth'] as num? ?? 0).toDouble();
        _initialBalance = extra - debt;
      } else {
        _initialBalance = 0.0;
      }

      // حساب الرصيد النهائي
      await _calculateFinalBalance();

      // إرسال التحديث
      _broadcastUpdate();

      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في تحميل البيانات المالية: $e');
    }
  }

  /// حساب الرصيد النهائي
  Future<void> _calculateFinalBalance() async {
    if (_currentWorkshopId == null) return;

    try {
      // جلب بيانات المعمل
      final workshop = await DBHelper.getWorkshopById(_currentWorkshopId!);
      if (workshop == null) return;

      // جلب الأعمال
      final works = await DBHelper.getWorksForWorkshop(_currentWorkshopId!);

      // حساب الدخل من الإنتاج
      double totalIncome = 0.0;
      double totalExpenses = 0.0;

      final pricePerPiece = (workshop['pricePerPiece'] as num? ?? 0).toDouble();
      final qassasPrice = (workshop['qassasPrice'] as num? ?? 0).toDouble();
      final otherTypePrice = (workshop['otherTypePrice'] as num? ?? 0).toDouble();
      final isQassas = (workshop['isQassas'] as int? ?? 0) == 1;
      final hasOtherType = (workshop['otherTypeName'] as String? ?? '').isNotEmpty;

      for (var work in works) {
        // الدخل
        totalIncome += (work['quantity'] as int? ?? 0) * pricePerPiece;
        if (isQassas) {
          totalIncome += (work['cutQuantity'] as int? ?? 0) * qassasPrice;
        }
        if (hasOtherType) {
          totalIncome += (work['otherCount'] as int? ?? 0) * otherTypePrice;
        }

        // المصروفات
        totalExpenses += double.tryParse(work['expense'].toString()) ?? 0.0;
      }

      // حساب الرصيد النهائي
      final totalIncomeWithExtras = totalIncome + _totalExtraWork + _initialBalance;
      final totalExpensesWithPenalties = totalExpenses + _totalPenalties;

      _finalBalance = totalIncomeWithExtras - totalExpensesWithPenalties;
      _isInDebt = _finalBalance < 0;

    } catch (e) {
      debugPrint('خطأ في حساب الرصيد النهائي: $e');
    }
  }

  /// إضافة غرامة جديدة
  Future<void> addPenalty(Map<String, dynamic> penaltyData) async {
    try {
      await DBHelper.insertPenalty(penaltyData);
      await _loadFinancialData(); // إعادة تحميل البيانات

      // إشعار فوري
      _showInstantNotification(
        'تم إضافة غرامة ${penaltyData['penaltyAmount']} ريال',
        _isInDebt ? 'أصبحت مديون: ${_finalBalance.abs().toStringAsFixed(0)} ريال'
                  : 'رصيدك الآن: ${_finalBalance.toStringAsFixed(0)} ريال'
      );
    } catch (e) {
      debugPrint('خطأ في إضافة الغرامة: $e');
    }
  }

  /// إضافة عمل إضافي جديد
  Future<void> addExtraWork(Map<String, dynamic> extraWorkData) async {
    try {
      await DBHelper.insertExtraWork(extraWorkData);
      await _loadFinancialData(); // إعادة تحميل البيانات

      // إشعار فوري
      _showInstantNotification(
        'تم إضافة عمل إضافي ${extraWorkData['extraWorkAmount']} ريال',
        _isInDebt ? 'لا تزال مديون: ${_finalBalance.abs().toStringAsFixed(0)} ريال'
                  : 'رصيدك الآن: ${_finalBalance.toStringAsFixed(0)} ريال'
      );
    } catch (e) {
      debugPrint('خطأ في إضافة العمل الإضافي: $e');
    }
  }

  /// تحديث الرصيد المرحل
  Future<void> updateInitialBalance(double amount) async {
    try {
      if (_currentWorkshopId == null) return;

      // إنشاء بيانات الرصيد المرحل
      final balanceData = {
        'tailorId': _currentWorkshopId!,
        'extraFromFirstMonth': amount > 0 ? amount : 0.0,
        'debtFromFirstMonth': amount < 0 ? amount.abs() : 0.0,
        'updatedAt': DateTime.now().toIso8601String(),
      };

      // تحديث أو إضافة الرصيد
      final existingBalance = await DBHelper.getInitialBalanceByTailorId(_currentWorkshopId!);
      if (existingBalance != null) {
        await DBHelper.updateInitialBalance(existingBalance['id'], balanceData);
      } else {
        balanceData['createdAt'] = DateTime.now().toIso8601String();
        await DBHelper.insertInitialBalance(balanceData);
      }

      await _loadFinancialData(); // إعادة تحميل البيانات

      // إشعار فوري
      _showInstantNotification(
        'تم تحديث الرصيد المرحل إلى ${amount.toStringAsFixed(0)} ريال',
        'الرصيد النهائي: ${_finalBalance.toStringAsFixed(0)} ريال'
      );
    } catch (e) {
      debugPrint('خطأ في تحديث الرصيد المرحل: $e');
    }
  }

  /// إرسال تحديث للمستمعين
  void _broadcastUpdate() {
    final data = {
      'totalPenalties': _totalPenalties,
      'totalExtraWork': _totalExtraWork,
      'initialBalance': _initialBalance,
      'finalBalance': _finalBalance,
      'isInDebt': _isInDebt,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };

    _financialDataController.add(data);
  }

  /// إظهار إشعار فوري
  void _showInstantNotification(String title, String subtitle) {
    // يمكن استخدام هذه الدالة لإظهار إشعارات فورية
    debugPrint('🔔 $title - $subtitle');
  }

  /// تنظيف الموارد
  @override
  void dispose() {
    _financialDataController.close();
    super.dispose();
  }

  /// إعادة تحميل البيانات يدوياً
  Future<void> refresh() async {
    await _loadFinancialData();
  }

  /// الحصول على ملخص الحالة المالية
  Map<String, dynamic> getFinancialSummary() {
    return {
      'totalPenalties': _totalPenalties,
      'totalExtraWork': _totalExtraWork,
      'initialBalance': _initialBalance,
      'finalBalance': _finalBalance,
      'isInDebt': _isInDebt,
      'status': _isInDebt ? 'مديون' : 'رصيد موجب',
      'statusColor': _isInDebt ? 'red' : 'green',
      'amount': _finalBalance.abs(),
    };
  }
}
