import 'package:flutter/material.dart';
import '../../models/db_helper.dart';
import '../../utils/number_utils.dart';
import '../../utils/date_utils.dart';

class AddTailorWorkScreen extends StatefulWidget {
  final Map<String, dynamic> tailor;
  final Map<String, dynamic>? workToEdit;

  const AddTailorWorkScreen({
    super.key,
    required this.tailor,
    this.workToEdit,
  });

  @override
  State<AddTailorWorkScreen> createState() => _AddTailorWorkScreenState();
}

class _AddTailorWorkScreenState extends State<AddTailorWorkScreen> {
  final _formKey = GlobalKey<FormState>();

  // Controllers للحقول
  final _quantityController = TextEditingController();
  final _cutQuantityController = TextEditingController();
  final _otherQuantityController = TextEditingController();
  final _dailyExpenseController = TextEditingController();
  final _notesController = TextEditingController();
  final _executionDateController = TextEditingController();

  bool _isLoading = false;
  bool _isEdit = false;

  // معلومات الخياط
  String _workType = '';
  double _pricePerPiece = 0.0;
  bool _isCutter = false;
  bool _hasOtherWork = false;
  String _otherWorkType = '';
  double _otherWorkPrice = 0.0;
  double _cutPrice = 0.0;

  @override
  void initState() {
    super.initState();
    _loadTailorInfo();
    if (widget.workToEdit != null) {
      _isEdit = true;
      _loadWorkData();
    }
    _executionDateController.text = DateTime.now().toString().split(' ')[0];
  }

  void _loadTailorInfo() {
    _workType = widget.tailor['workType'] ?? '';
    _pricePerPiece = (widget.tailor['pricePerPiece'] ?? 0.0).toDouble();
    _isCutter = (widget.tailor['isCutter'] ?? 0) == 1;
    _hasOtherWork = (widget.tailor['hasOtherWork'] ?? 0) == 1;
    _otherWorkType = widget.tailor['otherWorkType'] ?? '';
    _otherWorkPrice = (widget.tailor['otherWorkPrice'] ?? 0.0).toDouble();
    _cutPrice = (widget.tailor['cutPrice'] ?? 0.0).toDouble();
  }

  void _loadWorkData() {
    final work = widget.workToEdit!;
    _quantityController.text = NumberUtils.formatForInput(work['quantity']);
    _cutQuantityController.text = NumberUtils.formatForInput(work['cutQuantity']);
    _otherQuantityController.text = NumberUtils.formatForInput(work['otherQuantity']);
    _dailyExpenseController.text = NumberUtils.formatForInput(work['dailyExpense']);
    _notesController.text = work['notes'] ?? '';
    _executionDateController.text = work['executionDate'] ?? '';
  }

  @override
  void dispose() {
    _quantityController.dispose();
    _cutQuantityController.dispose();
    _otherQuantityController.dispose();
    _dailyExpenseController.dispose();
    _notesController.dispose();
    _executionDateController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: Colors.grey[50],
        appBar: AppBar(
          title: Text(
            _isEdit ? 'تعديل عمل الخياط' : 'إضافة عمل جديد للخياط',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          backgroundColor: Colors.purple[700],
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.white),
            onPressed: () => Navigator.pop(context),
          ),
        ),
        body: Form(
          key: _formKey,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // بطاقة معلومات الخياط
                _buildTailorInfoCard(),

                const SizedBox(height: 16),

                // بطاقة تفاصيل العمل
                _buildWorkDetailsCard(),

                const SizedBox(height: 16),

                // بطاقة المصروفات والملاحظات
                _buildExpensesCard(),

                const SizedBox(height: 24),

                // زر الحفظ
                _buildSaveButton(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTailorInfoCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            colors: [Colors.purple[600]!, Colors.purple[400]!],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.person, color: Colors.white, size: 24),
                SizedBox(width: 12),
                Text(
                  'معلومات الخياط',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoRow('الاسم', widget.tailor['name'] ?? ''),
            _buildInfoRow('نوع الشغل', _workType),
            _buildInfoRow('سعر القطعة', '${_pricePerPiece.toStringAsFixed(2)} ريال'),
            if (_isCutter)
              _buildInfoRow('سعر القصة', '${_cutPrice.toStringAsFixed(2)} ريال'),
            if (_hasOtherWork) ...[
              _buildInfoRow('العمل الإضافي', _otherWorkType),
              _buildInfoRow('سعر العمل الإضافي', '${_otherWorkPrice.toStringAsFixed(2)} ريال'),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWorkDetailsCard() {
    return _buildCard(
      title: 'تفاصيل العمل',
      icon: Icons.work,
      children: [
        // عدد القطع الأساسي
        _buildTextField(
          controller: _quantityController,
          label: 'عدد القطع',
          hint: '0',
          icon: Icons.format_list_numbered,
          keyboardType: TextInputType.number,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'يرجى إدخال عدد القطع';
            }
            final quantity = NumberUtils.parseInteger(value.trim());
            if (quantity <= 0) {
              return 'يرجى إدخال عدد صحيح';
            }
            return null;
          },
        ),

        // عدد القصة (إذا كان قصاص)
        if (_isCutter) ...[
          const SizedBox(height: 16),
          _buildTextField(
            controller: _cutQuantityController,
            label: 'عدد القصة',
            hint: '0',
            icon: Icons.content_cut,
            keyboardType: TextInputType.number,
            validator: (value) {
              if (value != null && value.trim().isNotEmpty) {
                final quantity = NumberUtils.parseInteger(value.trim());
                if (quantity < 0) {
                  return 'يرجى إدخال عدد صحيح';
                }
              }
              return null;
            },
          ),
        ],

        // عدد الأعمال الإضافية (إذا كان يعمل أنواع أخرى)
        if (_hasOtherWork) ...[
          const SizedBox(height: 16),
          _buildTextField(
            controller: _otherQuantityController,
            label: 'عدد $_otherWorkType',
            hint: '0',
            icon: Icons.category,
            keyboardType: TextInputType.number,
            validator: (value) {
              if (value != null && value.trim().isNotEmpty) {
                final quantity = NumberUtils.parseInteger(value.trim());
                if (quantity < 0) {
                  return 'يرجى إدخال عدد صحيح';
                }
              }
              return null;
            },
          ),
        ],

        const SizedBox(height: 16),

        // تاريخ التنفيذ
        _buildDateField(),
      ],
    );
  }

  Widget _buildExpensesCard() {
    return _buildCard(
      title: 'المصروفات والملاحظات',
      icon: Icons.receipt,
      children: [
        // المصروف اليومي
        _buildTextField(
          controller: _dailyExpenseController,
          label: 'المصروف اليومي (ريال)',
          hint: '0.00',
          icon: Icons.money,
          keyboardType: TextInputType.number,
          validator: (value) {
            if (value != null && value.trim().isNotEmpty) {
              final expense = NumberUtils.parseInteger(value.trim());
              if (expense < 0) {
                return 'يرجى إدخال مبلغ صحيح';
              }
            }
            return null;
          },
        ),

        const SizedBox(height: 16),

        // الملاحظات
        _buildTextField(
          controller: _notesController,
          label: 'الملاحظات',
          hint: 'أي ملاحظات إضافية...',
          icon: Icons.note,
          maxLines: 3,
        ),
      ],
    );
  }

  Widget _buildCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: Colors.purple[600], size: 24),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[800],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    TextInputType keyboardType = TextInputType.text,
    String? Function(String?)? validator,
    int maxLines = 1,
  }) {
    // تحديد ما إذا كان الحقل رقمي
    final isNumeric = keyboardType == TextInputType.number;

    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      inputFormatters: isNumeric ? NumberUtils.integerInputFormatters : null,
      maxLines: maxLines,
      validator: validator,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixIcon: Icon(icon, color: Colors.purple[600]),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey[300]!),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.purple[600]!, width: 2),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey[300]!),
        ),
        filled: true,
        fillColor: Colors.grey[50],
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      ),
    );
  }

  Widget _buildDateField() {
    return TextFormField(
      controller: _executionDateController,
      readOnly: true,
      onTap: _selectDate,
      decoration: InputDecoration(
        labelText: 'تاريخ التنفيذ',
        hintText: 'اختر التاريخ',
        prefixIcon: Icon(Icons.calendar_today, color: Colors.purple[600]),
        suffixIcon: Icon(Icons.arrow_drop_down, color: Colors.purple[600]),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey[300]!),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.purple[600]!, width: 2),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey[300]!),
        ),
        filled: true,
        fillColor: Colors.grey[50],
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'يرجى اختيار تاريخ التنفيذ';
        }
        return null;
      },
    );
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: Colors.purple[600]!,
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _executionDateController.text = picked.toString().split(' ')[0];
      });
    }
  }

  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _saveWork,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.purple[600],
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          elevation: 4,
        ),
        child: _isLoading
            ? const SizedBox(
                height: 24,
                width: 24,
                child: CircularProgressIndicator(
                  color: Colors.white,
                  strokeWidth: 2,
                ),
              )
            : Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(_isEdit ? Icons.update : Icons.save, size: 24),
                  const SizedBox(width: 12),
                  Text(
                    _isEdit ? 'تحديث العمل' : 'حفظ العمل',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  // دالة التحقق من صحة البيانات
  bool _validateRequiredFields() {
    // الحقول المطلوبة
    final quantity = _quantityController.text.trim();
    final dailyExpense = _dailyExpenseController.text.trim();

    // التحقق من وجود قيمة في حقل واحد على الأقل
    bool hasQuantity = quantity.isNotEmpty && NumberUtils.parseInteger(quantity) > 0;
    bool hasDailyExpense = dailyExpense.isNotEmpty && NumberUtils.parseDouble(dailyExpense) > 0;

    // إذا لم يتم ملء أي حقل من الحقلين الأساسيين
    if (!hasQuantity && !hasDailyExpense) {
      _showValidationError();
      return false;
    }

    return true;
  }

  // دالة عرض رسالة الخطأ
  void _showValidationError() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Icon(Icons.warning, color: Colors.orange[600]),
              const SizedBox(width: 12),
              const Text('تنبيه'),
            ],
          ),
          content: const Text(
            'يرجى ملء واحد من الحقول التالية:\n\n• عدد العمل\n• المصروف اليومي',
            style: TextStyle(fontSize: 16),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('فهمت'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _saveWork() async {
    if (!_formKey.currentState!.validate()) return;

    // التحقق من صحة البيانات - يجب ملء حقل واحد على الأقل
    if (!_validateRequiredFields()) {
      return;
    }

    setState(() => _isLoading = true);

    try {
      // حساب الأسعار
      final quantity = NumberUtils.parseInteger(_quantityController.text.trim());
      final cutQuantity = _cutQuantityController.text.trim().isNotEmpty
          ? NumberUtils.parseInteger(_cutQuantityController.text.trim())
          : 0;
      final otherQuantity = _otherQuantityController.text.trim().isNotEmpty
          ? NumberUtils.parseInteger(_otherQuantityController.text.trim())
          : 0;
      final dailyExpense = _dailyExpenseController.text.trim().isNotEmpty
          ? NumberUtils.parseDouble(_dailyExpenseController.text.trim())
          : 0.0;

      // حساب السعر الإجمالي
      double totalPrice = quantity * _pricePerPiece;
      if (_isCutter && cutQuantity > 0) {
        totalPrice += cutQuantity * _cutPrice;
      }
      if (_hasOtherWork && otherQuantity > 0) {
        totalPrice += otherQuantity * _otherWorkPrice;
      }

      final data = {
        'tailorId': widget.tailor['id'],
        'workType': _workType,
        'quantity': quantity,
        'cutQuantity': cutQuantity,
        'otherQuantity': otherQuantity,
        'pricePerPiece': _pricePerPiece,
        'cutPrice': _cutPrice,
        'otherWorkPrice': _otherWorkPrice,
        'totalPrice': totalPrice,
        'dailyExpense': dailyExpense,
        'executionDate': _executionDateController.text.trim(),
        'notes': _notesController.text.trim(),
        'dayName': AppDateUtils.getCurrentDayName(), // إضافة اسم اليوم
        'createdAt': DateTime.now().toIso8601String(),
        'updatedAt': DateTime.now().toIso8601String(),
      };

      if (_isEdit) {
        await DBHelper.updateTailorWork(widget.workToEdit!['id'], data);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.white),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(AppDateUtils.getWorkUpdatedMessage()),
                  ),
                ],
              ),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(10)),
              ),
            ),
          );
        }
      } else {
        await DBHelper.insertTailorWork(data);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.white),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(AppDateUtils.getWorkAddedMessage()),
                  ),
                ],
              ),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(10)),
              ),
            ),
          );
        }
      }

      if (mounted) {
        Navigator.pop(context, true); // إرجاع true للإشارة إلى نجاح العملية
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(
                  child: Text('خطأ في ${_isEdit ? 'تحديث' : 'إضافة'} العمل: $e'),
                ),
              ],
            ),
            backgroundColor: Colors.red[600],
            behavior: SnackBarBehavior.floating,
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.all(Radius.circular(10)),
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
