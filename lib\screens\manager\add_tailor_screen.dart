import 'package:flutter/material.dart';
import '../../models/db_helper.dart';
import '../../utils/number_utils.dart';
import '../../utils/date_utils.dart';

class AddTailorScreen extends StatefulWidget {
  final Map<String, dynamic>? tailorToEdit;

  const AddTailorScreen({super.key, this.tailorToEdit});

  @override
  State<AddTailorScreen> createState() => _AddTailorScreenState();
}

class _AddTailorScreenState extends State<AddTailorScreen> {
  final _formKey = GlobalKey<FormState>();

  // Controllers للحقول
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _workTypeController = TextEditingController();
  final _priceController = TextEditingController();
  final _addressController = TextEditingController();
  final _notesController = TextEditingController();

  // Controllers للحقول الجديدة
  final _cutPriceController = TextEditingController();
  final _otherWorkTypeController = TextEditingController();
  final _otherWorkPriceController = TextEditingController();

  bool _isLoading = false;
  bool _isEdit = false;
  bool _isCutter = false; // هل هو قصاص
  bool _hasOtherWork = false; // هل يشتغل أنواع أخرى

  @override
  void initState() {
    super.initState();
    if (widget.tailorToEdit != null) {
      _isEdit = true;
      _loadTailorData();
    }
  }

  void _loadTailorData() {
    final tailor = widget.tailorToEdit!;
    _nameController.text = tailor['name'] ?? '';
    _phoneController.text = tailor['phone'] ?? '';
    _workTypeController.text = tailor['workType'] ?? '';
    _priceController.text = NumberUtils.formatForInput(tailor['pricePerPiece']);
    _addressController.text = tailor['address'] ?? '';
    _notesController.text = tailor['notes'] ?? '';

    // تحميل الحقول الجديدة
    _isCutter = (tailor['isCutter'] ?? 0) == 1;
    _hasOtherWork = (tailor['hasOtherWork'] ?? 0) == 1;

    if (_isCutter && tailor['cutPrice'] != null) {
      _cutPriceController.text = NumberUtils.formatForInput(tailor['cutPrice']);
    }

    if (_hasOtherWork) {
      _otherWorkTypeController.text = tailor['otherWorkType'] ?? '';
      if (tailor['otherWorkPrice'] != null) {
        _otherWorkPriceController.text = NumberUtils.formatForInput(tailor['otherWorkPrice']);
      }
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _workTypeController.dispose();
    _priceController.dispose();
    _addressController.dispose();
    _notesController.dispose();
    _cutPriceController.dispose();
    _otherWorkTypeController.dispose();
    _otherWorkPriceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: Colors.grey[50],
        appBar: AppBar(
          title: Text(
            _isEdit ? 'تعديل خياط' : 'إضافة خياط جديد',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          backgroundColor: Colors.purple.shade700,
          elevation: 2,
          actions: [
            if (_isLoading)
              const Padding(
                padding: EdgeInsets.all(16.0),
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
              ),
          ],
        ),
        body: Form(
          key: _formKey,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // بطاقة المعلومات الأساسية
                _buildCard(
                  title: 'المعلومات الأساسية',
                  icon: Icons.person,
                  children: [
                    _buildTextField(
                      controller: _nameController,
                      label: 'اسم الخياط',
                      hint: 'أدخل اسم الخياط',
                      icon: Icons.person_outline,
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'يرجى إدخال اسم الخياط';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    _buildTextField(
                      controller: _phoneController,
                      label: 'رقم الجوال (اختياري)',
                      hint: 'مثال: 0501234567',
                      icon: Icons.phone,
                      keyboardType: TextInputType.phone,
                      validator: (value) {
                        // رقم الجوال اختياري - لا توجد شروط
                        return null;
                      },
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // بطاقة معلومات العمل
                _buildCard(
                  title: 'معلومات العمل',
                  icon: Icons.work,
                  children: [
                    _buildTextField(
                      controller: _workTypeController,
                      label: 'نوع الشغل',
                      hint: 'مثال: ثياب رجالية، بدلات، ثياب نسائية',
                      icon: Icons.category,
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'يرجى إدخال نوع الشغل';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    _buildTextField(
                      controller: _priceController,
                      label: 'سعر القطعة (ريال)',
                      hint: '0.00',
                      icon: Icons.attach_money,
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'يرجى إدخال سعر القطعة';
                        }
                        final price = NumberUtils.parseInteger(value.trim());
                        if (price <= 0) {
                          return 'يرجى إدخال سعر صحيح';
                        }
                        return null;
                      },
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // بطاقة مهارات إضافية
                _buildCard(
                  title: 'مهارات إضافية',
                  icon: Icons.build,
                  children: [
                    // زر اختيار هل هو قصاص
                    Row(
                      children: [
                        Checkbox(
                          value: _isCutter,
                          onChanged: (value) {
                            setState(() {
                              _isCutter = value ?? false;
                              if (!_isCutter) {
                                _cutPriceController.clear();
                              }
                            });
                          },
                          activeColor: Colors.purple[600],
                        ),
                        const SizedBox(width: 8),
                        const Expanded(
                          child: Text(
                            'هل أنت قصاص؟',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),

                    // حقل سعر القصة (يظهر فقط إذا كان قصاص)
                    if (_isCutter) ...[
                      const SizedBox(height: 16),
                      _buildTextField(
                        controller: _cutPriceController,
                        label: 'سعر القصة (ريال)',
                        hint: '0.00',
                        icon: Icons.content_cut,
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (_isCutter && (value == null || value.trim().isEmpty)) {
                            return 'يرجى إدخال سعر القصة';
                          }
                          if (_isCutter && value != null && value.trim().isNotEmpty) {
                            final price = NumberUtils.parseInteger(value.trim());
                            if (price <= 0) {
                              return 'يرجى إدخال سعر صحيح';
                            }
                          }
                          return null;
                        },
                      ),
                    ],

                    const SizedBox(height: 16),

                    // زر اختيار هل يشتغل أنواع أخرى
                    Row(
                      children: [
                        Checkbox(
                          value: _hasOtherWork,
                          onChanged: (value) {
                            setState(() {
                              _hasOtherWork = value ?? false;
                              if (!_hasOtherWork) {
                                _otherWorkTypeController.clear();
                                _otherWorkPriceController.clear();
                              }
                            });
                          },
                          activeColor: Colors.purple[600],
                        ),
                        const SizedBox(width: 8),
                        const Expanded(
                          child: Text(
                            'هل تشتغل أنواع أخرى من الثياب؟',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),

                    // حقول العمل الإضافي (تظهر فقط إذا اختار نعم)
                    if (_hasOtherWork) ...[
                      const SizedBox(height: 16),
                      _buildTextField(
                        controller: _otherWorkTypeController,
                        label: 'نوع العمل الإضافي',
                        hint: 'مثال: عبايات، بشوت، إلخ',
                        icon: Icons.category,
                        validator: (value) {
                          if (_hasOtherWork && (value == null || value.trim().isEmpty)) {
                            return 'يرجى إدخال نوع العمل الإضافي';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      _buildTextField(
                        controller: _otherWorkPriceController,
                        label: 'سعر العمل الإضافي (ريال)',
                        hint: '0.00',
                        icon: Icons.attach_money,
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (_hasOtherWork && (value == null || value.trim().isEmpty)) {
                            return 'يرجى إدخال سعر العمل الإضافي';
                          }
                          if (_hasOtherWork && value != null && value.trim().isNotEmpty) {
                            final price = NumberUtils.parseInteger(value.trim());
                            if (price <= 0) {
                              return 'يرجى إدخال سعر صحيح';
                            }
                          }
                          return null;
                        },
                      ),
                    ],
                  ],
                ),

                const SizedBox(height: 16),

                // بطاقة معلومات إضافية
                _buildCard(
                  title: 'معلومات إضافية',
                  icon: Icons.info,
                  children: [
                    _buildTextField(
                      controller: _addressController,
                      label: 'العنوان',
                      hint: 'عنوان الخياط (اختياري)',
                      icon: Icons.location_on,
                      maxLines: 2,
                    ),
                    const SizedBox(height: 16),
                    _buildTextField(
                      controller: _notesController,
                      label: 'ملاحظات',
                      hint: 'أي ملاحظات إضافية (اختياري)',
                      icon: Icons.note,
                      maxLines: 3,
                    ),
                  ],
                ),

                const SizedBox(height: 32),

                // أزرار الحفظ والإلغاء
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _saveTailor,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.purple.shade600,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          elevation: 2,
                        ),
                        child: _isLoading
                            ? const SizedBox(
                                height: 20,
                                width: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                ),
                              )
                            : Text(
                                _isEdit ? 'تحديث الخياط' : 'حفظ الخياط',
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: OutlinedButton(
                        onPressed: _isLoading ? null : () => Navigator.pop(context),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: Colors.grey[700],
                          side: BorderSide(color: Colors.grey[400]!),
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: const Text(
                          'إلغاء',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 32),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.purple.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    icon,
                    color: Colors.purple[700],
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[800],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    TextInputType? keyboardType,
    int maxLines = 1,
    String? Function(String?)? validator,
  }) {
    // تحديد ما إذا كان الحقل رقمي
    final isNumeric = keyboardType == TextInputType.number;

    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      inputFormatters: isNumeric ? NumberUtils.integerInputFormatters : null,
      maxLines: maxLines,
      validator: validator,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixIcon: Icon(icon, color: Colors.purple[600]),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey[300]!),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey[300]!),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.purple[600]!, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.red, width: 2),
        ),
        filled: true,
        fillColor: Colors.grey[50],
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      ),
    );
  }

  Future<void> _saveTailor() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final data = {
        'name': _nameController.text.trim(),
        'phone': _phoneController.text.trim(),
        'workType': _workTypeController.text.trim(),
        'pricePerPiece': NumberUtils.parseDouble(_priceController.text.trim()),
        'address': _addressController.text.trim(),
        'notes': _notesController.text.trim(),
        'isCutter': _isCutter ? 1 : 0,
        'cutPrice': _isCutter && _cutPriceController.text.trim().isNotEmpty
            ? NumberUtils.parseDouble(_cutPriceController.text.trim())
            : null,
        'hasOtherWork': _hasOtherWork ? 1 : 0,
        'otherWorkType': _hasOtherWork ? _otherWorkTypeController.text.trim() : null,
        'otherWorkPrice': _hasOtherWork && _otherWorkPriceController.text.trim().isNotEmpty
            ? NumberUtils.parseDouble(_otherWorkPriceController.text.trim())
            : null,
        'dayName': AppDateUtils.getCurrentDayName(), // إضافة اسم اليوم
      };

      if (_isEdit) {
        // استخدام النظام الجديد لتحديث الأسعار فورياً
        final updateResult = await DBHelper.updateTailorWithPriceUpdate(widget.tailorToEdit!['id'], data);
        final updatedWorksCount = updateResult['updatedWorksCount'] as int;

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.white),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      updatedWorksCount > 0
                        ? 'تم تحديث الخياط وتحديث $updatedWorksCount عمل بالأسعار الجديدة'
                        : 'تم تحديث بيانات الخياط بنجاح',
                      style: const TextStyle(fontSize: 14),
                    ),
                  ),
                ],
              ),
              backgroundColor: Colors.green[600],
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
              duration: Duration(seconds: updatedWorksCount > 0 ? 4 : 3),
            ),
          );
        }
      } else {
        await DBHelper.insertTailor(data);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.white),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(AppDateUtils.getTailorAddedMessage()),
                  ),
                ],
              ),
              backgroundColor: Colors.green[600],
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          );
        }
      }

      if (mounted) {
        Navigator.pop(context, true);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(
                  child: Text('خطأ في ${_isEdit ? 'تحديث' : 'إضافة'} الخياط: $e'),
                ),
              ],
            ),
            backgroundColor: Colors.red[600],
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
