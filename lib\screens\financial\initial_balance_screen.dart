// lib/screens/financial/initial_balance_screen.dart

import 'package:flutter/material.dart';
import '../../models/db_helper.dart';
import '../../models/financial_models.dart';
import '../../measurements/measurement_styles.dart';
import '../../utils/number_utils.dart';

class InitialBalanceScreen extends StatefulWidget {
  final int tailorId;
  final String tailorName;

  const InitialBalanceScreen({
    super.key,
    required this.tailorId,
    required this.tailorName,
  });

  @override
  State<InitialBalanceScreen> createState() => _InitialBalanceScreenState();
}

class _InitialBalanceScreenState extends State<InitialBalanceScreen> {
  final _formKey = GlobalKey<FormState>();
  final _debtController = TextEditingController();
  final _extraController = TextEditingController();

  bool _isLoading = false;
  InitialBalance? _existingBalance;

  @override
  void initState() {
    super.initState();
    _loadExistingBalance();
  }

  @override
  void dispose() {
    _debtController.dispose();
    _extraController.dispose();
    super.dispose();
  }

  Future<void> _loadExistingBalance() async {
    setState(() => _isLoading = true);

    try {
      final balanceData = await DBHelper.getInitialBalanceByTailorId(widget.tailorId);
      if (balanceData != null) {
        _existingBalance = InitialBalance.fromMap(balanceData);
        _debtController.text = NumberUtils.formatForInput(_existingBalance!.debtFromFirstMonth);
        _extraController.text = NumberUtils.formatForInput(_existingBalance!.extraFromFirstMonth);
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في تحميل البيانات: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _saveBalance() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final debtAmount = NumberUtils.parseDouble(_debtController.text);
      final extraAmount = NumberUtils.parseDouble(_extraController.text);

      final balanceData = {
        'tailorId': widget.tailorId,
        'debtFromFirstMonth': debtAmount,
        'extraFromFirstMonth': extraAmount,
      };

      if (_existingBalance != null) {
        // تحديث الرصيد الموجود
        await DBHelper.updateInitialBalance(_existingBalance!.id!, balanceData);
        _showSuccessSnackBar('تم تحديث الرصيد المرحل بنجاح');
      } else {
        // إضافة رصيد جديد
        await DBHelper.insertInitialBalance(balanceData);
        _showSuccessSnackBar('تم حفظ الرصيد المرحل بنجاح');
      }

      Navigator.pop(context, true);
    } catch (e) {
      _showErrorSnackBar('خطأ في حفظ البيانات: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: MeasurementStyles.successColor,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: MeasurementStyles.errorColor,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: MeasurementStyles.backgroundColor,
      appBar: AppBar(
        title: Text(
          'الرصيد المرحل - ${widget.tailorName}',
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 18,
          ),
        ),
        backgroundColor: MeasurementStyles.primaryColor,
        foregroundColor: MeasurementStyles.whiteColor,
        elevation: 0,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // شرح الميزة
                    MeasurementStyles.modernCard(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: MeasurementStyles.primaryColor.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: const Icon(
                                  Icons.info_outline_rounded,
                                  color: MeasurementStyles.primaryColor,
                                  size: 24,
                                ),
                              ),
                              const SizedBox(width: 12),
                              const Expanded(
                                child: Text(
                                  'إدارة الرصيد المرحل من الشهر الأول',
                                  style: MeasurementStyles.cardTitleStyle,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          const Text(
                            'يمكنك هنا إدخال المبالغ المتبقية أو الزائدة من الشهر الأول لتكون نقطة البداية في حساب الخياط.',
                            style: MeasurementStyles.cardSubtitleStyle,
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 20),

                    // نموذج الإدخال
                    MeasurementStyles.modernCard(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          MeasurementStyles.sectionTitle(
                            'بيانات الرصيد المرحل',
                            icon: Icons.account_balance_wallet_rounded,
                          ),

                          const SizedBox(height: 16),

                          // مبلغ متأخر من الشهر الأول
                          TextFormField(
                            controller: _debtController,
                            keyboardType: TextInputType.number,
                            inputFormatters: NumberUtils.integerInputFormatters,
                            textAlign: TextAlign.center,
                            style: MeasurementStyles.normalTextStyle,
                            decoration: MeasurementStyles.inputDecoration(
                              'مبلغ متأخر من الشهر الأول (ريال)',
                              icon: Icons.trending_down_rounded,
                            ),
                            validator: (value) {
                              if (value != null && value.isNotEmpty) {
                                final amount = NumberUtils.parseInteger(value);
                                if (amount < 0) {
                                  return 'يرجى إدخال مبلغ صحيح';
                                }
                              }
                              return null;
                            },
                          ),

                          const SizedBox(height: 16),

                          // مبلغ زائد من الشهر الأول
                          TextFormField(
                            controller: _extraController,
                            keyboardType: TextInputType.number,
                            inputFormatters: NumberUtils.integerInputFormatters,
                            textAlign: TextAlign.center,
                            style: MeasurementStyles.normalTextStyle,
                            decoration: MeasurementStyles.inputDecoration(
                              'مبلغ زائد من الشهر الأول (ريال)',
                              icon: Icons.trending_up_rounded,
                            ),
                            validator: (value) {
                              if (value != null && value.isNotEmpty) {
                                final amount = NumberUtils.parseInteger(value);
                                if (amount < 0) {
                                  return 'يرجى إدخال مبلغ صحيح';
                                }
                              }
                              return null;
                            },
                          ),

                          const SizedBox(height: 20),

                          // عرض الرصيد الصافي
                          _buildNetBalanceDisplay(),

                          const SizedBox(height: 20),

                          // زر الحفظ
                          MeasurementStyles.saveButton(
                            _existingBalance != null ? 'تحديث الرصيد' : 'حفظ الرصيد',
                            _saveBalance,
                            isLoading: _isLoading,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildNetBalanceDisplay() {
    final debtAmount = NumberUtils.parseDouble(_debtController.text);
    final extraAmount = NumberUtils.parseDouble(_extraController.text);
    final netBalance = extraAmount - debtAmount;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: netBalance >= 0
            ? MeasurementStyles.successColor.withOpacity(0.1)
            : MeasurementStyles.errorColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: netBalance >= 0
              ? MeasurementStyles.successColor.withOpacity(0.3)
              : MeasurementStyles.errorColor.withOpacity(0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            netBalance >= 0
                ? Icons.trending_up_rounded
                : Icons.trending_down_rounded,
            color: netBalance >= 0
                ? MeasurementStyles.successColor
                : MeasurementStyles.errorColor,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'الرصيد الصافي',
                  style: MeasurementStyles.cardSubtitleStyle,
                ),
                Text(
                  '${NumberUtils.formatIntegerDisplay(netBalance.abs())} ريال',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: netBalance >= 0
                        ? MeasurementStyles.successColor
                        : MeasurementStyles.errorColor,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: netBalance >= 0
                  ? MeasurementStyles.successColor
                  : MeasurementStyles.errorColor,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              netBalance >= 0 ? 'رصيد موجب' : 'رصيد سالب',
              style: const TextStyle(
                color: MeasurementStyles.whiteColor,
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
