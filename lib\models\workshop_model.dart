// class WorkshopModel {
//   final int? id; // هذا هو id المحلي في SQLite
//   final String name;
//   final bool isQassas;
//   final double? qassasPrice;

//   WorkshopModel({
//     this.id,
//     required this.name,
//     this.isQassas = false,
//     this.qassasPrice,
//   });

//   // لتحويل من خريطة (من SQLite أو Firebase) إلى كائن ورشة
//   factory WorkshopModel.fromMap(Map<String, dynamic> map) {
//     return WorkshopModel(
//       id: map['id'],
//       name: map['name'],
//       isQassas: map['isQassas'] == 1 || map['isQassas'] == true,
//       qassasPrice: map['qassasPrice'] != null
//           ? double.tryParse(map['qassasPrice'].toString())
//           : null,
//     );
//   }

//   // لتحويل الكائن إلى خريطة (لحفظه في SQLite أو Firebase)
//   Map<String, dynamic> toMap() {
//     return {
//       'id': id,
//       'name': name,
//       'isQassas': isQassas ? 1 : 0, // SQLite لا يدعم boolean
//       'qassasPrice': qassasPrice,
//     };
//   }

//   // نسخة جديدة معدّلة من نفس الورشة
//   WorkshopModel copyWith({
//     int? id,
//     String? name,
//     bool? isQassas,
//     double? qassasPrice,
//   }) {
//     return WorkshopModel(
//       id: id ?? this.id,
//       name: name ?? this.name,
//       isQassas: isQassas ?? this.isQassas,
//       qassasPrice: qassasPrice ?? this.qassasPrice,
//     );
//   }
// }









// class WorkshopModel {
//   final int? id;            // هذا هو id المحلي في SQLite
//   final String name;
//   final bool isQassas;
//   final double qassasPrice;
//   final String otherTypeName;   // اسم النوع الإضافي
//   final double otherTypePrice;  // سعر النوع الإضافي

//   WorkshopModel({
//     this.id,
//     required this.name,
//     this.isQassas = false,
//     this.qassasPrice = 0.0,
//     this.otherTypeName = '',
//     this.otherTypePrice = 0.0,
//   });

//   // لتحويل من خريطة (من SQLite أو Firebase) إلى كائن ورشة
//   factory WorkshopModel.fromMap(Map<String, dynamic> map) {
//     return WorkshopModel(
//       id: map['id'] as int?,
//       name: map['name'] as String,
//       isQassas: (map['isQassas'] as int) == 1,
//       qassasPrice: (map['qassasPrice'] as num?)?.toDouble() ?? 0.0,
//       otherTypeName: map['otherTypeName'] as String? ?? '',
//       otherTypePrice: (map['otherTypePrice'] as num?)?.toDouble() ?? 0.0,
//     );
//   }

//   // لتحويل الكائن إلى خريطة (لحفظه في SQLite أو Firebase)
//   Map<String, dynamic> toMap() {
//     return {
//       'id': id,
//       'name': name,
//       'isQassas': isQassas ? 1 : 0,
//       'qassasPrice': qassasPrice,
//       'otherTypeName': otherTypeName,
//       'otherTypePrice': otherTypePrice,
//     };
//   }

//   // نسخة جديدة معدّلة من نفس الورشة
//   WorkshopModel copyWith({
//     int? id,
//     String? name,
//     bool? isQassas,
//     double? qassasPrice,
//     String? otherTypeName,
//     double? otherTypePrice,
//   }) {
//     return WorkshopModel(
//       id: id ?? this.id,
//       name: name ?? this.name,
//       isQassas: isQassas ?? this.isQassas,
//       qassasPrice: qassasPrice ?? this.qassasPrice,
//       otherTypeName: otherTypeName ?? this.otherTypeName,
//       otherTypePrice: otherTypePrice ?? this.otherTypePrice,
//     );
//   }
// }




class WorkshopModel {
  final int? id;
  final String name;
  final String ownerPhone;
  final String workType;
  final double pricePerPiece;
  final bool isQassas;
  final double qassasPrice;
  final String otherTypeName;
  final double otherTypePrice;
  final DateTime createdAt;

  WorkshopModel({
    this.id,
    required this.name,
    required this.ownerPhone,
    required this.workType,
    required this.pricePerPiece,
    this.isQassas = false,
    this.qassasPrice = 0.0,
    this.otherTypeName = '',
    this.otherTypePrice = 0.0,
    required this.createdAt,
  });

  factory WorkshopModel.fromMap(Map<String, dynamic> map) {
    return WorkshopModel(
      id: map['id'] as int?,
      name: map['name'] as String,
      ownerPhone: map['ownerPhone'] as String,
      workType: map['workType'] as String,
      pricePerPiece: (map['pricePerPiece'] as num).toDouble(),
      isQassas: (map['isQassas'] as int) == 1,
      qassasPrice: (map['qassasPrice'] as num).toDouble(),
      otherTypeName: map['otherTypeName'] as String,
      otherTypePrice: (map['otherTypePrice'] as num).toDouble(),
      createdAt: DateTime.parse(map['createdAt'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'ownerPhone': ownerPhone,
      'workType': workType,
      'pricePerPiece': pricePerPiece,
      'isQassas': isQassas ? 1 : 0,
      'qassasPrice': qassasPrice,
      'otherTypeName': otherTypeName,
      'otherTypePrice': otherTypePrice,
      'createdAt': createdAt.toIso8601String(),
    };
  }
    }