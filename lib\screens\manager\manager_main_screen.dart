import 'package:flutter/material.dart';
import 'dart:math';
import 'tailors_home_screen.dart';
import 'manager_measurements_screen.dart';
import 'manager_invoices_screen.dart';
import '../role_selection_screen.dart';
import '../measurement_detail_options_screen.dart';
import '../tailor_home_screen.dart';
import '../../models/db_helper.dart';
import '../../widgets/export_print_buttons.dart';

class ManagerMainScreen extends StatefulWidget {
  const ManagerMainScreen({super.key});

  @override
  State<ManagerMainScreen> createState() => _ManagerMainScreenState();
}

class _ManagerMainScreenState extends State<ManagerMainScreen> {
  int _currentIndex = 0;
  List<Map<String, dynamic>> _randomTailors = [];

  // قائمة خياطين عشوائية للعرض
  final List<Map<String, dynamic>> _allTailors = [
    {
      'name': 'أحمد محمد',
      'phone': '0501234567',
      'specialty': 'ثياب رجالية',
      'rating': 4.8,
      'workshopsCount': 3,
      'avatar': 'A',
    },
    {
      'name': 'محمد علي',
      'phone': '0507654321',
      'specialty': 'بدلات',
      'rating': 4.6,
      'workshopsCount': 2,
      'avatar': 'م',
    },
    {
      'name': 'عبدالله سالم',
      'phone': '0551234567',
      'specialty': 'ثياب نسائية',
      'rating': 4.9,
      'workshopsCount': 4,
      'avatar': 'ع',
    },
    {
      'name': 'سعد الأحمد',
      'phone': '0561234567',
      'specialty': 'ثياب رجالية',
      'rating': 4.7,
      'workshopsCount': 1,
      'avatar': 'س',
    },
    {
      'name': 'خالد محمود',
      'phone': '0571234567',
      'specialty': 'بدلات وثياب',
      'rating': 4.5,
      'workshopsCount': 5,
      'avatar': 'خ',
    },
    {
      'name': 'فهد العتيبي',
      'phone': '0581234567',
      'specialty': 'ثياب تراثية',
      'rating': 4.8,
      'workshopsCount': 2,
      'avatar': 'ف',
    },
  ];

  @override
  void initState() {
    super.initState();
    _generateRandomTailors();
  }

  void _generateRandomTailors() {
    final random = Random();
    final shuffled = List<Map<String, dynamic>>.from(_allTailors);
    shuffled.shuffle(random);

    setState(() {
      _randomTailors = shuffled.take(4).toList();
    });
  }

  Future<void> _resetDatabase() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد إعادة التعيين'),
        content: const Text('هل أنت متأكد من إعادة تعيين قاعدة البيانات؟\nهذا سيحذف جميع البيانات!'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('نعم، إعادة تعيين'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await DBHelper.resetDatabase();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إعادة تعيين قاعدة البيانات بنجاح'),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 3),
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في إعادة التعيين: $e'),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 5),
            ),
          );
        }
      }
    }
  }

  // دالة عرض حوار تبديل الدور
  void _showRoleSwitchDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
            title: Row(
              children: [
                Icon(Icons.swap_horiz, color: Colors.teal[600]),
                const SizedBox(width: 12),
                const Text('تبديل الدور'),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  'اختر النظام الذي تريد الانتقال إليه:',
                  style: TextStyle(fontSize: 16),
                ),
                const SizedBox(height: 20),

                // زر نظام الخياط
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: () {
                      Navigator.pop(context);
                      Navigator.pushReplacement(
                        context,
                        MaterialPageRoute(
                          builder: (_) => const TailorHomeScreen(),
                        ),
                      );
                    },
                    icon: const Icon(Icons.person),
                    label: const Text('نظام الخياط'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.teal[600],
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ),

                const SizedBox(height: 12),

                // زر اختيار الدور
                SizedBox(
                  width: double.infinity,
                  child: OutlinedButton.icon(
                    onPressed: () {
                      Navigator.pop(context);
                      Navigator.pushReplacement(
                        context,
                        MaterialPageRoute(
                          builder: (_) => const RoleSelectionScreen(),
                        ),
                      );
                    },
                    icon: const Icon(Icons.home),
                    label: const Text('اختيار الدور'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.purple[700],
                      side: BorderSide(color: Colors.purple[700]!),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
            ],
          );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        body: IndexedStack(
          index: _currentIndex,
          children: [
            _buildHomeTab(),
            const TailorsHomeScreen(),
            const ManagerMeasurementsScreen(),
            const ManagerInvoicesScreen(),
          ],
        ),
        bottomNavigationBar: BottomNavigationBar(
          type: BottomNavigationBarType.fixed,
          currentIndex: _currentIndex,
          onTap: (index) {
            setState(() {
              _currentIndex = index;
            });
          },
          selectedItemColor: Colors.purple[700],
          unselectedItemColor: Colors.grey[600],
          backgroundColor: Colors.white,
          elevation: 8,
          items: const [
            BottomNavigationBarItem(
              icon: Icon(Icons.home),
              label: 'الرئيسية',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.people),
              label: 'إدارة الخياطين',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.straighten),
              label: 'إدارة المقاسات',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.receipt_long),
              label: 'إدارة الفواتير',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHomeTab() {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'لوحة تحكم مدير الخياطين',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.purple[700],
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () {
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(
                builder: (_) => const RoleSelectionScreen(),
              ),
            );
          },
          tooltip: 'العودة لاختيار الدور',
        ),
        actions: [
          // زر التبديل إلى نظام الخياط
          IconButton(
            icon: const Icon(Icons.person, color: Colors.white),
            onPressed: () {
              _showRoleSwitchDialog();
            },
            tooltip: 'التبديل إلى نظام الخياط',
          ),
          IconButton(
            icon: const Icon(Icons.settings, color: Colors.white),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (_) => const MeasurementDetailOptionsScreen(),
                ),
              );
            },
            tooltip: 'إدارة خيارات التفصيل',
          ),
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: _generateRandomTailors,
            tooltip: 'تحديث الخياطين',
          ),
          IconButton(
            icon: const Icon(Icons.restore, color: Colors.white),
            onPressed: _resetDatabase,
            tooltip: 'إعادة تعيين قاعدة البيانات',
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          _generateRandomTailors();
        },
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // بطاقة الترحيب
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.purple[600]!, Colors.purple[400]!],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.purple.withOpacity(0.3),
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: const Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.supervisor_account,
                          color: Colors.white,
                          size: 32,
                        ),
                        SizedBox(width: 12),
                        Text(
                          'مرحباً بك',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 8),
                    Text(
                      'إدارة شاملة لفريق الخياطين والأعمال',
                      style: TextStyle(
                        color: Colors.white70,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 24),

              // عنوان الخياطين المقترحين
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'خياطين مقترحين',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey[800],
                    ),
                  ),
                  TextButton.icon(
                    onPressed: _generateRandomTailors,
                    icon: const Icon(Icons.refresh, size: 18),
                    label: const Text('تحديث'),
                    style: TextButton.styleFrom(
                      foregroundColor: Colors.purple[700],
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // قائمة الخياطين العشوائيين
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _randomTailors.length,
                itemBuilder: (context, index) {
                  final tailor = _randomTailors[index];
                  return _buildTailorCard(tailor);
                },
              ),

              const SizedBox(height: 24),

              // إحصائيات سريعة
              _buildQuickStats(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTailorCard(Map<String, dynamic> tailor) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            // صورة رمزية
            CircleAvatar(
              radius: 30,
              backgroundColor: Colors.purple[100],
              child: Text(
                tailor['avatar'],
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.purple[700],
                ),
              ),
            ),

            const SizedBox(width: 16),

            // معلومات الخياط
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    tailor['name'],
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    tailor['specialty'],
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 14,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      const Icon(Icons.star, color: Colors.amber, size: 16),
                      const SizedBox(width: 4),
                      Text(
                        '${tailor['rating']}',
                        style: TextStyle(
                          color: Colors.grey[700],
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(width: 16),
                      const Icon(Icons.work, color: Colors.blue, size: 16),
                      const SizedBox(width: 4),
                      Text(
                        '${tailor['workshopsCount']} معمل',
                        style: TextStyle(
                          color: Colors.grey[700],
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // زر إرسال طلب صداقة
            ElevatedButton(
              onPressed: () {
                _sendFriendRequest(tailor);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.purple[600],
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
              child: const Text(
                'إرسال طلب',
                style: TextStyle(fontSize: 12),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStats() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إحصائيات سريعة',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey[800],
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  icon: Icons.people,
                  title: 'الخياطين',
                  value: '12',
                  color: Colors.blue,
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  icon: Icons.work,
                  title: 'الأعمال',
                  value: '48',
                  color: Colors.green,
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  icon: Icons.attach_money,
                  title: 'الإيرادات',
                  value: '15,240',
                  color: Colors.orange,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String title,
    required String value,
    required Color color,
  }) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(icon, color: color, size: 24),
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.grey[800],
          ),
        ),
        Text(
          title,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  void _sendFriendRequest(Map<String, dynamic> tailor) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Text('إرسال طلب صداقة'),
        content: Text('هل تريد إرسال طلب صداقة إلى ${tailor['name']}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('تم إرسال طلب الصداقة إلى ${tailor['name']}'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.purple[600],
              foregroundColor: Colors.white,
            ),
            child: const Text('إرسال'),
          ),
        ],
      ),
    );
  }
}
