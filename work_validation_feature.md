# ميزة التحقق من صحة البيانات في إضافة العمل

## 🎯 الهدف:
إضافة تحقق من صحة البيانات في صفحة إضافة العمل بحيث يجب ملء حقل واحد على الأقل من الحقول المحددة قبل الحفظ.

## ✅ ما تم إضافته:

### 1. **دالة التحقق من صحة البيانات**:
```dart
bool _validateRequiredFields() {
  // الحقول المطلوبة
  final quantity = _quantityController.text.trim();
  final expense = _expenseController.text.trim();
  final cutQuantity = widget.isQassas ? _cutQuantityController.text.trim() : '';
  final otherCount = widget.hasOtherTypes ? _otherCountController.text.trim() : '';
  
  // التحقق من وجود قيمة في حقل واحد على الأقل
  bool hasQuantity = quantity.isNotEmpty && NumberUtils.parseInteger(quantity) > 0;
  bool hasExpense = expense.isNotEmpty && NumberUtils.parseDouble(expense) > 0;
  bool hasCutQuantity = widget.isQassas && cutQuantity.isNotEmpty && NumberUtils.parseInteger(cutQuantity) > 0;
  bool hasOtherCount = widget.hasOtherTypes && otherCount.isNotEmpty && NumberUtils.parseInteger(otherCount) > 0;
  
  // إذا لم يتم ملء أي حقل
  if (!hasQuantity && !hasExpense && !hasCutQuantity && !hasOtherCount) {
    _showValidationError();
    return false;
  }
  
  return true;
}
```

### 2. **دالة عرض رسالة الخطأ**:
```dart
void _showValidationError() {
  List<String> requiredFields = ['عدد القطع', 'المصروف اليومي'];
  
  if (widget.isQassas) {
    requiredFields.add('عدد القطع المقصوصة');
  }
  
  if (widget.hasOtherTypes) {
    requiredFields.add('عدد الأنواع الأخرى من الثياب');
  }
  
  String message = 'يرجى ملء واحد من الحقول التالية:\n\n';
  for (int i = 0; i < requiredFields.length; i++) {
    message += '• ${requiredFields[i]}';
    if (i < requiredFields.length - 1) {
      message += '\n';
    }
  }
  
  // عرض حوار التنبيه
  showDialog(/* ... */);
}
```

### 3. **تحديث دالة الحفظ**:
```dart
Future<void> _saveWork() async {
  // التحقق من صحة البيانات - يجب ملء حقل واحد على الأقل
  if (!_validateRequiredFields()) {
    return; // إيقاف الحفظ إذا لم تكن البيانات صحيحة
  }
  
  // باقي كود الحفظ...
}
```

## 🎯 الحقول المطلوبة:

### الحقول الأساسية (دائماً موجودة):
1. **عدد القطع** - عدد الثياب المنجزة
2. **المصروف اليومي** - المصروفات اليومية

### الحقول الاختيارية (حسب نوع المعمل):
3. **عدد القطع المقصوصة** - إذا كان المعمل يعمل قصاص (`isQassas = true`)
4. **عدد الأنواع الأخرى من الثياب** - إذا كان المعمل يعمل أنواع أخرى (`hasOtherTypes = true`)

## 🎨 تصميم رسالة الخطأ:

### مثال للمعمل العادي:
```
⚠️ تنبيه

يرجى ملء واحد من الحقول التالية:

• عدد القطع
• المصروف اليومي

[فهمت]
```

### مثال للمعمل مع قصاص وأنواع أخرى:
```
⚠️ تنبيه

يرجى ملء واحد من الحقول التالية:

• عدد القطع
• المصروف اليومي
• عدد القطع المقصوصة
• عدد الأنواع الأخرى من الثياب

[فهمت]
```

## 🔧 منطق التحقق:

### شروط القبول:
- ✅ **عدد القطع > 0** أو
- ✅ **المصروف اليومي > 0** أو
- ✅ **عدد القطع المقصوصة > 0** (إذا كان متاح) أو
- ✅ **عدد الأنواع الأخرى > 0** (إذا كان متاح)

### شروط الرفض:
- ❌ **جميع الحقول فارغة** أو تحتوي على قيم صفر أو سالبة

### أمثلة:

#### مقبول ✅:
- عدد القطع: 5، المصروف: 0 → **مقبول**
- عدد القطع: 0، المصروف: 100 → **مقبول**
- عدد القطع: 3، المصروف: 50 → **مقبول**
- عدد القطع المقصوصة: 2 (والباقي فارغ) → **مقبول**

#### مرفوض ❌:
- جميع الحقول فارغة → **مرفوض**
- جميع الحقول تحتوي على صفر → **مرفوض**
- جميع الحقول تحتوي على قيم سالبة → **مرفوض**

## 🚀 كيفية الاختبار:

### الاختبار الأول: حفظ بدون بيانات
1. **افتح صفحة إضافة عمل**
2. **اتركجميع الحقول فارغة**
3. **اضغط زر "حفظ"**
4. **النتيجة المتوقعة**: ظهور رسالة تنبيه ✅

### الاختبار الثاني: حفظ مع عدد القطع فقط
1. **أدخل عدد القطع: 5**
2. **اترك باقي الحقول فارغة**
3. **اضغط زر "حفظ"**
4. **النتيجة المتوقعة**: حفظ ناجح ✅

### الاختبار الثالث: حفظ مع المصروف فقط
1. **أدخل المصروف: 100**
2. **اترك باقي الحقول فارغة**
3. **اضغط زر "حفظ"**
4. **النتيجة المتوقعة**: حفظ ناجح ✅

### الاختبار الرابع: معمل مع قصاص
1. **افتح معمل يعمل قصاص**
2. **أدخل عدد القطع المقصوصة: 3**
3. **اترك باقي الحقول فارغة**
4. **اضغط زر "حفظ"**
5. **النتيجة المتوقعة**: حفظ ناجح ✅

### الاختبار الخامس: معمل مع أنواع أخرى
1. **افتح معمل يعمل أنواع أخرى**
2. **أدخل عدد الأنواع الأخرى: 2**
3. **اترك باقي الحقول فارغة**
4. **اضغط زر "حفظ"**
5. **النتيجة المتوقعة**: حفظ ناجح ✅

## 🎯 الفوائد:

### 1. **منع الحفظ الفارغ**:
- ✅ لا يمكن حفظ عمل بدون أي بيانات مفيدة
- ✅ ضمان وجود قيمة في حقل واحد على الأقل

### 2. **رسائل واضحة**:
- ✅ رسالة تنبيه واضحة ومفهومة
- ✅ قائمة بالحقول المطلوبة حسب نوع المعمل
- ✅ تصميم أنيق مع أيقونة تحذير

### 3. **مرونة في الاستخدام**:
- ✅ يمكن ملء أي حقل من الحقول المطلوبة
- ✅ لا يجبر المستخدم على ملء حقول معينة
- ✅ يتكيف مع نوع المعمل (قصاص، أنواع أخرى)

### 4. **تجربة مستخدم محسنة**:
- ✅ منع الأخطاء قبل حدوثها
- ✅ توجيه واضح للمستخدم
- ✅ عدم فقدان البيانات المدخلة

## 🎉 النتيجة النهائية:

### قبل الإضافة:
- ❌ يمكن حفظ عمل فارغ بدون أي بيانات
- ❌ لا توجد رسائل تنبيه
- ❌ إمكانية حفظ بيانات غير مفيدة

### بعد الإضافة:
- ✅ **تحقق ذكي** من صحة البيانات
- ✅ **رسائل تنبيه واضحة** ومفيدة
- ✅ **منع الحفظ الفارغ** تماماً
- ✅ **مرونة في الاستخدام** حسب نوع المعمل
- ✅ **تجربة مستخدم محسنة** وآمنة

**ميزة التحقق من صحة البيانات جاهزة ومفعلة!** 🚀✨

الآن لا يمكن حفظ عمل بدون بيانات مفيدة، وسيحصل المستخدم على توجيه واضح لما يجب ملؤه.
