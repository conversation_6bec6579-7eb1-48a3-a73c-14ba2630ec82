// lib/screens/workshop_info_screen.dart

import 'package:flutter/material.dart';
import '../models/db_helper.dart';
import '../measurements/measurement_styles.dart';
import '../utils/number_utils.dart';

class WorkshopInfoScreen extends StatefulWidget {
  final Map<String, dynamic> workshop;

  const WorkshopInfoScreen({
    super.key,
    required this.workshop,
  });

  @override
  State<WorkshopInfoScreen> createState() => _WorkshopInfoScreenState();
}

class _WorkshopInfoScreenState extends State<WorkshopInfoScreen> {
  bool _isLoading = false;
  int _totalWorks = 0;
  double _totalIncome = 0.0;
  double _totalExpenses = 0.0;

  @override
  void initState() {
    super.initState();
    _loadWorkshopStats();
  }

  Future<void> _loadWorkshopStats() async {
    setState(() => _isLoading = true);

    try {
      // جلب إحصائيات المعمل
      final works = await DBHelper.getWorksForWorkshop(widget.workshop['id']);
      _totalWorks = works.length;

      // حساب إجمالي الدخل والمصروفات
      _totalIncome = 0.0;
      _totalExpenses = 0.0;

      for (var work in works) {
        final quantity = (work['quantity'] as num?)?.toInt() ?? 0;
        final cutQuantity = (work['cutQuantity'] as num?)?.toInt() ?? 0;
        final pricePerPiece = (work['pricePerPiece'] as num?)?.toDouble() ?? 0.0;
        final expense = (work['expense'] as num?)?.toDouble() ?? 0.0;

        _totalIncome += (quantity - cutQuantity) * pricePerPiece;
        _totalExpenses += expense;
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في تحميل البيانات: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: MeasurementStyles.errorColor,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: MeasurementStyles.backgroundColor,
      appBar: AppBar(
        title: const Text(
          'تفاصيل المعمل',
          style: TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 20,
          ),
        ),
        backgroundColor: MeasurementStyles.primaryColor,
        foregroundColor: MeasurementStyles.whiteColor,
        elevation: 0,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // معلومات المعمل الأساسية
                  _buildBasicInfoCard(),

                  const SizedBox(height: 16),

                  // معلومات الأسعار
                  _buildPricingInfoCard(),

                  const SizedBox(height: 16),

                  // إحصائيات المعمل
                  _buildStatsCard(),

                  const SizedBox(height: 16),

                  // معلومات إضافية
                  _buildAdditionalInfoCard(),
                ],
              ),
            ),
    );
  }

  Widget _buildBasicInfoCard() {
    return MeasurementStyles.modernCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: MeasurementStyles.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.factory_rounded,
                  color: MeasurementStyles.primaryColor,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'معلومات المعمل',
                      style: MeasurementStyles.cardSubtitleStyle,
                    ),
                    Text(
                      widget.workshop['name'] ?? 'غير محدد',
                      style: MeasurementStyles.cardTitleStyle,
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          _buildInfoRow(
            'اسم المعمل',
            widget.workshop['name'] ?? 'غير محدد',
            Icons.factory_rounded,
          ),

          const SizedBox(height: 12),

          _buildInfoRow(
            'رقم صاحب المعمل',
            widget.workshop['ownerPhone'] ?? 'غير محدد',
            Icons.phone_rounded,
          ),

          const SizedBox(height: 12),

          _buildInfoRow(
            'نوع العمل',
            widget.workshop['workType'] ?? 'غير محدد',
            Icons.work_rounded,
          ),

          if (widget.workshop['dayName'] != null) ...[
            const SizedBox(height: 12),
            _buildInfoRow(
              'يوم الإنشاء',
              widget.workshop['dayName'],
              Icons.today_rounded,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildPricingInfoCard() {
    final isQassas = (widget.workshop['isQassas'] ?? 0) == 1;
    final hasOtherTypes = (widget.workshop['otherTypeName'] as String? ?? '').isNotEmpty;

    return MeasurementStyles.modernCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          MeasurementStyles.sectionTitle(
            'معلومات الأسعار',
            icon: Icons.monetization_on_rounded,
          ),

          const SizedBox(height: 16),

          // السعر الأساسي
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: MeasurementStyles.successColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: MeasurementStyles.successColor.withOpacity(0.3),
              ),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.attach_money_rounded,
                  color: MeasurementStyles.successColor,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'سعر القطعة الأساسي',
                        style: MeasurementStyles.cardSubtitleStyle,
                      ),
                      Text(
                        '${NumberUtils.formatIntegerDisplay(widget.workshop['pricePerPiece'] ?? 0)} ريال',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: MeasurementStyles.successColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // سعر القصة إن وجد
          if (isQassas) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: MeasurementStyles.warningColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: MeasurementStyles.warningColor.withOpacity(0.3),
                ),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.content_cut_rounded,
                    color: MeasurementStyles.warningColor,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'سعر القصة',
                          style: MeasurementStyles.cardSubtitleStyle,
                        ),
                        Text(
                          '${NumberUtils.formatIntegerDisplay(widget.workshop['qassasPrice'] ?? 0)} ريال',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: MeasurementStyles.warningColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: MeasurementStyles.warningColor,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Text(
                      'قصاص',
                      style: TextStyle(
                        color: MeasurementStyles.whiteColor,
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],

          // النوع الإضافي إن وجد
          if (hasOtherTypes) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: MeasurementStyles.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: MeasurementStyles.primaryColor.withOpacity(0.3),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Row(
                    children: [
                      Icon(
                        Icons.category_rounded,
                        color: MeasurementStyles.primaryColor,
                        size: 20,
                      ),
                      SizedBox(width: 12),
                      Text(
                        'نوع إضافي',
                        style: MeasurementStyles.cardSubtitleStyle,
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        widget.workshop['otherTypeName'] ?? '',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: MeasurementStyles.primaryColor,
                        ),
                      ),
                      Text(
                        '${NumberUtils.formatIntegerDisplay(widget.workshop['otherTypePrice'] ?? 0)} ريال',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: MeasurementStyles.primaryColor,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildStatsCard() {
    final netProfit = _totalIncome - _totalExpenses;

    return MeasurementStyles.modernCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          MeasurementStyles.sectionTitle(
            'إحصائيات المعمل',
            icon: Icons.bar_chart_rounded,
          ),

          const SizedBox(height: 16),

          // عدد الأعمال
          _buildStatRow(
            'عدد الأعمال',
            '$_totalWorks عمل',
            Icons.work_outline_rounded,
            MeasurementStyles.primaryColor,
          ),

          const SizedBox(height: 12),

          // إجمالي الدخل
          _buildStatRow(
            'إجمالي الدخل',
            '${NumberUtils.formatIntegerDisplay(_totalIncome)} ريال',
            Icons.trending_up_rounded,
            MeasurementStyles.successColor,
          ),

          const SizedBox(height: 12),

          // إجمالي المصروفات
          _buildStatRow(
            'إجمالي المصروفات',
            '${NumberUtils.formatIntegerDisplay(_totalExpenses)} ريال',
            Icons.trending_down_rounded,
            MeasurementStyles.errorColor,
          ),

          const SizedBox(height: 16),

          // صافي الربح
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: netProfit >= 0
                  ? MeasurementStyles.successColor.withOpacity(0.1)
                  : MeasurementStyles.errorColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: netProfit >= 0
                    ? MeasurementStyles.successColor.withOpacity(0.3)
                    : MeasurementStyles.errorColor.withOpacity(0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  netProfit >= 0
                      ? Icons.account_balance_wallet_rounded
                      : Icons.warning_rounded,
                  color: netProfit >= 0
                      ? MeasurementStyles.successColor
                      : MeasurementStyles.errorColor,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'صافي الربح',
                        style: MeasurementStyles.cardSubtitleStyle,
                      ),
                      Text(
                        '${NumberUtils.formatIntegerDisplay(netProfit.abs())} ريال',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.w700,
                          color: netProfit >= 0
                              ? MeasurementStyles.successColor
                              : MeasurementStyles.errorColor,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: netProfit >= 0
                        ? MeasurementStyles.successColor
                        : MeasurementStyles.errorColor,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    netProfit >= 0 ? 'ربح' : 'خسارة',
                    style: const TextStyle(
                      color: MeasurementStyles.whiteColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAdditionalInfoCard() {
    final createdAt = DateTime.tryParse(widget.workshop['createdAt'] ?? '');
    final updatedAt = DateTime.tryParse(widget.workshop['updatedAt'] ?? '');

    return MeasurementStyles.modernCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          MeasurementStyles.sectionTitle(
            'معلومات إضافية',
            icon: Icons.info_outline_rounded,
          ),

          const SizedBox(height: 16),

          if (createdAt != null)
            _buildInfoRow(
              'تاريخ الإنشاء',
              '${createdAt.day}/${createdAt.month}/${createdAt.year}',
              Icons.calendar_today_rounded,
            ),

          if (updatedAt != null) ...[
            const SizedBox(height: 12),
            _buildInfoRow(
              'آخر تحديث',
              '${updatedAt.day}/${updatedAt.month}/${updatedAt.year}',
              Icons.update_rounded,
            ),
          ],

          const SizedBox(height: 12),

          _buildInfoRow(
            'حالة المزامنة',
            (widget.workshop['isSynced'] ?? 0) == 1 ? 'مزامن' : 'غير مزامن',
            (widget.workshop['isSynced'] ?? 0) == 1
                ? Icons.cloud_done_rounded
                : Icons.cloud_off_rounded,
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String title, String value, IconData icon) {
    return Row(
      children: [
        Icon(icon, color: MeasurementStyles.primaryColor, size: 20),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            title,
            style: MeasurementStyles.normalTextStyle,
          ),
        ),
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: MeasurementStyles.primaryColor,
          ),
        ),
      ],
    );
  }

  Widget _buildStatRow(String title, String value, IconData icon, Color color) {
    return Row(
      children: [
        Icon(icon, color: color, size: 20),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            title,
            style: MeasurementStyles.normalTextStyle,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: color,
          ),
        ),
      ],
    );
  }
}
