// ملف اختبار لصفحة التصفية الشهرية المحسنة مع المعالج
import 'package:flutter/material.dart';
import 'screens/financial/enhanced_monthly_settlement_screen.dart';

class TestSettlementIntegration extends StatelessWidget {
  const TestSettlementIntegration({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'اختبار التصفية الشهرية المحسنة',
      theme: ThemeData(
        primarySwatch: Colors.purple,
        fontFamily: 'Cairo',
      ),
      home: const EnhancedMonthlySettlementScreen(
        tailorId: 1, // معرف تجريبي
        tailorName: 'خياط تجريبي - التصفية المحسنة',
      ),
    );
  }
}

void main() {
  runApp(const TestSettlementIntegration());
}
