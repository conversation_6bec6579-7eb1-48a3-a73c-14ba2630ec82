// lib/widgets/app_drawer.dart

import 'package:flutter/material.dart';

class AppDrawer extends StatelessWidget {
  const AppDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          const DrawerHeader(
            decoration: BoxDecoration(
              color: Colors.teal,
            ),
            child: Text(
              'تطبيق الخياطة',
              style: TextStyle(
                color: Colors.white,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),

          // النظام المالي
          ListTile(
            leading: const Icon(Icons.account_balance, color: Colors.indigo),
            title: const Text(
              'النظام المالي',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
            subtitle: const Text('إدارة شاملة للأمور المالية'),
            onTap: () {
              Navigator.pop(context);
              _showWorkshopSelectionDialog(context);
            },
          ),
          const Divider(),

          // عن المطور
          ListTile(
            leading: const Icon(Icons.info, color: Colors.blue),
            title: const Text(
              'عن المطور',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
            onTap: () {
              Navigator.pop(context);
              // TODO: هنا يمكنك فتح شاشة "عن المطور" إذا رغبت
            },
          ),
          const Divider(),

          // تواصل معنا
          ListTile(
            leading: const Icon(Icons.contact_mail, color: Colors.orange),
            title: const Text(
              'تواصل معنا',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
            onTap: () {
              Navigator.pop(context);
              // TODO: هنا يمكنك فتح شاشة "تواصل معنا" إذا رغبت
            },
          ),
          const Divider(),

          // نسخة التطبيق
          const Padding(
            padding: EdgeInsets.symmetric(vertical: 20.0),
            child: Center(
              child: Text(
                'نسخة التطبيق: 1.0.0',
                style: TextStyle(
                  color: Colors.grey,
                  fontSize: 12,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showWorkshopSelectionDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختيار معمل'),
        content: const Text('يرجى اختيار معمل من الصفحة الرئيسية أولاً للوصول إلى النظام المالي'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }
}
