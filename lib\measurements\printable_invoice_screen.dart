import 'package:flutter/material.dart';

class PrintableInvoiceScreen extends StatelessWidget {
  const PrintableInvoiceScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text("طباعة فاتورة العميل"),
        backgroundColor: Colors.amber[700],
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: () {
              // TODO: تنفيذ الطباعة
            },
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Center(
                child: Column(
                  children: [
                    Icon(Icons.checkroom, size: 48, color: Colors.black),
                    SizedBox(height: 4),
                    Text("الهدف للخياطة الحديثة", style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                    SizedBox(height: 4),
                    Text("774377287", style: TextStyle(fontSize: 14)),
                  ],
                ),
              ),
              const Divider(height: 30, thickness: 1.2),
              buildInfoRow("رقم الفاتورة:", "106"),
              buildInfoRow("اسم العميل:", "كمال عبد الرحمن"),
              buildInfoRow("نوع القماش:", "صوف تركي"),
              buildInfoRow("تاريخ الاستلام:", "24-نوفمبر-2024"),
              buildInfoRow("الكمية:", "1"),
              const SizedBox(height: 20),
              const Text("المقاسات", style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
              const SizedBox(height: 10),
              buildInfoRow("الطول:", "144"),
              buildInfoRow("الكتف:", "48"),
              buildInfoRow("الكم:", "60"),
              buildInfoRow("الوسط:", "35"),
              buildInfoRow("الرقبة:", "18.6"),
              buildInfoRow("الجيوب:", "2"),
              buildInfoRow("الزرارات:", "3"),
              const SizedBox(height: 20),
              buildInfoRow("الملاحظات:", "بدون بطانة"),
              buildInfoRow("رقم الهاتف:", "0599999999"),
              const SizedBox(height: 30),
              Center(
                child: ElevatedButton.icon(
                  onPressed: () {
                    // TODO: تنفيذ الطباعة
                  },
                  icon: const Icon(Icons.print),
                  label: const Text("طباعة"),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.amber[800],
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 14),
                    textStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget buildInfoRow(String title, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        children: [
          Icon(Icons.label, size: 20, color: Colors.grey[600]),
          const SizedBox(width: 8),
          Text(title, style: const TextStyle(fontWeight: FontWeight.w600)),
          const Spacer(),
          Text(value, style: const TextStyle(fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }
}
