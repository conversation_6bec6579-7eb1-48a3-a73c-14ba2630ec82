buildscript {
    ext.kotlin_version = '1.8.10'
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        // تعريف Android Gradle Plugin متوافق مع Gradle 7.6.3
        classpath 'com.android.tools.build:gradle:7.4.2'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        // Firebase plugins removed - using SQLite only
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }

    // إصلاح تعارض Kotlin
    configurations.all {
        resolutionStrategy {
            force 'org.jetbrains.kotlin:kotlin-stdlib:1.8.10'
            force 'org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.10'
            force 'org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.10'
        }
    }
}

// ✅ تنظيم مجلدات البناء
rootProject.buildDir = '../build'

subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
    project.evaluationDependsOn(":app")
}

// ✅ مهمة تنظيف المشروع
tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
