import 'dart:io';
import 'dart:typed_data';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:path_provider/path_provider.dart';
import 'package:intl/intl.dart';
import 'package:flutter/services.dart';
import '../utils/date_utils.dart';

class ArabicPDFService {
  // خط عربي للـ PDF
  static pw.Font? _arabicFont;
  static pw.Font? _arabicBoldFont;
  
  /// تحميل الخطوط العربية
  static Future<void> _loadArabicFonts() async {
    if (_arabicFont != null && _arabicBoldFont != null) return;
    
    try {
      // محاولة تحميل خط Cairo
      try {
        final fontData = await rootBundle.load('assets/fonts/Cairo-Regular.ttf');
        final boldFontData = await rootBundle.load('assets/fonts/Cairo-Bold.ttf');
        _arabicFont = pw.Font.ttf(fontData);
        _arabicBoldFont = pw.Font.ttf(boldFontData);
        print('تم تحميل خط Cairo بنجاح');
        return;
      } catch (e) {
        print('فشل تحميل خط Cairo: $e');
      }
      
      // محاولة تحميل خط Amiri
      try {
        final fontData = await rootBundle.load('assets/fonts/Amiri-Regular.ttf');
        final boldFontData = await rootBundle.load('assets/fonts/Amiri-Bold.ttf');
        _arabicFont = pw.Font.ttf(fontData);
        _arabicBoldFont = pw.Font.ttf(boldFontData);
        print('تم تحميل خط Amiri بنجاح');
        return;
      } catch (e) {
        print('فشل تحميل خط Amiri: $e');
      }
      
      // استخدام خط افتراضي إذا فشل تحميل الخطوط العربية
      _arabicFont = pw.Font.helvetica();
      _arabicBoldFont = pw.Font.helvetica();
      print('تم استخدام الخط الافتراضي');
      
    } catch (e) {
      print('خطأ في تحميل الخطوط: $e');
      _arabicFont = pw.Font.helvetica();
      _arabicBoldFont = pw.Font.helvetica();
    }
  }

  /// إنشاء PDF للمعامل مع دعم العربية
  static Future<File> generateWorkshopPDF(Map<String, dynamic> workshop) async {
    try {
      await _loadArabicFonts();
      final pdf = pw.Document();

      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          textDirection: pw.TextDirection.rtl,
          build: (pw.Context context) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // العنوان الرئيسي
                pw.Container(
                  width: double.infinity,
                  padding: const pw.EdgeInsets.all(20),
                  decoration: pw.BoxDecoration(
                    color: PdfColor.fromHex('#E3F2FD'),
                    borderRadius: pw.BorderRadius.circular(10),
                  ),
                  child: pw.Column(
                    children: [
                      pw.Text(
                        'تفاصيل المعمل',
                        style: pw.TextStyle(
                          fontSize: 24,
                          fontWeight: pw.FontWeight.bold,
                          font: _arabicBoldFont,
                        ),
                      ),
                      pw.SizedBox(height: 10),
                      pw.Text(
                        'تاريخ التصدير: ${_getCurrentDate()}',
                        style: pw.TextStyle(
                          fontSize: 12,
                          font: _arabicFont,
                        ),
                      ),
                    ],
                  ),
                ),

                pw.SizedBox(height: 20),

                // معلومات المعمل
                pw.Container(
                  padding: const pw.EdgeInsets.all(15),
                  decoration: pw.BoxDecoration(
                    border: pw.Border.all(color: PdfColor.fromHex('#E0E0E0')),
                    borderRadius: pw.BorderRadius.circular(8),
                  ),
                  child: pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      _buildArabicInfoRow('اسم المعمل:', workshop['name'] ?? ''),
                      _buildArabicInfoRow('رقم هاتف المالك:', workshop['ownerPhone'] ?? ''),
                      _buildArabicInfoRow('نوع العمل:', workshop['workType'] ?? ''),
                      _buildArabicInfoRow('السعر لكل قطعة:', '${workshop['pricePerPiece'] ?? 0} ريال'),
                      if (workshop['isQassas'] == 1) ...[
                        _buildArabicInfoRow('يدعم القصة:', 'نعم'),
                        _buildArabicInfoRow('سعر القصة:', '${workshop['qassasPrice'] ?? 0} ريال'),
                      ],
                      if (workshop['otherTypeName']?.isNotEmpty == true) ...[
                        _buildArabicInfoRow('نوع عمل آخر:', workshop['otherTypeName']),
                        _buildArabicInfoRow('سعر العمل الآخر:', '${workshop['otherTypePrice'] ?? 0} ريال'),
                      ],
                      if (workshop['dayName'] != null)
                        _buildArabicInfoRow('يوم الإضافة:', workshop['dayName']),
                      _buildArabicInfoRow('تاريخ الإنشاء:', _formatArabicDate(workshop['createdAt'])),
                    ],
                  ),
                ),

                pw.Spacer(),

                // تذييل
                pw.Container(
                  width: double.infinity,
                  padding: const pw.EdgeInsets.all(10),
                  decoration: pw.BoxDecoration(
                    color: PdfColor.fromHex('#F5F5F5'),
                    borderRadius: pw.BorderRadius.circular(5),
                  ),
                  child: pw.Text(
                    'تم إنشاء هذا التقرير بواسطة تطبيق إدارة الخياطين',
                    style: pw.TextStyle(
                      fontSize: 10,
                      font: _arabicFont,
                    ),
                    textAlign: pw.TextAlign.center,
                  ),
                ),
              ],
            );
          },
        ),
      );

      return await _saveArabicPDF(pdf, 'معمل_${workshop['name']}_${DateTime.now().millisecondsSinceEpoch}');
    } catch (e) {
      throw Exception('فشل في إنشاء ملف PDF: ${e.toString()}');
    }
  }

  /// إنشاء PDF للأعمال مع دعم العربية
  static Future<File> generateWorksPDF(List<Map<String, dynamic>> works, {String? title}) async {
    try {
      await _loadArabicFonts();
      final pdf = pw.Document();

      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          textDirection: pw.TextDirection.rtl,
          build: (pw.Context context) {
            return [
              // العنوان
              pw.Container(
                width: double.infinity,
                padding: const pw.EdgeInsets.all(20),
                decoration: pw.BoxDecoration(
                  color: PdfColor.fromHex('#E8F5E8'),
                  borderRadius: pw.BorderRadius.circular(10),
                ),
                child: pw.Column(
                  children: [
                    pw.Text(
                      title ?? 'تقرير الأعمال',
                      style: pw.TextStyle(
                        fontSize: 24,
                        fontWeight: pw.FontWeight.bold,
                        font: _arabicBoldFont,
                      ),
                    ),
                    pw.SizedBox(height: 10),
                    pw.Text(
                      'تاريخ التصدير: ${_getCurrentDate()}',
                      style: pw.TextStyle(
                        fontSize: 12,
                        font: _arabicFont,
                      ),
                    ),
                    pw.Text(
                      'عدد الأعمال: ${works.length}',
                      style: pw.TextStyle(
                        fontSize: 12,
                        font: _arabicFont,
                      ),
                    ),
                  ],
                ),
              ),

              pw.SizedBox(height: 20),

              // قائمة الأعمال
              ...works.map((work) => pw.Container(
                margin: const pw.EdgeInsets.only(bottom: 15),
                padding: const pw.EdgeInsets.all(15),
                decoration: pw.BoxDecoration(
                  border: pw.Border.all(color: PdfColor.fromHex('#E0E0E0')),
                  borderRadius: pw.BorderRadius.circular(8),
                ),
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text(
                      'عمل: ${work['workshopName'] ?? ''}',
                      style: pw.TextStyle(
                        fontSize: 16,
                        fontWeight: pw.FontWeight.bold,
                        font: _arabicBoldFont,
                      ),
                    ),
                    pw.SizedBox(height: 10),
                    _buildArabicInfoRow('عدد القطع:', '${work['pieceCount'] ?? 0}'),
                    _buildArabicInfoRow('المصاريف اليومية:', '${work['dailyExpenses'] ?? 0} ريال'),
                    _buildArabicInfoRow('السعر لكل قطعة:', '${work['pricePerPiece'] ?? 0} ريال'),
                    _buildArabicInfoRow('الإجمالي:', '${work['totalPrice'] ?? 0} ريال'),
                    _buildArabicInfoRow('تاريخ الإنشاء:', _formatArabicDate(work['createdAt'])),
                  ],
                ),
              )),
            ];
          },
        ),
      );

      return await _saveArabicPDF(pdf, 'الأعمال_${DateTime.now().millisecondsSinceEpoch}');
    } catch (e) {
      throw Exception('فشل في إنشاء ملف PDF: ${e.toString()}');
    }
  }

  /// إنشاء PDF للمقاسات مع دعم العربية
  static Future<File> generateMeasurementsPDF(List<Map<String, dynamic>> measurements, String type) async {
    try {
      await _loadArabicFonts();
      final pdf = pw.Document();

      String typeTitle = '';
      switch (type) {
        case 'clothing':
          typeTitle = 'مقاسات الثياب';
          break;
        case 'suits':
          typeTitle = 'مقاسات البدل';
          break;
        case 'women':
          typeTitle = 'مقاسات النساء';
          break;
        default:
          typeTitle = 'المقاسات';
      }

      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          textDirection: pw.TextDirection.rtl,
          build: (pw.Context context) {
            return [
              // العنوان
              pw.Container(
                width: double.infinity,
                padding: const pw.EdgeInsets.all(20),
                decoration: pw.BoxDecoration(
                  color: PdfColor.fromHex('#F3E5F5'),
                  borderRadius: pw.BorderRadius.circular(10),
                ),
                child: pw.Column(
                  children: [
                    pw.Text(
                      typeTitle,
                      style: pw.TextStyle(
                        fontSize: 24,
                        fontWeight: pw.FontWeight.bold,
                        font: _arabicBoldFont,
                      ),
                    ),
                    pw.SizedBox(height: 10),
                    pw.Text(
                      'تاريخ التصدير: ${_getCurrentDate()}',
                      style: pw.TextStyle(
                        fontSize: 12,
                        font: _arabicFont,
                      ),
                    ),
                    pw.Text(
                      'عدد المقاسات: ${measurements.length}',
                      style: pw.TextStyle(
                        fontSize: 12,
                        font: _arabicFont,
                      ),
                    ),
                  ],
                ),
              ),

              pw.SizedBox(height: 20),

              // قائمة المقاسات
              ...measurements.map((measurement) => pw.Container(
                margin: const pw.EdgeInsets.only(bottom: 15),
                padding: const pw.EdgeInsets.all(15),
                decoration: pw.BoxDecoration(
                  border: pw.Border.all(color: PdfColors.grey300),
                  borderRadius: pw.BorderRadius.circular(8),
                ),
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text(
                      'مقاس: ${measurement['customerName'] ?? ''}',
                      style: pw.TextStyle(
                        fontSize: 16,
                        fontWeight: pw.FontWeight.bold,
                        font: _arabicBoldFont,
                      ),
                    ),
                    pw.SizedBox(height: 10),
                    _buildArabicInfoRow('رقم الهاتف:', measurement['phoneNumber'] ?? ''),
                    _buildArabicInfoRow('رقم الفاتورة:', measurement['billNumber'] ?? ''),
                    _buildArabicInfoRow('نوع القماش:', measurement['fabricType'] ?? ''),
                    _buildArabicInfoRow('الكمية:', '${measurement['quantity'] ?? 0}'),
                    _buildArabicInfoRow('المبلغ:', '${measurement['price'] ?? 0} ريال'),
                    _buildArabicInfoRow('المدفوع:', '${measurement['paid'] ?? 0} ريال'),
                    _buildArabicInfoRow('المتبقي:', '${measurement['remaining'] ?? 0} ريال'),
                    if (measurement['dayName'] != null)
                      _buildArabicInfoRow('يوم الإضافة:', measurement['dayName']),
                    _buildArabicInfoRow('تاريخ الإنشاء:', _formatArabicDate(measurement['createdAt'])),
                  ],
                ),
              )),
            ];
          },
        ),
      );

      return await _saveArabicPDF(pdf, '${typeTitle}_${DateTime.now().millisecondsSinceEpoch}');
    } catch (e) {
      throw Exception('فشل في إنشاء ملف PDF: ${e.toString()}');
    }
  }

  /// إنشاء PDF للفواتير مع دعم العربية
  static Future<File> generateInvoicesPDF(List<Map<String, dynamic>> invoices) async {
    try {
      await _loadArabicFonts();
      final pdf = pw.Document();

      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          textDirection: pw.TextDirection.rtl,
          build: (pw.Context context) {
            return [
              // العنوان
              pw.Container(
                width: double.infinity,
                padding: const pw.EdgeInsets.all(20),
                decoration: pw.BoxDecoration(
                  color: PdfColor.fromHex('#FFF3E0'),
                  borderRadius: pw.BorderRadius.circular(10),
                ),
                child: pw.Column(
                  children: [
                    pw.Text(
                      'تقرير الفواتير',
                      style: pw.TextStyle(
                        fontSize: 24,
                        fontWeight: pw.FontWeight.bold,
                        font: _arabicBoldFont,
                      ),
                    ),
                    pw.SizedBox(height: 10),
                    pw.Text(
                      'تاريخ التصدير: ${_getCurrentDate()}',
                      style: pw.TextStyle(
                        fontSize: 12,
                        font: _arabicFont,
                      ),
                    ),
                    pw.Text(
                      'عدد الفواتير: ${invoices.length}',
                      style: pw.TextStyle(
                        fontSize: 12,
                        font: _arabicFont,
                      ),
                    ),
                  ],
                ),
              ),

              pw.SizedBox(height: 20),

              // قائمة الفواتير
              ...invoices.map((invoice) => pw.Container(
                margin: const pw.EdgeInsets.only(bottom: 15),
                padding: const pw.EdgeInsets.all(15),
                decoration: pw.BoxDecoration(
                  border: pw.Border.all(color: PdfColor.fromHex('#E0E0E0')),
                  borderRadius: pw.BorderRadius.circular(8),
                ),
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text(
                      'فاتورة: ${invoice['invoiceNumber'] ?? ''}',
                      style: pw.TextStyle(
                        fontSize: 16,
                        fontWeight: pw.FontWeight.bold,
                        font: _arabicBoldFont,
                      ),
                    ),
                    pw.SizedBox(height: 10),
                    _buildArabicInfoRow('اسم المحل:', invoice['shopName'] ?? ''),
                    _buildArabicInfoRow('اسم العميل:', invoice['customerName'] ?? ''),
                    _buildArabicInfoRow('عدد الثياب:', '${invoice['clothesCount'] ?? 0}'),
                    _buildArabicInfoRow('الحالة:', invoice['isReady'] == 1 ? 'جاهزة' : 'غير جاهزة'),
                    _buildArabicInfoRow('تاريخ الإنشاء:', _formatArabicDate(invoice['createdAt'])),
                  ],
                ),
              )),
            ];
          },
        ),
      );

      return await _saveArabicPDF(pdf, 'الفواتير_${DateTime.now().millisecondsSinceEpoch}');
    } catch (e) {
      throw Exception('فشل في إنشاء ملف PDF: ${e.toString()}');
    }
  }

  /// إنشاء PDF للإحصائيات مع دعم العربية
  static Future<File> generateStatisticsPDF(Map<String, dynamic> stats, {String? title}) async {
    try {
      await _loadArabicFonts();
      final pdf = pw.Document();

      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          textDirection: pw.TextDirection.rtl,
          build: (pw.Context context) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // العنوان
                pw.Container(
                  width: double.infinity,
                  padding: const pw.EdgeInsets.all(20),
                  decoration: pw.BoxDecoration(
                    color: PdfColor.fromHex('#E1F5FE'),
                    borderRadius: pw.BorderRadius.circular(10),
                  ),
                  child: pw.Column(
                    children: [
                      pw.Text(
                        title ?? 'تقرير الإحصائيات',
                        style: pw.TextStyle(
                          fontSize: 24,
                          fontWeight: pw.FontWeight.bold,
                          font: _arabicBoldFont,
                        ),
                      ),
                      pw.SizedBox(height: 10),
                      pw.Text(
                        'تاريخ التصدير: ${_getCurrentDate()}',
                        style: pw.TextStyle(
                          fontSize: 12,
                          font: _arabicFont,
                        ),
                      ),
                    ],
                  ),
                ),

                pw.SizedBox(height: 20),

                // الإحصائيات
                pw.Container(
                  padding: const pw.EdgeInsets.all(15),
                  decoration: pw.BoxDecoration(
                    border: pw.Border.all(color: PdfColor.fromHex('#E0E0E0')),
                    borderRadius: pw.BorderRadius.circular(8),
                  ),
                  child: pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text(
                        'الإحصائيات المالية',
                        style: pw.TextStyle(
                          fontSize: 18,
                          fontWeight: pw.FontWeight.bold,
                          font: _arabicBoldFont,
                        ),
                      ),
                      pw.SizedBox(height: 15),
                      _buildArabicInfoRow('إجمالي الأرباح:', '${stats['totalEarnings'] ?? 0} ريال'),
                      _buildArabicInfoRow('إجمالي المصاريف:', '${stats['totalExpenses'] ?? 0} ريال'),
                      _buildArabicInfoRow('صافي الربح:', '${stats['netProfit'] ?? 0} ريال'),
                      _buildArabicInfoRow('عدد الأعمال:', '${stats['totalWorks'] ?? 0}'),
                      _buildArabicInfoRow('عدد المعامل:', '${stats['totalWorkshops'] ?? 0}'),
                      _buildArabicInfoRow('عدد الفواتير:', '${stats['totalInvoices'] ?? 0}'),
                      _buildArabicInfoRow('عدد المقاسات:', '${stats['totalMeasurements'] ?? 0}'),
                    ],
                  ),
                ),

                pw.Spacer(),

                // تذييل
                pw.Container(
                  width: double.infinity,
                  padding: const pw.EdgeInsets.all(10),
                  decoration: pw.BoxDecoration(
                    color: PdfColor.fromHex('#F5F5F5'),
                    borderRadius: pw.BorderRadius.circular(5),
                  ),
                  child: pw.Text(
                    'تم إنشاء هذا التقرير بواسطة تطبيق إدارة الخياطين',
                    style: pw.TextStyle(
                      fontSize: 10,
                      font: _arabicFont,
                    ),
                    textAlign: pw.TextAlign.center,
                  ),
                ),
              ],
            );
          },
        ),
      );

      return await _saveArabicPDF(pdf, 'الإحصائيات_${DateTime.now().millisecondsSinceEpoch}');
    } catch (e) {
      throw Exception('فشل في إنشاء ملف PDF: ${e.toString()}');
    }
  }

  // دوال مساعدة مع دعم العربية
  static pw.Widget _buildArabicInfoRow(String label, String value) {
    return pw.Padding(
      padding: const pw.EdgeInsets.symmetric(vertical: 3),
      child: pw.Row(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.SizedBox(
            width: 120,
            child: pw.Text(
              label,
              style: pw.TextStyle(
                fontWeight: pw.FontWeight.bold,
                font: _arabicBoldFont,
              ),
            ),
          ),
          pw.Expanded(
            child: pw.Text(
              value,
              style: pw.TextStyle(
                font: _arabicFont,
              ),
            ),
          ),
        ],
      ),
    );
  }

  static String _formatArabicDate(String? dateString) {
    if (dateString == null) return 'غير محدد';
    try {
      final date = DateTime.parse(dateString);
      return DateFormat('yyyy/MM/dd').format(date);
    } catch (e) {
      return 'غير محدد';
    }
  }

  static String _getCurrentDate() {
    return DateFormat('yyyy/MM/dd').format(DateTime.now());
  }

  static Future<File> _saveArabicPDF(pw.Document pdf, String fileName) async {
    try {
      final output = await getTemporaryDirectory();
      final file = File('${output.path}/$fileName.pdf');
      await file.writeAsBytes(await pdf.save());
      return file;
    } catch (e) {
      throw Exception('فشل في حفظ ملف PDF: ${e.toString()}');
    }
  }
}
