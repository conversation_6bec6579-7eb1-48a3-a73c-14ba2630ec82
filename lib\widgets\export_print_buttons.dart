import 'dart:io';
import 'package:flutter/material.dart';
import '../services/simple_pdf_service.dart';
import '../services/printing_service.dart';

class ExportPrintButtons extends StatelessWidget {
  final dynamic data;
  final String type;
  final String title;
  final bool showIndividual;
  final bool showBulk;
  final VoidCallback? onBulkExport;

  const ExportPrintButtons({
    super.key,
    required this.data,
    required this.type,
    required this.title,
    this.showIndividual = true,
    this.showBulk = false,
    this.onBulkExport,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        children: [
          // عنوان القسم
          Row(
            children: [
              Icon(Icons.file_download, color: Colors.blue[600], size: 20),
              const SizedBox(width: 8),
              Text(
                'تصدير وطباعة',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[700],
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // أزرار التصدير والطباعة
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              // تصدير PDF
              if (showIndividual)
                _buildActionButton(
                  icon: Icons.picture_as_pdf,
                  label: 'PDF',
                  color: Colors.red,
                  onPressed: () => _exportToPDF(context),
                ),

              // طباعة عادية
              if (showIndividual)
                _buildActionButton(
                  icon: Icons.print,
                  label: 'طباعة',
                  color: Colors.blue,
                  onPressed: () => _printDocument(context),
                ),



              // مشاركة
              if (showIndividual)
                _buildActionButton(
                  icon: Icons.share,
                  label: 'مشاركة',
                  color: Colors.green,
                  onPressed: () => _shareDocument(context),
                ),

              // واتساب
              if (showIndividual)
                _buildActionButton(
                  icon: Icons.message,
                  label: 'واتساب',
                  color: Colors.teal,
                  onPressed: () => _shareViaWhatsApp(context),
                ),

              // تصدير شامل
              if (showBulk && onBulkExport != null)
                _buildActionButton(
                  icon: Icons.folder_zip,
                  label: 'تصدير شامل',
                  color: Colors.purple,
                  onPressed: onBulkExport!,
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color.withOpacity(0.3)),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: color, size: 16),
            const SizedBox(width: 6),
            Text(
              label,
              style: TextStyle(
                color: color,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _exportToPDF(BuildContext context) async {
    try {
      _showLoadingDialog(context, 'جاري إنشاء ملف PDF...');

      File pdfFile;

      switch (type) {
        case 'workshop':
          pdfFile = await SimplePDFService.generateWorkshopPDF(data);
          break;
        case 'works':
          pdfFile = await SimplePDFService.generateWorksPDF(data is List ? data : [data], title: title);
          break;
        case 'measurements':
          String measurementType = _getMeasurementType();
          pdfFile = await SimplePDFService.generateMeasurementsPDF(data is List ? data : [data], measurementType);
          break;
        case 'invoices':
          pdfFile = await SimplePDFService.generateInvoicesPDF(data is List ? data : [data]);
          break;
        case 'statistics':
          pdfFile = await SimplePDFService.generateStatisticsPDF(data, title: title);
          break;
        case 'tailors':
        case 'workshops':
          // هذه الأنواع غير مدعومة حالياً
          throw Exception('هذا النوع غير متاح حالياً');
        default:
          throw Exception('نوع البيانات غير مدعوم');
      }

      Navigator.of(context).pop(); // إغلاق dialog التحميل

      _showSuccessDialog(context, 'تم إنشاء ملف PDF بنجاح', pdfFile);

    } catch (e) {
      Navigator.of(context).pop(); // إغلاق dialog التحميل
      _showErrorDialog(context, 'فشل في إنشاء ملف PDF: $e');
    }
  }

  Future<void> _printDocument(BuildContext context) async {
    try {
      _showLoadingDialog(context, 'جاري التحضير للطباعة...');

      // إنشاء PDF أولاً
      File pdfFile = await _generatePDFForType();

      Navigator.of(context).pop(); // إغلاق dialog التحميل

      // طباعة PDF
      await PrintingService.printPDF(pdfFile);

    } catch (e) {
      Navigator.of(context).pop(); // إغلاق dialog التحميل
      _showErrorDialog(context, 'فشل في الطباعة: $e');
    }
  }



  Future<void> _shareDocument(BuildContext context) async {
    try {
      _showLoadingDialog(context, 'جاري التحضير للمشاركة...');

      File pdfFile = await _generatePDFForType();

      Navigator.of(context).pop(); // إغلاق dialog التحميل

      await PrintingService.sharePDF(pdfFile, title);

    } catch (e) {
      Navigator.of(context).pop(); // إغلاق dialog التحميل
      _showErrorDialog(context, 'فشل في المشاركة: $e');
    }
  }

  Future<void> _shareViaWhatsApp(BuildContext context) async {
    try {
      _showLoadingDialog(context, 'جاري التحضير للإرسال عبر واتساب...');

      File pdfFile = await _generatePDFForType();

      Navigator.of(context).pop(); // إغلاق dialog التحميل

      await PrintingService.shareViaWhatsApp(pdfFile, title);

    } catch (e) {
      Navigator.of(context).pop(); // إغلاق dialog التحميل
      _showErrorDialog(context, 'فشل في الإرسال عبر واتساب: $e');
    }
  }

  Future<File> _generatePDFForType() async {
    switch (type) {
      case 'workshop':
        return await SimplePDFService.generateWorkshopPDF(data);
      case 'works':
        return await SimplePDFService.generateWorksPDF(data is List ? data : [data], title: title);
      case 'measurements':
        String measurementType = _getMeasurementType();
        return await SimplePDFService.generateMeasurementsPDF(data is List ? data : [data], measurementType);
      case 'invoices':
        return await SimplePDFService.generateInvoicesPDF(data is List ? data : [data]);
      case 'statistics':
        return await SimplePDFService.generateStatisticsPDF(data, title: title);
      default:
        throw Exception('نوع البيانات غير مدعوم');
    }
  }

  String _getMeasurementType() {
    // تحديد نوع المقاس بناءً على البيانات
    if (data is Map && data.containsKey('jacketHeight')) {
      return 'suits';
    } else if (data is Map && data.containsKey('dressHeight')) {
      return 'women';
    } else {
      return 'clothing';
    }
  }



  void _showLoadingDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Row(
          children: [
            const CircularProgressIndicator(),
            const SizedBox(width: 16),
            Expanded(child: Text(message)),
          ],
        ),
      ),
    );
  }

  void _showSuccessDialog(BuildContext context, String message, File file) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green),
            SizedBox(width: 8),
            Text('نجح'),
          ],
        ),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              PrintingService.sharePDF(file, title);
            },
            child: const Text('مشاركة'),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.error, color: Colors.red),
            SizedBox(width: 8),
            Text('خطأ'),
          ],
        ),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }


}
