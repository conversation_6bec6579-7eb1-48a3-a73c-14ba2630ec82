import 'package:flutter/material.dart';
import '../models/db_helper.dart';
import '../measurements/clothing_measurements_screen.dart';
import '../measurements/suits_measurements_screen.dart';
import '../measurements/women_measurements_screen.dart';
import '../measurements/measurement_details_screen.dart';
import '../measurements/measurement_styles.dart';
import '../widgets/export_print_buttons.dart';

class WorkshopMeasurementsScreen extends StatefulWidget {
  final Map<String, dynamic> workshop;

  const WorkshopMeasurementsScreen({super.key, required this.workshop});

  @override
  State<WorkshopMeasurementsScreen> createState() => _WorkshopMeasurementsScreenState();
}

class _WorkshopMeasurementsScreenState extends State<WorkshopMeasurementsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();

  List<Map<String, dynamic>> _clothingMeasurements = [];
  List<Map<String, dynamic>> _suitMeasurements = [];
  List<Map<String, dynamic>> _womenMeasurements = [];

  List<Map<String, dynamic>> _filteredClothingMeasurements = [];
  List<Map<String, dynamic>> _filteredSuitMeasurements = [];
  List<Map<String, dynamic>> _filteredWomenMeasurements = [];

  bool _isLoading = true;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadAllMeasurements();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text;
      _filterMeasurements();
    });
  }

  void _filterMeasurements() {
    if (_searchQuery.isEmpty) {
      _filteredClothingMeasurements = List.from(_clothingMeasurements);
      _filteredSuitMeasurements = List.from(_suitMeasurements);
      _filteredWomenMeasurements = List.from(_womenMeasurements);
    } else {
      _filteredClothingMeasurements = _clothingMeasurements.where((measurement) {
        final customerName = measurement['customerName']?.toString().toLowerCase() ?? '';
        final phoneNumber = measurement['phoneNumber']?.toString().toLowerCase() ?? '';
        final billNumber = measurement['billNumber']?.toString().toLowerCase() ?? '';
        final query = _searchQuery.toLowerCase();

        return customerName.contains(query) ||
               phoneNumber.contains(query) ||
               billNumber.contains(query);
      }).toList();

      _filteredSuitMeasurements = _suitMeasurements.where((measurement) {
        final customerName = measurement['customerName']?.toString().toLowerCase() ?? '';
        final phoneNumber = measurement['phoneNumber']?.toString().toLowerCase() ?? '';
        final billNumber = measurement['billNumber']?.toString().toLowerCase() ?? '';
        final query = _searchQuery.toLowerCase();

        return customerName.contains(query) ||
               phoneNumber.contains(query) ||
               billNumber.contains(query);
      }).toList();

      _filteredWomenMeasurements = _womenMeasurements.where((measurement) {
        final customerName = measurement['customerName']?.toString().toLowerCase() ?? '';
        final phoneNumber = measurement['phoneNumber']?.toString().toLowerCase() ?? '';
        final billNumber = measurement['billNumber']?.toString().toLowerCase() ?? '';
        final query = _searchQuery.toLowerCase();

        return customerName.contains(query) ||
               phoneNumber.contains(query) ||
               billNumber.contains(query);
      }).toList();
    }
  }

  Future<void> _loadAllMeasurements() async {
    setState(() => _isLoading = true);

    try {
      final workshopId = widget.workshop['id'] as int;

      final clothingResults = await DBHelper.getClothingMeasurementsForWorkshop(workshopId);
      final suitResults = await DBHelper.getSuitMeasurementsForWorkshop(workshopId);
      final womenResults = await DBHelper.getWomenMeasurementsForWorkshop(workshopId);

      setState(() {
        _clothingMeasurements = clothingResults;
        _suitMeasurements = suitResults;
        _womenMeasurements = womenResults;
        _filterMeasurements();
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل المقاسات: $e')),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    final workshopName = widget.workshop['name'] as String;

    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: MeasurementStyles.backgroundColor,
        appBar: AppBar(
          title: Text(
            'مقاسات $workshopName',
            style: const TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 20,
              letterSpacing: 0.5,
              color: MeasurementStyles.whiteColor,
            ),
          ),
          backgroundColor: MeasurementStyles.primaryColor,
          centerTitle: true,
          bottom: TabBar(
            controller: _tabController,
            labelColor: MeasurementStyles.whiteColor,
            unselectedLabelColor: MeasurementStyles.whiteColor.withOpacity(0.7),
            indicatorColor: MeasurementStyles.whiteColor,
            tabs: const [
              Tab(text: "ثياب"),
              Tab(text: "بدلات"),
              Tab(text: "نسائي"),
            ],
          ),
        ),
        body: Column(
          children: [
            // شريط البحث
            Container(
              padding: const EdgeInsets.all(16),
              color: Colors.white,
              child: TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'البحث بالاسم أو رقم الهاتف أو رقم الفاتورة...',
                  prefixIcon: const Icon(Icons.search, color: MeasurementStyles.primaryColor),
                  suffixIcon: _searchQuery.isNotEmpty
                      ? IconButton(
                          icon: const Icon(Icons.clear),
                          onPressed: () {
                            _searchController.clear();
                          },
                        )
                      : null,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(color: MeasurementStyles.primaryColor),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(color: MeasurementStyles.primaryColor, width: 2),
                  ),
                ),
              ),
            ),

            // عرض النتائج
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : TabBarView(
                      controller: _tabController,
                      children: [
                        _buildMeasurementsList(_filteredClothingMeasurements, 'clothing'),
                        _buildMeasurementsList(_filteredSuitMeasurements, 'suits'),
                        _buildMeasurementsList(_filteredWomenMeasurements, 'women'),
                      ],
                    ),
            ),
          ],
        ),

        // زر إضافة مقاس حسب التبويب النشط
        floatingActionButton: FloatingActionButton(
          heroTag: "add_measurement",
          onPressed: _addMeasurementForCurrentTab,
          backgroundColor: MeasurementStyles.primaryColor,
          child: const Icon(Icons.add, color: Colors.white),
        ),
      ),
    );
  }

  Widget _buildMeasurementsList(List<Map<String, dynamic>> measurements, String type) {
    if (measurements.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.straighten,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              _searchQuery.isNotEmpty
                  ? 'لا توجد نتائج للبحث "$_searchQuery"'
                  : 'لا توجد مقاسات بعد',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
            if (_searchQuery.isNotEmpty) ...[
              const SizedBox(height: 8),
              TextButton(
                onPressed: () {
                  _searchController.clear();
                },
                child: const Text('مسح البحث'),
              ),
            ],
          ],
        ),
      );
    }

    return Column(
      children: [
        // أزرار التصدير للمقاسات
        Container(
          margin: const EdgeInsets.all(12),
          child: ExportPrintButtons(
            data: measurements,
            type: 'measurements',
            title: 'مقاسات $type - معمل ${widget.workshop['name']}',
            showIndividual: false,
            showBulk: true,
          ),
        ),

        // قائمة المقاسات
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: measurements.length,
            itemBuilder: (context, index) {
              final measurement = measurements[index];
              return _buildMeasurementCard(measurement, type);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildMeasurementCard(Map<String, dynamic> measurement, String type) {
    final typeColor = _getTypeColor(type);
    final price = measurement['price']?.toString() ?? '0';
    final paid = measurement['paid']?.toString() ?? '0';
    final remaining = measurement['remaining']?.toString() ?? '0';

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white,
            typeColor.withOpacity(0.05),
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: typeColor.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
        border: Border.all(
          color: typeColor.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(16),
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (_) => MeasurementDetailsScreen(
                measurement: measurement,
                measurementType: type,
              ),
            ),
          );
        },
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // الرأس: الاسم والنوع والإجراءات
              Row(
                children: [
                  // أيقونة النوع
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: typeColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Icon(
                      _getTypeIcon(type),
                      color: typeColor,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  // اسم العميل
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          measurement['customerName'] ?? 'غير محدد',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.grey[800],
                          ),
                        ),
                        const SizedBox(height: 2),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                          decoration: BoxDecoration(
                            color: typeColor,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            _getTypeLabel(type),
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 11,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  // قائمة الإجراءات
                  PopupMenuButton<String>(
                    icon: Icon(Icons.more_vert, color: Colors.grey[600]),
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                    onSelected: (value) {
                      if (value == 'edit') {
                        _editMeasurement(measurement, type);
                      } else if (value == 'delete') {
                        _deleteMeasurement(measurement, type);
                      }
                    },
                    itemBuilder: (context) => [
                      PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          children: [
                            Icon(Icons.edit, color: Colors.blue[600], size: 20),
                            const SizedBox(width: 8),
                            const Text('تعديل'),
                          ],
                        ),
                      ),
                      PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, color: Colors.red[600], size: 20),
                            const SizedBox(width: 8),
                            const Text('حذف'),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // معلومات الاتصال والفاتورة
              if (measurement['phoneNumber'] != null && measurement['phoneNumber'].toString().isNotEmpty)
                _buildInfoRow(Icons.phone, measurement['phoneNumber'].toString(), Colors.green),

              if (measurement['billNumber'] != null && measurement['billNumber'].toString().isNotEmpty)
                _buildInfoRow(Icons.receipt_long, 'فاتورة: ${measurement['billNumber']}', Colors.orange),

              if (measurement['fabricType'] != null && measurement['fabricType'].toString().isNotEmpty)
                _buildInfoRow(Icons.texture, 'نوع القماش: ${measurement['fabricType']}', Colors.purple),

              const SizedBox(height: 12),

              // معلومات المالية
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey[200]!),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: _buildPriceInfo('الإجمالي', price, Colors.blue),
                    ),
                    Container(
                      width: 1,
                      height: 30,
                      color: Colors.grey[300],
                    ),
                    Expanded(
                      child: _buildPriceInfo('المدفوع', paid, Colors.green),
                    ),
                    Container(
                      width: 1,
                      height: 30,
                      color: Colors.grey[300],
                    ),
                    Expanded(
                      child: _buildPriceInfo('المتبقي', remaining, Colors.red),
                    ),
                  ],
                ),
              ),

              // تاريخ الإضافة
              if (measurement['createdAt'] != null)
                Padding(
                  padding: const EdgeInsets.only(top: 8),
                  child: Row(
                    children: [
                      Icon(Icons.access_time, size: 14, color: Colors.grey[500]),
                      const SizedBox(width: 4),
                      Text(
                        _formatDate(measurement['createdAt']),
                        style: TextStyle(
                          color: Colors.grey[500],
                          fontSize: 12,
                        ),
                      ),
                      if (measurement['dayName'] != null) ...[
                        const SizedBox(width: 12),
                        Icon(Icons.today, size: 14, color: typeColor),
                        const SizedBox(width: 4),
                        Text(
                          measurement['dayName'],
                          style: TextStyle(
                            color: typeColor,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),

                const SizedBox(height: 12),

                // أزرار التصدير للمقاس
                ExportPrintButtons(
                  data: measurement,
                  type: 'measurements',
                  title: 'مقاس ${measurement['customerName']} - $type',
                  showIndividual: true,
                ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getTypeColor(String type) {
    switch (type) {
      case 'clothing':
        return Colors.blue;
      case 'suits':
        return Colors.green;
      case 'women':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  String _getTypeLabel(String type) {
    switch (type) {
      case 'clothing':
        return 'ثياب';
      case 'suits':
        return 'بدلات';
      case 'women':
        return 'نسائي';
      default:
        return 'غير محدد';
    }
  }

  IconData _getTypeIcon(String type) {
    switch (type) {
      case 'clothing':
        return Icons.checkroom;
      case 'suits':
        return Icons.business_center;
      case 'women':
        return Icons.woman;
      default:
        return Icons.straighten;
    }
  }

  Widget _buildInfoRow(IconData icon, String text, Color color) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 6),
      child: Row(
        children: [
          Icon(icon, size: 16, color: color),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                color: Colors.grey[700],
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPriceInfo(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          label,
          style: TextStyle(
            color: Colors.grey[600],
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          '$value ريال',
          style: TextStyle(
            color: color,
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  String _formatDate(String? dateStr) {
    if (dateStr == null) return '';
    try {
      final date = DateTime.parse(dateStr);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return dateStr;
    }
  }



  void _addMeasurementForCurrentTab() async {
    final workshopId = widget.workshop['id'] as int;
    final currentIndex = _tabController.index;

    Widget screen;
    switch (currentIndex) {
      case 0: // تبويب الثياب
        screen = ClothingMeasurementsScreen(workshopId: workshopId);
        break;
      case 1: // تبويب البدلات
        screen = SuitsMeasurementsScreen(workshopId: workshopId);
        break;
      case 2: // تبويب النسائي
        screen = WomenMeasurementsScreen(workshopId: workshopId);
        break;
      default:
        return;
    }

    final result = await Navigator.push(
      context,
      MaterialPageRoute(builder: (_) => screen),
    );

    if (result == true) {
      _loadAllMeasurements();
    }
  }

  void _editMeasurement(Map<String, dynamic> measurement, String type) async {
    final measurementId = measurement['id'] as int;
    final workshopId = widget.workshop['id'] as int;

    Widget screen;
    switch (type) {
      case 'clothing':
        screen = ClothingMeasurementsScreen(
          measurementId: measurementId,
          workshopId: workshopId,
        );
        break;
      case 'suits':
        screen = SuitsMeasurementsScreen(
          measurementId: measurementId,
          workshopId: workshopId,
        );
        break;
      case 'women':
        screen = WomenMeasurementsScreen(
          measurementId: measurementId,
          workshopId: workshopId,
        );
        break;
      default:
        return;
    }

    final result = await Navigator.push(
      context,
      MaterialPageRoute(builder: (_) => screen),
    );

    if (result == true) {
      _loadAllMeasurements();
    }
  }

  void _deleteMeasurement(Map<String, dynamic> measurement, String type) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(Icons.warning, color: Colors.red[600]),
            const SizedBox(width: 8),
            const Text('تأكيد الحذف'),
          ],
        ),
        content: Text(
          'هل أنت متأكد من حذف مقاس "${measurement['customerName'] ?? 'غير محدد'}"?\n\nلن يمكن استرجاع هذه البيانات بعد الحذف.',
          style: TextStyle(color: Colors.grey[700]),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إلغاء',
              style: TextStyle(color: Colors.grey[600]),
            ),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red[600],
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            onPressed: () async {
              Navigator.pop(context);
              await _performDelete(measurement, type);
            },
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  Future<void> _performDelete(Map<String, dynamic> measurement, String type) async {
    try {
      final measurementId = measurement['id'] as int;

      switch (type) {
        case 'clothing':
          await DBHelper.deleteClothingMeasurement(measurementId);
          break;
        case 'suits':
          await DBHelper.deleteSuitMeasurement(measurementId);
          break;
        case 'women':
          await DBHelper.deleteWomenMeasurement(measurementId);
          break;
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Row(
              children: [
                Icon(Icons.check_circle, color: Colors.white),
                SizedBox(width: 8),
                Text('تم حذف المقاس بنجاح'),
              ],
            ),
            backgroundColor: Colors.green[600],
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
        _loadAllMeasurements();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                Text('خطأ في حذف المقاس: $e'),
              ],
            ),
            backgroundColor: Colors.red[600],
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    }
  }
}
