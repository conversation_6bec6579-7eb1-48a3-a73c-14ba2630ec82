// lib/screens/financial/financial_management_screen.dart

import 'package:flutter/material.dart';
import '../../models/db_helper.dart';
import '../../models/financial_models.dart';
import '../../measurements/measurement_styles.dart';
import 'initial_balance_screen.dart';
import 'penalties_screen.dart';
import 'extra_work_screen.dart';
import 'monthly_settlement_screen.dart';

class FinancialManagementScreen extends StatefulWidget {
  final int tailorId;
  final String tailorName;

  const FinancialManagementScreen({
    super.key,
    required this.tailorId,
    required this.tailorName,
  });

  @override
  State<FinancialManagementScreen> createState() => _FinancialManagementScreenState();
}

class _FinancialManagementScreenState extends State<FinancialManagementScreen> {
  bool _isLoading = false;
  TailorFinancialSummary? _financialSummary;

  @override
  void initState() {
    super.initState();
    _loadFinancialSummary();
  }

  Future<void> _loadFinancialSummary() async {
    setState(() => _isLoading = true);
    
    try {
      // جلب الرصيد المرحل
      final initialBalanceData = await DBHelper.getInitialBalanceByTailorId(widget.tailorId);
      InitialBalance? initialBalance;
      if (initialBalanceData != null) {
        initialBalance = InitialBalance.fromMap(initialBalanceData);
      }

      // جلب الغرامات
      final penaltiesData = await DBHelper.getPenaltiesByTailorId(widget.tailorId);
      final penalties = penaltiesData.map((data) => Penalty.fromMap(data)).toList();

      // جلب الأعمال الإضافية
      final extraWorksData = await DBHelper.getExtraWorkByTailorId(widget.tailorId);
      final extraWorks = extraWorksData.map((data) => ExtraWork.fromMap(data)).toList();

      // جلب التصفيات الشهرية
      final settlementsData = await DBHelper.getMonthlySettlementsByTailorId(widget.tailorId);
      final settlements = settlementsData.map((data) => MonthlySettlement.fromMap(data)).toList();

      // حساب الرصيد الحالي
      double currentBalance = initialBalance?.netBalance ?? 0.0;
      currentBalance += extraWorks.fold(0.0, (sum, work) => sum + work.extraWorkAmount);
      currentBalance -= penalties.fold(0.0, (sum, penalty) => sum + penalty.penaltyAmount);
      
      // إضافة آخر تصفية شهرية إن وجدت
      if (settlements.isNotEmpty) {
        currentBalance = settlements.first.finalBalance;
      }

      _financialSummary = TailorFinancialSummary(
        tailorId: widget.tailorId,
        tailorName: widget.tailorName,
        initialBalance: initialBalance,
        penalties: penalties,
        extraWork: extraWorks,
        settlements: settlements,
        currentBalance: currentBalance,
      );
    } catch (e) {
      _showErrorSnackBar('خطأ في تحميل البيانات: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: MeasurementStyles.errorColor,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  Future<void> _navigateToScreen(Widget screen) async {
    final result = await Navigator.push(context, MaterialPageRoute(builder: (context) => screen));
    if (result == true) {
      _loadFinancialSummary();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: MeasurementStyles.backgroundColor,
      appBar: AppBar(
        title: Text(
          'النظام المالي - ${widget.tailorName}',
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 18,
          ),
        ),
        backgroundColor: MeasurementStyles.primaryColor,
        foregroundColor: MeasurementStyles.whiteColor,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _loadFinancialSummary,
            icon: const Icon(Icons.refresh_rounded),
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // ملخص الرصيد الحالي
                  _buildCurrentBalanceCard(),
                  
                  const SizedBox(height: 20),

                  // بطاقات الإدارة المالية
                  _buildManagementCards(),

                  const SizedBox(height: 20),

                  // ملخص سريع
                  _buildQuickSummary(),
                ],
              ),
            ),
    );
  }

  Widget _buildCurrentBalanceCard() {
    final currentBalance = _financialSummary?.currentBalance ?? 0.0;
    final isPositive = currentBalance >= 0;

    return MeasurementStyles.modernCard(
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: isPositive
                ? [
                    MeasurementStyles.successColor.withOpacity(0.1),
                    MeasurementStyles.successColor.withOpacity(0.05),
                  ]
                : [
                    MeasurementStyles.errorColor.withOpacity(0.1),
                    MeasurementStyles.errorColor.withOpacity(0.05),
                  ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Column(
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: isPositive 
                        ? MeasurementStyles.successColor
                        : MeasurementStyles.errorColor,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    isPositive 
                        ? Icons.trending_up_rounded
                        : Icons.trending_down_rounded,
                    color: MeasurementStyles.whiteColor,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'الرصيد الحالي',
                        style: MeasurementStyles.cardSubtitleStyle,
                      ),
                      Text(
                        '${currentBalance.abs().toStringAsFixed(2)} ريال',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.w700,
                          color: isPositive 
                              ? MeasurementStyles.successColor
                              : MeasurementStyles.errorColor,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: isPositive 
                        ? MeasurementStyles.successColor
                        : MeasurementStyles.errorColor,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    isPositive ? 'رصيد موجب' : 'رصيد سالب',
                    style: const TextStyle(
                      color: MeasurementStyles.whiteColor,
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            
            if (!isPositive) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: MeasurementStyles.warningColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: MeasurementStyles.warningColor.withOpacity(0.3),
                  ),
                ),
                child: const Row(
                  children: [
                    Icon(
                      Icons.warning_rounded,
                      color: MeasurementStyles.warningColor,
                      size: 20,
                    ),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'يوجد مبلغ متأخر يحتاج تصفية',
                        style: TextStyle(
                          color: MeasurementStyles.warningColor,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildManagementCards() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildManagementCard(
                title: 'الرصيد المرحل',
                subtitle: 'من الشهر الأول',
                icon: Icons.account_balance_wallet_rounded,
                color: MeasurementStyles.primaryColor,
                onTap: () => _navigateToScreen(
                  InitialBalanceScreen(
                    tailorId: widget.tailorId,
                    tailorName: widget.tailorName,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildManagementCard(
                title: 'الغرامات',
                subtitle: '${_financialSummary?.penalties.length ?? 0} غرامة',
                icon: Icons.warning_rounded,
                color: MeasurementStyles.errorColor,
                onTap: () => _navigateToScreen(
                  PenaltiesScreen(
                    tailorId: widget.tailorId,
                    tailorName: widget.tailorName,
                  ),
                ),
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 12),
        
        Row(
          children: [
            Expanded(
              child: _buildManagementCard(
                title: 'الأعمال الإضافية',
                subtitle: '${_financialSummary?.extraWork.length ?? 0} عمل',
                icon: Icons.work_rounded,
                color: MeasurementStyles.successColor,
                onTap: () => _navigateToScreen(
                  ExtraWorkScreen(
                    tailorId: widget.tailorId,
                    tailorName: widget.tailorName,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildManagementCard(
                title: 'التصفية الشهرية',
                subtitle: 'حساب الشهر',
                icon: Icons.calculate_rounded,
                color: MeasurementStyles.primaryLightColor,
                onTap: () => _navigateToScreen(
                  MonthlySettlementScreen(
                    tailorId: widget.tailorId,
                    tailorName: widget.tailorName,
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildManagementCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(16),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: MeasurementStyles.surfaceColor,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: const Color(0xFFE2E8F0),
          ),
          boxShadow: [
            const BoxShadow(
              color: MeasurementStyles.cardShadowColor,
              blurRadius: 10,
              offset: Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: color,
                size: 24,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: MeasurementStyles.textColor,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: MeasurementStyles.cardSubtitleStyle,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickSummary() {
    if (_financialSummary == null) return const SizedBox.shrink();

    return MeasurementStyles.modernCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          MeasurementStyles.sectionTitle(
            'ملخص سريع',
            icon: Icons.summarize_rounded,
          ),
          
          const SizedBox(height: 16),
          
          _buildSummaryRow(
            'الرصيد المرحل',
            '${_financialSummary!.initialNetBalance.toStringAsFixed(2)} ريال',
            Icons.account_balance_wallet_rounded,
            _financialSummary!.initialNetBalance >= 0 
                ? MeasurementStyles.successColor
                : MeasurementStyles.errorColor,
          ),
          
          const SizedBox(height: 12),
          
          _buildSummaryRow(
            'إجمالي الغرامات',
            '${_financialSummary!.totalPenalties.toStringAsFixed(2)} ريال',
            Icons.warning_rounded,
            MeasurementStyles.errorColor,
          ),
          
          const SizedBox(height: 12),
          
          _buildSummaryRow(
            'إجمالي الأعمال الإضافية',
            '${_financialSummary!.totalExtraWork.toStringAsFixed(2)} ريال',
            Icons.work_rounded,
            MeasurementStyles.successColor,
          ),
          
          if (_financialSummary!.lastSettlement != null) ...[
            const SizedBox(height: 12),
            _buildSummaryRow(
              'آخر تصفية',
              _financialSummary!.lastSettlement!.formattedMonthYear,
              Icons.calculate_rounded,
              MeasurementStyles.primaryColor,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSummaryRow(String title, String value, IconData icon, Color color) {
    return Row(
      children: [
        Icon(icon, color: color, size: 20),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            title,
            style: MeasurementStyles.normalTextStyle,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: color,
          ),
        ),
      ],
    );
  }
}
