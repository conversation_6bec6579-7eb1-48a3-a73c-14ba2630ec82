// plugins {
//     id "com.android.application"
//     id "kotlin-android"
//     // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
//     id "dev.flutter.flutter-gradle-plugin"
// }

// def localProperties = new Properties()
// def localPropertiesFile = rootProject.file("local.properties")
// if (localPropertiesFile.exists()) {
//     localPropertiesFile.withReader("UTF-8") { reader ->
//         localProperties.load(reader)
//     }
// }

// def flutterVersionCode = localProperties.getProperty("flutter.versionCode")
// if (flutterVersionCode == null) {
//     flutterVersionCode = "1"
// }

// def flutterVersionName = localProperties.getProperty("flutter.versionName")
// if (flutterVersionName == null) {
//     flutterVersionName = "1.0"
// }

// android {
//     namespace = "com.example.databasflutter"
//     compileSdk = flutter.compileSdkVersion
//     ndkVersion = flutter.ndkVersion

//     compileOptions {
//         sourceCompatibility = JavaVersion.VERSION_1_8
//         targetCompatibility = JavaVersion.VERSION_1_8
//     }

//     defaultConfig {
//         // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
//         applicationId = "com.example.databasflutter"
//         // You can update the following values to match your application needs.
//         // For more information, see: https://docs.flutter.dev/deployment/android#reviewing-the-gradle-build-configuration.
//         minSdk = flutter.minSdkVersion
//         targetSdk = flutter.targetSdkVersion
//         versionCode = flutterVersionCode.toInteger()
//         versionName = flutterVersionName
//     }

//     buildTypes {
//         release {
//             // TODO: Add your own signing config for the release build.
//             // Signing with the debug keys for now, so `flutter run --release` works.
//             signingConfig = signingConfigs.debug
//         }
//     }
// }

// flutter {
//     source = "../.."
// }


// apply plugin: 'com.google.gms.google-services'





plugins {
    id "com.android.application"
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file("local.properties")
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader("UTF-8") { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty("flutter.versionCode")
if (flutterVersionCode == null) {
    flutterVersionCode = "1"
}

def flutterVersionName = localProperties.getProperty("flutter.versionName")
if (flutterVersionName == null) {
    flutterVersionName = "1.0"
}

android {
    namespace = "com.example.databasflutter"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = flutter.ndkVersion

    // ✅ هنا التعديل المهم لجافا 17
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    defaultConfig {
        applicationId = "com.example.databasflutter"
        minSdk = flutter.minSdkVersion
        targetSdk = flutter.targetSdkVersion
        versionCode = flutterVersionCode.toInteger()
        versionName = flutterVersionName
    }

    buildTypes {
        release {
            signingConfig = signingConfigs.debug
        }
    }
}

flutter {
    source = "../.."
}

// Firebase plugins removed - using SQLite only
