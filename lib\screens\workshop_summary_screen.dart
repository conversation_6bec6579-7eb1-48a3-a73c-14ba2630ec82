


import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:databasflutter/models/db_helper.dart';
import '../widgets/export_print_buttons.dart';

class WorkshopSummaryScreen extends StatefulWidget {
  final int workshopId;
  final String workshopName;

  const WorkshopSummaryScreen({
    super.key,
    required this.workshopId,
    required this.workshopName,
  });

  @override
  State<WorkshopSummaryScreen> createState() => _WorkshopSummaryScreenState();
}

class _WorkshopSummaryScreenState extends State<WorkshopSummaryScreen> {
  bool _isLoading = true;

  // بيانات الورشة
  double pricePerPiece = 0.0;
  double qassasPrice = 0.0;
  String otherTypeName = '';
  double otherTypePrice = 0.0;
  bool isQassas = false;           // ← أضفنا هذا المتغير

  // ملخص الأرقام
  int totalQuantity = 0;
  double totalIncomeFromProduction = 0.0;

  int totalCutQuantity = 0;
  double totalIncomeFromCuts = 0.0;

  int totalOtherCount = 0;
  double totalIncomeFromOthers = 0.0;

  double totalExpense = 0.0;

  double grossIncome = 0.0;
  double netProfitOrDebt = 0.0;
  bool isInDebt = false;

  @override
  void initState() {
    super.initState();
    _calculateSummary();
  }

  Future<void> _calculateSummary() async {
    // 1. جلب بيانات الورشة المحلية
    final workshopMap = await DBHelper.getWorkshopById(widget.workshopId);
    if (workshopMap == null) {
      setState(() { _isLoading = false; });
      return;
    }
    // اجعل map غير nullable
    final map = workshopMap;
    pricePerPiece  = (map['pricePerPiece'] as num? ?? 0).toDouble();
    qassasPrice    = (map['qassasPrice']   as num? ?? 0).toDouble();
    otherTypeName  = map['otherTypeName']  as String? ?? '';
    otherTypePrice = (map['otherTypePrice'] as num? ?? 0).toDouble();
    isQassas       = (map['isQassas']     as int? ?? 0) == 1;  // ← هنا

    final hasOtherType = otherTypeName.isNotEmpty;

    // 2. جلب الأعمال المرتبطة
    final works = await DBHelper.getWorksForWorkshop(widget.workshopId);

    // متغيرات للتجميع
    int qty      = 0;
    int cutQty   = 0;
    int otherCnt = 0;
    double exp   = 0.0;

    for (var w in works) {
      qty    += w['quantity'] as int? ?? 0;
      exp    += double.tryParse(w['expense'].toString()) ?? 0.0;
      if (isQassas) {
        cutQty += w['cutQuantity'] as int? ?? 0;
      }
      if (hasOtherType) {
        otherCnt += w['otherCount'] as int? ?? 0;
      }
    }

    // 3. حساب المجاميع
    totalQuantity             = qty;
    totalIncomeFromProduction = qty * pricePerPiece;

    totalCutQuantity   = cutQty;
    totalIncomeFromCuts = cutQty * qassasPrice;

    totalOtherCount       = otherCnt;
    totalIncomeFromOthers = otherCnt * otherTypePrice;

    totalExpense = exp;

    grossIncome      = totalIncomeFromProduction
                     + totalIncomeFromCuts
                     + totalIncomeFromOthers;
    final net        = grossIncome - totalExpense;
    netProfitOrDebt  = net.abs();
    isInDebt         = net < 0;

    setState(() { _isLoading = false; });
  }

  /// صياغة الرقم بدون الكسور الزائدة
  String formatNum(double v) {
    if (v == v.roundToDouble()) return v.toInt().toString();
    String s = v.toStringAsFixed(2);
    return s.replaceAll(RegExp(r'\.?0+$'), '');
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;

    // شروط العرض
    final showCuts   = isQassas && totalCutQuantity > 0;
    final showOthers = otherTypeName.isNotEmpty && totalOtherCount > 0;

    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: Colors.green.shade50,
        body: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : Column(
                children: [
                  // هيدر
                  Container(
                    height: screenHeight * 0.22,
                    decoration: const BoxDecoration(
                      gradient: LinearGradient(
                        colors: [Colors.teal, Colors.green],
                        begin: Alignment.topRight,
                        end: Alignment.bottomLeft,
                      ),
                      borderRadius: BorderRadius.vertical(bottom: Radius.circular(40)),
                    ),
                    padding: const EdgeInsets.only(top: 60, right: 20, left: 20),
                    child: Row(
                      children: [
                        GestureDetector(
                          onTap: () => Navigator.pop(context),
                          child: Container(
                            padding: const EdgeInsets.all(6),
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.2),
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(Icons.arrow_back, color: Colors.white),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            'إحصائيات معمل: ${widget.workshopName}',
                            style: GoogleFonts.cairo(
                              color: Colors.white,
                              fontSize: 22,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // إحصائيات
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                      child: SingleChildScrollView(
                        child: Column(
                          children: [
                            _buildStatCard(
                              title: 'إجمالي القطع المنتجة',
                              value: totalQuantity.toString(),
                              icon: Icons.confirmation_number,
                              color: Colors.blue.shade100,
                            ),
                            const SizedBox(height: 8),
                            _buildStatCard(
                              title: 'دخل الإنتاج',
                              value: '${formatNum(totalIncomeFromProduction)} ريال',
                              icon: Icons.production_quantity_limits,
                              color: Colors.lightBlue.shade100,
                            ),

                            // بطاقتا القصّ تظهران فقط إذا isQassas && totalCutQuantity>0
                            if (showCuts) ...[
                              const SizedBox(height: 8),
                              _buildStatCard(
                                title: 'عدد الثياب المقصوصة',
                                value: totalCutQuantity.toString(),
                                icon: Icons.content_cut,
                                color: Colors.orange.shade100,
                              ),
                              const SizedBox(height: 8),
                              _buildStatCard(
                                title: 'دخل القصّ',
                                value: '${formatNum(totalIncomeFromCuts)} ريال',
                                icon: Icons.monetization_on,
                                color: Colors.deepOrange.shade100,
                              ),
                            ],

                            // بطاقتا الأنواع الأخرى
                            if (showOthers) ...[
                              const SizedBox(height: 8),
                              _buildStatCard(
                                title: 'عدد $otherTypeName',
                                value: totalOtherCount.toString(),
                                icon: Icons.category,
                                color: Colors.purple.shade100,
                              ),
                              const SizedBox(height: 8),
                              _buildStatCard(
                                title: 'دخل $otherTypeName',
                                value: '${formatNum(totalIncomeFromOthers)} ريال',
                                icon: Icons.monetization_on_outlined,
                                color: Colors.purple.shade200,
                              ),
                            ],

                            const SizedBox(height: 8),
                            _buildStatCard(
                              title: 'إجمالي المصروفات',
                              value: '${formatNum(totalExpense)} ريال',
                              icon: Icons.money_off,
                              color: Colors.red.shade100,
                            ),
                            const SizedBox(height: 8),
                            _buildStatCard(
                              title: isInDebt ? 'مديونية' : 'صافي الربح',
                              value: '${formatNum(netProfitOrDebt)} ريال',
                              icon: isInDebt ? Icons.error_outline : Icons.trending_up,
                              color: isInDebt ? Colors.red.shade200 : Colors.green.shade200,
                            ),

                            const SizedBox(height: 16),

                            // أزرار التصدير والطباعة
                            ExportPrintButtons(
                              data: {
                                'workshopName': widget.workshopName,
                                'totalQuantity': totalQuantity,
                                'totalIncomeFromProduction': totalIncomeFromProduction,
                                'totalCutQuantity': totalCutQuantity,
                                'totalIncomeFromCuts': totalIncomeFromCuts,
                                'totalOtherCount': totalOtherCount,
                                'totalIncomeFromOthers': totalIncomeFromOthers,
                                'otherTypeName': otherTypeName,
                                'totalExpense': totalExpense,
                                'grossIncome': grossIncome,
                                'netProfitOrDebt': netProfitOrDebt,
                                'isInDebt': isInDebt,
                                'showCuts': showCuts,
                                'showOthers': showOthers,
                              },
                              type: 'statistics',
                              title: 'إحصائيات معمل ${widget.workshopName}',
                              showIndividual: true,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),

                  // إعلان
                  Container(
                    height: 60,
                    margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade300,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.teal.shade200),
                    ),
                    child: Center(
                      child: Text(
                        'إعلان بنر هنا',
                        style: GoogleFonts.cairo(fontSize: 14, color: Colors.black54),
                      ),
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.95),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(color: Colors.teal.withOpacity(0.1), blurRadius: 6, offset: const Offset(0, 2)),
        ],
      ),
      child: Row(
        children: [
          CircleAvatar(backgroundColor: Colors.white, child: Icon(icon, color: Colors.teal)),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(title, style: GoogleFonts.cairo(fontSize: 14, fontWeight: FontWeight.bold)),
                Text(value, style: GoogleFonts.cairo(fontSize: 16, color: Colors.black87)),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
