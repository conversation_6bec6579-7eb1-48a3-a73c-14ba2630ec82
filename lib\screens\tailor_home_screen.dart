



// lib/screens/tailor_home_screen.dart

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../models/db_helper.dart';
import '../screens/add_workshop_screen.dart';
import '../screens/workshop_details_screen.dart';
import '../screens/workshop_info_screen.dart';
import '../screens/workshop_summary_screen.dart';
import '../screens/measurement_detail_options_screen.dart';
import '../screens/role_selection_screen.dart';
import '../screens/manager/manager_main_screen.dart';
import '../widgets/app_drawer.dart';

import '../services/pdf_service.dart';
import '../services/printing_service.dart';
import '../widgets/export_print_buttons.dart';

class TailorHomeScreen extends StatefulWidget {
  const TailorHomeScreen({super.key});

  @override
  State<TailorHomeScreen> createState() => _TailorHomeScreenState();
}

class _TailorHomeScreenState extends State<TailorHomeScreen> {
  List<Map<String, dynamic>> _workshops = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    // جلب المعامل من SQLite
    _fetchWorkshops();
  }

  Future<void> _fetchWorkshops() async {
    final data = await DBHelper.getAllWorkshops();
    if (mounted) {
      setState(() {
        _workshops = data;
        _isLoading = false;
      });
    }
  }

  Future<void> _deleteWorkshop(int id) async {
    await DBHelper.deleteWorkshop(id);
    _fetchWorkshops();
  }

  String _formatDate(String? dateStr) {
    if (dateStr == null) return '';
    final date = DateTime.tryParse(dateStr);
    if (date == null) return '';
    return DateFormat('yyyy/MM/dd – HH:mm').format(date);
  }



  // دالة عرض حوار تبديل الدور
  void _showRoleSwitchDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
            title: Row(
              children: [
                Icon(Icons.swap_horiz, color: Colors.purple[600]),
                const SizedBox(width: 12),
                const Text('تبديل الدور'),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  'اختر النظام الذي تريد الانتقال إليه:',
                  style: TextStyle(fontSize: 16),
                ),
                const SizedBox(height: 20),

                // زر نظام المدير
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: () {
                      Navigator.pop(context);
                      Navigator.pushReplacement(
                        context,
                        MaterialPageRoute(
                          builder: (_) => const ManagerMainScreen(),
                        ),
                      );
                    },
                    icon: const Icon(Icons.supervisor_account),
                    label: const Text('نظام مدير الخياطين'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple[600],
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ),

                const SizedBox(height: 12),

                // زر اختيار الدور
                SizedBox(
                  width: double.infinity,
                  child: OutlinedButton.icon(
                    onPressed: () {
                      Navigator.pop(context);
                      Navigator.pushReplacement(
                        context,
                        MaterialPageRoute(
                          builder: (_) => const RoleSelectionScreen(),
                        ),
                      );
                    },
                    icon: const Icon(Icons.home),
                    label: const Text('اختيار الدور'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.teal[700],
                      side: BorderSide(color: Colors.teal[700]!),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
            ],
          );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF9F9F9),
      appBar: AppBar(
        title: const Text(
          '📋 قائمة المعامل',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.teal.shade700,
        elevation: 2,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () {
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(
                builder: (_) => const RoleSelectionScreen(),
              ),
            );
          },
          tooltip: 'العودة لاختيار الدور',
        ),
        actions: [
          // زر التبديل إلى نظام المدير
          IconButton(
            icon: const Icon(Icons.supervisor_account, color: Colors.white),
            onPressed: () {
              _showRoleSwitchDialog();
            },
            tooltip: 'التبديل إلى نظام المدير',
          ),
          IconButton(
            icon: const Icon(Icons.settings, color: Colors.white),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (_) => const MeasurementDetailOptionsScreen(),
                ),
              );
            },
            tooltip: 'إدارة خيارات التفصيل',
          ),
        ],
      ),

      // استخدام الـ AppDrawer المخصص
      drawer: const AppDrawer(),

      body: SafeArea(
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : _workshops.isEmpty
                ? const Center(
                    child: Text(
                      'لا يوجد معامل بعد',
                      style: TextStyle(fontSize: 18, color: Colors.grey),
                    ),
                  )
                : Column(
                    children: [
                      // زر التصدير الشامل
                      if (_workshops.isNotEmpty)
                        Container(
                          margin: const EdgeInsets.all(12),
                          child: ExportPrintButtons(
                            data: _workshops,
                            type: 'workshops',
                            title: 'جميع المعامل',
                            showIndividual: false,
                            showBulk: true,
                          ),
                        ),

                      // قائمة المعامل
                      Expanded(
                        child: ListView.builder(
                    padding: const EdgeInsets.all(12),
                    itemCount: _workshops.length,
                    itemBuilder: (ctx, index) {
                      final workshop = _workshops[index];
                      return Card(
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12)),
                        elevation: 3,
                        margin: const EdgeInsets.symmetric(vertical: 8),
                        child: Padding(
                          padding: const EdgeInsets.all(12),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  CircleAvatar(
                                    backgroundColor: Colors.teal.shade100,
                                    child:
                                        const Icon(Icons.factory, color: Colors.teal),
                                  ),
                                  const SizedBox(width: 12),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'معمل: ${workshop['name']}',
                                          style: const TextStyle(
                                              fontWeight: FontWeight.bold,
                                              fontSize: 16),
                                        ),
                                        const SizedBox(height: 4),
                                        Text(
                                          'نوع الشغل: ${workshop['workType']}',
                                          style: const TextStyle(fontSize: 14),
                                        ),
                                        Text(
                                          'تاريخ الإنشاء: ${_formatDate(workshop['createdAt'])}',
                                          style: TextStyle(
                                              fontSize: 13,
                                              color: Colors.grey[600]),
                                        ),
                                        if (workshop['dayName'] != null)
                                          Text(
                                            'يوم: ${workshop['dayName']}',
                                            style: TextStyle(
                                                fontSize: 13,
                                                color: Colors.teal[600],
                                                fontWeight: FontWeight.w500),
                                          ),
                                      ],
                                    ),
                                  ),
                                  PopupMenuButton<String>(
                                    onSelected: (value) async {
                                      if (value == 'edit') {
                                        await Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                            builder: (_) =>
                                                AddWorkshopScreen(workshopToEdit: workshop),
                                          ),
                                        );
                                        _fetchWorkshops();
                                      } else if (value == 'delete') {
                                        final confirm = await showDialog<bool>(
                                          context: context,
                                          builder: (ctx) => AlertDialog(
                                            title: const Text("تأكيد الحذف"),
                                            content: const Text(
                                                "هل أنت متأكد من حذف هذا المعمل؟"),
                                            actions: [
                                              TextButton(
                                                onPressed: () =>
                                                    Navigator.pop(ctx, false),
                                                child: const Text("إلغاء"),
                                              ),
                                              TextButton(
                                                onPressed: () =>
                                                    Navigator.pop(ctx, true),
                                                child: const Text(
                                                  "حذف",
                                                  style: TextStyle(color: Colors.red),
                                                ),
                                              ),
                                            ],
                                          ),
                                        );
                                        if (confirm == true) {
                                          _deleteWorkshop(workshop['id'] as int);
                                        }
                                      }
                                    },
                                    itemBuilder: (ctx) => const [
                                      PopupMenuItem(
                                          value: 'edit', child: Text('تعديل')),
                                      PopupMenuItem(
                                          value: 'delete', child: Text('حذف')),
                                    ],
                                  ),
                                ],
                              ),
                              const SizedBox(height: 12),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  TextButton.icon(
                                    icon:
                                        const Icon(Icons.bar_chart, color: Colors.teal),
                                    label: const Text(
                                      "الإحصائيات",
                                      style: TextStyle(color: Colors.teal),
                                    ),
                                    onPressed: () {
                                      Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                          builder: (_) => WorkshopSummaryScreen(
                                            workshopId: workshop['id'] as int,
                                            workshopName: workshop['name'] as String,
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                  Expanded(
                                    child: Row(
                                      children: [
                                        Expanded(
                                          child: InkWell(
                                            borderRadius: BorderRadius.circular(10),
                                            onTap: () {
                                              Navigator.push(
                                                context,
                                                MaterialPageRoute(
                                                  builder: (_) => WorkshopInfoScreen(
                                                    workshop: workshop,
                                                  ),
                                                ),
                                              );
                                            },
                                            child: Container(
                                              padding: const EdgeInsets.symmetric(
                                                  vertical: 8, horizontal: 12),
                                              decoration: BoxDecoration(
                                                color: Colors.blue.shade50,
                                                borderRadius: BorderRadius.circular(8),
                                                border: Border.all(
                                                    color: Colors.blue.shade200),
                                              ),
                                              child: const Center(
                                                child: Text(
                                                  'عرض المعلومات',
                                                  style: TextStyle(
                                                      color: Colors.blue,
                                                      fontWeight: FontWeight.bold,
                                                      fontSize: 12),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                        const SizedBox(width: 8),
                                        Expanded(
                                          child: InkWell(
                                            borderRadius: BorderRadius.circular(10),
                                            onTap: () async {
                                              await Navigator.push(
                                                context,
                                                MaterialPageRoute(
                                                  builder: (_) => WorkshopDetailsScreen(
                                                    workshop: workshop,
                                                  ),
                                                ),
                                              );
                                              // حدث بيانات المعامل عند العودة
                                              _fetchWorkshops();
                                            },
                                            child: Container(
                                              padding: const EdgeInsets.symmetric(
                                                  vertical: 8, horizontal: 12),
                                              decoration: BoxDecoration(
                                                color: Colors.teal.shade50,
                                                borderRadius: BorderRadius.circular(8),
                                                border: Border.all(
                                                    color: Colors.teal.shade200),
                                              ),
                                              child: const Center(
                                                child: Text(
                                                  'عرض الأعمال',
                                                  style: TextStyle(
                                                      color: Colors.teal,
                                                      fontWeight: FontWeight.bold,
                                                      fontSize: 12),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),

                              const SizedBox(height: 12),

                              // أزرار التصدير والطباعة
                              ExportPrintButtons(
                                data: workshop,
                                type: 'workshop',
                                title: 'معمل ${workshop['name']}',
                                showIndividual: true,
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                        ),
                      ],
                    ),
      ),

      floatingActionButton: FloatingActionButton.extended(
        onPressed: () async {
          await Navigator.push(
            context,
            MaterialPageRoute(builder: (_) => const AddWorkshopScreen()),
          );
          _fetchWorkshops();
        },
        icon: const Icon(Icons.add),
        label: const Text('إضافة معمل'),
        backgroundColor: Colors.teal,
      ),
    );
  }
}
