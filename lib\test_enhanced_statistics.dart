// ملف اختبار لصفحة الإحصائيات المحسنة
import 'package:flutter/material.dart';
import 'screens/financial/enhanced_statistics_screen.dart';

class TestEnhancedStatistics extends StatelessWidget {
  const TestEnhancedStatistics({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'اختبار الإحصائيات المحسنة',
      theme: ThemeData(
        primarySwatch: Colors.teal,
        fontFamily: 'Cairo',
      ),
      home: const TestStatisticsScreen(),
    );
  }
}

class TestStatisticsScreen extends StatelessWidget {
  const TestStatisticsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار الإحصائيات المحسنة'),
        backgroundColor: Colors.teal[600],
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Icon(
              Icons.analytics,
              size: 80,
              color: Colors.teal,
            ),
            const SizedBox(height: 20),
            const Text(
              'صفحة الإحصائيات المحسنة',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.teal,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            const Text(
              'الميزات الجديدة:',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            _buildFeatureItem('✅ تصميم محسن مع أنيميشن'),
            _buildFeatureItem('✅ كارد الحالة المالية التفاعلي'),
            _buildFeatureItem('✅ إحصائيات الإنتاج مفصلة'),
            _buildFeatureItem('✅ إحصائيات النظام المالي'),
            _buildFeatureItem('✅ الملخص النهائي الشامل'),
            _buildFeatureItem('✅ أزرار التصدير والطباعة'),
            _buildFeatureItem('✅ ربط مع مدير الحالة المالية'),
            _buildFeatureItem('✅ تحديث فوري للبيانات'),
            const SizedBox(height: 30),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const EnhancedStatisticsScreen(
                      workshopId: 1,
                      workshopName: 'معمل تجريبي - الإحصائيات المحسنة',
                    ),
                  ),
                );
              },
              icon: const Icon(Icons.analytics),
              label: const Text('فتح الإحصائيات المحسنة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.teal[600],
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
            const SizedBox(height: 20),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.green[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.green[200]!),
              ),
              child: const Column(
                children: [
                  Icon(Icons.check_circle, color: Colors.green, size: 32),
                  SizedBox(height: 8),
                  Text(
                    'تم نقل الإحصائيات للنظام المالي',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    'جميع الإحصائيات الآن في مكان واحد',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.green,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureItem(String feature) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              feature,
              style: const TextStyle(fontSize: 16),
            ),
          ),
        ],
      ),
    );
  }
}

void main() {
  runApp(const TestEnhancedStatistics());
}
