import 'package:flutter/material.dart';

class TailorSummaryScreen extends StatelessWidget {
  final int tailorId;

  const TailorSummaryScreen({super.key, required this.tailorId});

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('إحصائيات الخياط'),
          backgroundColor: Colors.purple.shade700,
          foregroundColor: Colors.white,
        ),
        body: const Center(
          child: Text(
            'صفحة إحصائيات الخياط\nسيتم إضافتها لاحقاً',
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: 18),
          ),
        ),
      ),
    );
  }
}
