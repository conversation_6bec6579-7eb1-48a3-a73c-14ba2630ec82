// صفحة الرصيد المرحل المحسنة مع شرح مفصل
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../models/db_helper.dart';
import '../../models/financial_models.dart';
import '../../measurements/measurement_styles.dart';
import '../../services/financial_state_manager.dart';
import 'widgets/financial_status_card.dart';

class EnhancedInitialBalanceScreen extends StatefulWidget {
  final int tailorId;
  final String tailorName;

  const EnhancedInitialBalanceScreen({
    super.key,
    required this.tailorId,
    required this.tailorName,
  });

  @override
  State<EnhancedInitialBalanceScreen> createState() => _EnhancedInitialBalanceScreenState();
}

class _EnhancedInitialBalanceScreenState extends State<EnhancedInitialBalanceScreen>
    with TickerProviderStateMixin {

  final _formKey = GlobalKey<FormState>();
  final _debtController = TextEditingController();
  final _extraController = TextEditingController();

  bool _isLoading = false;
  bool _showExplanation = true;
  InitialBalance? _existingBalance;

  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  late Animation<double> _fadeAnimation;

  final FinancialStateManager _financialManager = FinancialStateManager();

  @override
  void initState() {
    super.initState();
    _loadExistingBalance();
    _financialManager.setCurrentWorkshop(widget.tailorId);

    // إعداد الأنيميشن
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _slideAnimation = Tween<double>(
      begin: 0.3,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _debtController.dispose();
    _extraController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadExistingBalance() async {
    setState(() => _isLoading = true);

    try {
      final balanceData = await DBHelper.getInitialBalanceByTailorId(widget.tailorId);
      if (balanceData != null) {
        _existingBalance = InitialBalance.fromMap(balanceData);
        _debtController.text = _existingBalance!.debtFromFirstMonth.toString();
        _extraController.text = _existingBalance!.extraFromFirstMonth.toString();
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في تحميل البيانات: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _saveBalance() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final debt = double.tryParse(_debtController.text) ?? 0.0;
      final extra = double.tryParse(_extraController.text) ?? 0.0;

      final balance = InitialBalance(
        id: _existingBalance?.id,
        tailorId: widget.tailorId,
        debtFromFirstMonth: debt,
        extraFromFirstMonth: extra,
        createdAt: _existingBalance?.createdAt ?? DateTime.now().toIso8601String(),
        updatedAt: DateTime.now().toIso8601String(),
      );

      if (_existingBalance != null) {
        await DBHelper.updateInitialBalance(balance.id!, balance.toMap());
      } else {
        await DBHelper.insertInitialBalance(balance.toMap());
      }

      // تحديث فوري للحالة المالية
      await _financialManager.refresh();

      _showSuccessSnackBar(
        _existingBalance != null ? 'تم تحديث الرصيد المرحل بنجاح' : 'تم حفظ الرصيد المرحل بنجاح'
      );

      // إعادة تحميل البيانات
      await _loadExistingBalance();

    } catch (e) {
      _showErrorSnackBar('خطأ في حفظ البيانات: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white),
            const SizedBox(width: 12),
            Text(message),
          ],
        ),
        backgroundColor: Colors.green[600],
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: MeasurementStyles.errorColor,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: Colors.grey[50],
        body: FadeTransition(
          opacity: _fadeAnimation,
          child: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                  child: Column(
                    children: [
                      // هيدر محسن
                      _buildEnhancedHeader(),

                      // المحتوى الرئيسي
                      SlideTransition(
                        position: Tween<Offset>(
                          begin: Offset(0, _slideAnimation.value),
                          end: Offset.zero,
                        ).animate(_animationController),
                        child: Column(
                          children: [
                            // كارد الحالة المالية
                            FinancialStatusCard(
                              workshopId: widget.tailorId,
                              onTap: () {
                                // يمكن إضافة الانتقال للإحصائيات
                              },
                            ),

                            // شرح مفصل للرصيد المرحل
                            if (_showExplanation) _buildExplanationCard(),

                            // نموذج إدخال الرصيد
                            _buildBalanceForm(),

                            // ملخص الرصيد الحالي
                            if (_existingBalance != null) _buildCurrentBalanceSummary(),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
        ),
      ),
    );
  }

  Widget _buildEnhancedHeader() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.cyan[700]!, Colors.cyan[500]!],
          begin: Alignment.topRight,
          end: Alignment.bottomLeft,
        ),
        borderRadius: const BorderRadius.vertical(bottom: Radius.circular(30)),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              Row(
                children: [
                  GestureDetector(
                    onTap: () => Navigator.pop(context),
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(Icons.arrow_back, color: Colors.white),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Text(
                      'الرصيد المرحل',
                      style: GoogleFonts.cairo(
                        color: Colors.white,
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () {
                      setState(() {
                        _showExplanation = !_showExplanation;
                      });
                    },
                    icon: Icon(
                      _showExplanation ? Icons.visibility_off : Icons.help_outline,
                      color: Colors.white,
                    ),
                    tooltip: _showExplanation ? 'إخفاء الشرح' : 'إظهار الشرح',
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Text(
                'خياط: ${widget.tailorName}',
                style: GoogleFonts.cairo(
                  color: Colors.white.withOpacity(0.9),
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'إدارة الرصيد المرحل من الشهر الأول',
                style: GoogleFonts.cairo(
                  color: Colors.white.withOpacity(0.8),
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildExplanationCard() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.blue[50]!, Colors.blue[100]!],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withOpacity(0.2),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue[600],
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.school,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  'ما هو الرصيد المرحل؟',
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue[800],
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // تعريف الرصيد المرحل
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.7),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              'الرصيد المرحل هو المبلغ المتبقي من الشهر السابق، سواء كان موجباً (لصالحك) أو سالباً (عليك)',
              style: GoogleFonts.cairo(
                fontSize: 16,
                color: Colors.grey[700],
                height: 1.5,
              ),
              textAlign: TextAlign.center,
            ),
          ),

          const SizedBox(height: 20),

          // أمثلة توضيحية
          Text(
            'أمثلة توضيحية:',
            style: GoogleFonts.cairo(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.blue[700],
            ),
          ),

          const SizedBox(height: 12),

          _buildExampleCard(
            'مثال 1: رصيد موجب',
            'في شهر يناير ربحت 5000 ريال وصرفت 3000 ريال\nالرصيد المتبقي: 2000 ريال\nهذا المبلغ يصبح "رصيد مرحل موجب" لشهر فبراير',
            Icons.trending_up,
            Colors.green,
          ),

          const SizedBox(height: 12),

          _buildExampleCard(
            'مثال 2: رصيد سالب',
            'في شهر يناير ربحت 3000 ريال وصرفت 5000 ريال\nالعجز: 2000 ريال\nهذا المبلغ يصبح "رصيد مرحل سالب" لشهر فبراير',
            Icons.trending_down,
            Colors.red,
          ),

          const SizedBox(height: 20),

          // نصيحة مهمة
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.amber[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.amber[200]!),
            ),
            child: Row(
              children: [
                Icon(Icons.lightbulb, color: Colors.amber[700], size: 24),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'نصيحة: قم بتحديث الرصيد المرحل في بداية كل شهر للحصول على حسابات دقيقة',
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color: Colors.amber[800],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExampleCard(String title, String description, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 8),
              Text(
                title,
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            description,
            style: GoogleFonts.cairo(
              fontSize: 13,
              color: Colors.grey[600],
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBalanceForm() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تحديد الرصيد المرحل',
              style: GoogleFonts.cairo(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.grey[800],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'أدخل المبالغ المرحلة من الشهر السابق',
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),

            const SizedBox(height: 24),

            // حقل المبلغ الزائد
            _buildAmountField(
              controller: _extraController,
              label: 'المبلغ الزائد (لصالحك)',
              hint: 'أدخل المبلغ الزائد بالريال',
              icon: Icons.add_circle,
              color: Colors.green,
              helperText: 'المبلغ المتبقي لصالحك من الشهر السابق',
            ),

            const SizedBox(height: 20),

            // حقل المبلغ المتأخر
            _buildAmountField(
              controller: _debtController,
              label: 'المبلغ المتأخر (عليك)',
              hint: 'أدخل المبلغ المتأخر بالريال',
              icon: Icons.remove_circle,
              color: Colors.red,
              helperText: 'المبلغ المستحق عليك من الشهر السابق',
            ),

            const SizedBox(height: 24),

            // حساب الرصيد الصافي
            _buildNetBalanceCalculator(),

            const SizedBox(height: 24),

            // زر الحفظ
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isLoading ? null : _saveBalance,
                icon: _isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white),
                    )
                  : const Icon(Icons.save),
                label: Text(_existingBalance != null ? 'تحديث الرصيد' : 'حفظ الرصيد'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.cyan[600],
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAmountField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    required Color color,
    required String helperText,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, color: color, size: 20),
            const SizedBox(width: 8),
            Text(
              label,
              style: GoogleFonts.cairo(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.grey[700],
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          keyboardType: TextInputType.number,
          decoration: InputDecoration(
            hintText: hint,
            helperText: helperText,
            helperStyle: GoogleFonts.cairo(
              fontSize: 12,
              color: Colors.grey[500],
            ),
            prefixIcon: Icon(Icons.attach_money, color: color),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: color, width: 2),
            ),
            filled: true,
            fillColor: color.withOpacity(0.05),
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return null; // الحقل اختياري
            }
            if (double.tryParse(value) == null) {
              return 'يرجى إدخال رقم صحيح';
            }
            if (double.parse(value) < 0) {
              return 'لا يمكن أن يكون المبلغ سالباً';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildNetBalanceCalculator() {
    final extra = double.tryParse(_extraController.text) ?? 0.0;
    final debt = double.tryParse(_debtController.text) ?? 0.0;
    final netBalance = extra - debt;
    final isPositive = netBalance >= 0;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: isPositive
            ? [Colors.green[50]!, Colors.green[100]!]
            : [Colors.red[50]!, Colors.red[100]!],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isPositive ? Colors.green[200]! : Colors.red[200]!,
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                isPositive ? Icons.trending_up : Icons.trending_down,
                color: isPositive ? Colors.green[700] : Colors.red[700],
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'الرصيد الصافي المرحل',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[700],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            '${isPositive ? '+' : ''}${netBalance.toStringAsFixed(0)} ريال',
            style: GoogleFonts.cairo(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: isPositive ? Colors.green[700] : Colors.red[700],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            isPositive
              ? 'لديك رصيد زائد من الشهر السابق'
              : netBalance == 0
                ? 'لا يوجد رصيد مرحل'
                : 'لديك مبلغ متأخر من الشهر السابق',
            style: GoogleFonts.cairo(
              fontSize: 14,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildCurrentBalanceSummary() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الرصيد المرحل الحالي',
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey[800],
            ),
          ),
          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: _buildSummaryItem(
                  'المبلغ الزائد',
                  _existingBalance!.extraFromFirstMonth,
                  Icons.add_circle,
                  Colors.green,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildSummaryItem(
                  'المبلغ المتأخر',
                  _existingBalance!.debtFromFirstMonth,
                  Icons.remove_circle,
                  Colors.red,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: _existingBalance!.netBalance >= 0
                  ? [Colors.green[50]!, Colors.green[100]!]
                  : [Colors.red[50]!, Colors.red[100]!],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'الرصيد الصافي:',
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[700],
                  ),
                ),
                Text(
                  '${_existingBalance!.netBalance >= 0 ? '+' : ''}${_existingBalance!.netBalance.toStringAsFixed(0)} ريال',
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: _existingBalance!.netBalance >= 0 ? Colors.green[700] : Colors.red[700],
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 12),

          Text(
            'آخر تحديث: ${_formatDate(_existingBalance!.updatedAt ?? _existingBalance!.createdAt)}',
            style: GoogleFonts.cairo(
              fontSize: 12,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryItem(String label, double amount, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 8),
        Text(
          label,
          style: GoogleFonts.cairo(
            fontSize: 14,
            color: Colors.grey[600],
          ),
        ),
        const SizedBox(height: 4),
        Text(
          '${amount.toStringAsFixed(0)} ريال',
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  String _formatDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return 'غير محدد';
    }
  }
}
