import 'package:flutter/material.dart';
import 'measurement_styles.dart';
import '../models/db_helper.dart';
import '../utils/number_utils.dart';
import '../utils/date_utils.dart';

class SuitsMeasurementsScreen extends StatefulWidget {
  final int? measurementId;
  final int? workshopId;

  const SuitsMeasurementsScreen({super.key, this.measurementId, this.workshopId});

  @override
  State<SuitsMeasurementsScreen> createState() => _SuitsMeasurementsScreenState();
}

class _SuitsMeasurementsScreenState extends State<SuitsMeasurementsScreen> {
  bool _isLoading = false;
  bool _isEdit = false;
  final _formKey = GlobalKey<FormState>();
  final billController = TextEditingController();
  final nameController = TextEditingController();
  final phoneController = TextEditingController();
  final fabricController = TextEditingController();
  final quantityController = TextEditingController();
  final dateController = TextEditingController();
  final priceController = TextEditingController();
  final paidController = TextEditingController();
  final remainController = TextEditingController();
  final noteController = TextEditingController();
  final deliveryDateController = TextEditingController();

  final Map<String, TextEditingController> shirt = {
    'الطول': TextEditingController(),
    'الكتف': TextEditingController(),
    'طول الكم': TextEditingController(),
    'وسع الصدر': TextEditingController(),
    'وسع البطن': TextEditingController(),
    'الرقبه': TextEditingController(),
    'تنزيله اليد': TextEditingController(),
    'طول الكبك': TextEditingController(),
    'عرض الكبك': TextEditingController(),
  };

  final Map<String, TextEditingController> pants = {
    'الطول': TextEditingController(),
    'الحزام': TextEditingController(),
    'الورك': TextEditingController(),
    'الفخد': TextEditingController(),
    'الركبه': TextEditingController(),
    'الفتحه': TextEditingController(),
  };

  final Map<String, TextEditingController> coat = {
    'الطول': TextEditingController(),
    'الكتف': TextEditingController(),
    'طول اليد': TextEditingController(),
    'وسع الصدر': TextEditingController(),
    'وسع البطن': TextEditingController(),
    'وسع اليد': TextEditingController(),
    'وسط اليد': TextEditingController(),
    'فتحه اليد': TextEditingController(),
  };

  final Map<String, TextEditingController> vest = {
    'الطول': TextEditingController(),
    'الكتف': TextEditingController(),
    'العرض': TextEditingController(),
  };

  @override
  void initState() {
    super.initState();
    _isEdit = widget.measurementId != null;
    if (_isEdit) {
      _loadMeasurement();
    }
  }

  Future<void> _loadMeasurement() async {
    setState(() => _isLoading = true);
    try {
      final measurement = await DBHelper.getSuitMeasurementById(widget.measurementId!);
      if (measurement != null) {
        // ملء النموذج بالبيانات المسترجعة
        billController.text = measurement['billNumber'] ?? '';
        nameController.text = measurement['customerName'] ?? '';
        phoneController.text = measurement['phoneNumber'] ?? '';
        fabricController.text = measurement['fabricType'] ?? '';
        quantityController.text = NumberUtils.formatForInput(measurement['quantity']);
        dateController.text = measurement['receivedDate'] ?? '';
        priceController.text = NumberUtils.formatForInput(measurement['price']);
        paidController.text = NumberUtils.formatForInput(measurement['paid']);
        remainController.text = NumberUtils.formatForInput(measurement['remaining']);
        deliveryDateController.text = measurement['deliveryDate'] ?? '';
        noteController.text = measurement['notes'] ?? '';

        // ملء مقاسات القميص
        shirt['الطول']!.text = NumberUtils.formatForInput(measurement['shirtHeight']);
        shirt['الكتف']!.text = NumberUtils.formatForInput(measurement['shirtShoulder']);
        shirt['طول الكم']!.text = NumberUtils.formatForInput(measurement['shirtSleeveLength']);
        shirt['وسع الصدر']!.text = NumberUtils.formatForInput(measurement['shirtChest']);
        shirt['وسع البطن']!.text = NumberUtils.formatForInput(measurement['shirtStomach']);
        shirt['الرقبه']!.text = NumberUtils.formatForInput(measurement['shirtNeck']);
        shirt['تنزيله اليد']!.text = NumberUtils.formatForInput(measurement['shirtHandDrop']);
        shirt['طول الكبك']!.text = NumberUtils.formatForInput(measurement['shirtCuffLength']);
        shirt['عرض الكبك']!.text = NumberUtils.formatForInput(measurement['shirtCuffWidth']);

        // ملء مقاسات البنطلون
        pants['الطول']!.text = NumberUtils.formatForInput(measurement['pantsHeight']);
        pants['الحزام']!.text = NumberUtils.formatForInput(measurement['pantsBelt']);
        pants['الورك']!.text = NumberUtils.formatForInput(measurement['pantsHip']);
        pants['الفخد']!.text = NumberUtils.formatForInput(measurement['pantsThigh']);
        pants['الركبه']!.text = NumberUtils.formatForInput(measurement['pantsKnee']);
        pants['الفتحه']!.text = NumberUtils.formatForInput(measurement['pantsOpening']);

        // ملء مقاسات الكوت
        coat['الطول']!.text = NumberUtils.formatForInput(measurement['coatHeight']);
        coat['الكتف']!.text = NumberUtils.formatForInput(measurement['coatShoulder']);
        coat['طول اليد']!.text = NumberUtils.formatForInput(measurement['coatHandLength']);
        coat['وسع الصدر']!.text = NumberUtils.formatForInput(measurement['coatChest']);
        coat['وسع البطن']!.text = NumberUtils.formatForInput(measurement['coatStomach']);
        coat['وسع اليد']!.text = NumberUtils.formatForInput(measurement['coatHandWidth']);
        coat['وسط اليد']!.text = NumberUtils.formatForInput(measurement['coatMiddleHand']);
        coat['فتحه اليد']!.text = NumberUtils.formatForInput(measurement['coatHandOpening']);

        // ملء مقاسات اليلق
        vest['الطول']!.text = NumberUtils.formatForInput(measurement['vestHeight']);
        vest['الكتف']!.text = NumberUtils.formatForInput(measurement['vestShoulder']);
        vest['العرض']!.text = NumberUtils.formatForInput(measurement['vestWidth']);
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('حدث خطأ أثناء تحميل البيانات: ${e.toString()}')),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _saveMeasurement() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);
    try {
      // حساب المتبقي إذا لم يكن مدخلاً
      if (priceController.text.isNotEmpty && paidController.text.isNotEmpty) {
        final price = NumberUtils.parseDouble(priceController.text);
        final paid = NumberUtils.parseDouble(paidController.text);
        remainController.text = NumberUtils.formatForInput(price - paid);
      }

      // جمع البيانات من النموذج
      final data = {
        'workshopId': widget.workshopId, // ربط المقاس بالمعمل
        'billNumber': billController.text,
        'customerName': nameController.text,
        'phoneNumber': phoneController.text,
        'fabricType': fabricController.text,
        'quantity': quantityController.text.isNotEmpty ? NumberUtils.parseInteger(quantityController.text) : null,
        'receivedDate': dateController.text,
        'price': priceController.text.isNotEmpty ? NumberUtils.parseDouble(priceController.text) : null,
        'paid': paidController.text.isNotEmpty ? NumberUtils.parseDouble(paidController.text) : null,
        'remaining': remainController.text.isNotEmpty ? NumberUtils.parseDouble(remainController.text) : null,
        'deliveryDate': deliveryDateController.text,
        'notes': noteController.text,

        // مقاسات القميص
        'shirtHeight': shirt['الطول']!.text,
        'shirtShoulder': shirt['الكتف']!.text,
        'shirtSleeveLength': shirt['طول الكم']!.text,
        'shirtChest': shirt['وسع الصدر']!.text,
        'shirtStomach': shirt['وسع البطن']!.text,
        'shirtNeck': shirt['الرقبه']!.text,
        'shirtHandDrop': shirt['تنزيله اليد']!.text,
        'shirtCuffLength': shirt['طول الكبك']!.text,
        'shirtCuffWidth': shirt['عرض الكبك']!.text,

        // مقاسات البنطلون
        'pantsHeight': pants['الطول']!.text,
        'pantsBelt': pants['الحزام']!.text,
        'pantsHip': pants['الورك']!.text,
        'pantsThigh': pants['الفخد']!.text,
        'pantsKnee': pants['الركبه']!.text,
        'pantsOpening': pants['الفتحه']!.text,

        // مقاسات الكوت
        'coatHeight': coat['الطول']!.text,
        'coatShoulder': coat['الكتف']!.text,
        'coatHandLength': coat['طول اليد']!.text,
        'coatChest': coat['وسع الصدر']!.text,
        'coatStomach': coat['وسع البطن']!.text,
        'coatHandWidth': coat['وسع اليد']!.text,
        'coatMiddleHand': coat['وسط اليد']!.text,
        'coatHandOpening': coat['فتحه اليد']!.text,

        // مقاسات اليلق
        'vestHeight': vest['الطول']!.text,
        'vestShoulder': vest['الكتف']!.text,
        'vestWidth': vest['العرض']!.text,

        // إضافة اسم اليوم
        'dayName': AppDateUtils.getCurrentDayName(),
        'createdAt': DateTime.now().toIso8601String(),
      };

      if (_isEdit) {
        await DBHelper.updateSuitMeasurement(widget.measurementId!, data);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.white),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(AppDateUtils.getMeasurementUpdatedMessage()),
                  ),
                ],
              ),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          );
        }
      } else {
        await DBHelper.insertSuitMeasurement(data);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.white),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(AppDateUtils.getMeasurementAddedMessage()),
                  ),
                ],
              ),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          );
        }
      }

      if (!mounted) return;
      Navigator.pop(context, true); // إرجاع true للإشارة إلى نجاح العملية
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('حدث خطأ أثناء الحفظ: ${e.toString()}')),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  // بناء صف مع ثلاثة حقول إدخال
  Widget buildTripleInput(
    String label1,
    String label2,
    String label3,
    TextEditingController controller1,
    TextEditingController controller2,
    TextEditingController controller3
  ) {
    return Row(
      children: [
        Expanded(child: MeasurementStyles.buildInput(label1, controller1)),
        const SizedBox(width: 10),
        Expanded(child: MeasurementStyles.buildInput(label2, controller2)),
        const SizedBox(width: 10),
        Expanded(child: MeasurementStyles.buildInput(label3, controller3)),
      ],
    );
  }

  // بناء مجموعة من حقول الإدخال من خريطة
  Widget buildMapInputs(Map<String, TextEditingController> group) {
    List<Widget> rows = [];
    List<MapEntry<String, TextEditingController>> entries = group.entries.toList();

    for (int i = 0; i < entries.length; i += 3) {
      if (i + 2 < entries.length) {
        // إضافة صف كامل من ثلاثة حقول
        rows.add(
          buildTripleInput(
            entries[i].key,
            entries[i+1].key,
            entries[i+2].key,
            entries[i].value,
            entries[i+1].value,
            entries[i+2].value
          )
        );
        rows.add(const SizedBox(height: 10));
      } else if (i + 1 < entries.length) {
        // إضافة صف من حقلين
        rows.add(
          Row(
            children: [
              Expanded(child: MeasurementStyles.buildInput(entries[i].key, entries[i].value)),
              const SizedBox(width: 10),
              Expanded(child: MeasurementStyles.buildInput(entries[i+1].key, entries[i+1].value)),
              const Expanded(child: SizedBox()), // فراغ للحفاظ على المحاذاة
            ],
          )
        );
        rows.add(const SizedBox(height: 10));
      } else {
        // إضافة حقل واحد فقط
        rows.add(
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 50),
            child: MeasurementStyles.buildInput(entries[i].key, entries[i].value),
          )
        );
      }
    }

    return Column(children: rows);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: MeasurementStyles.backgroundColor,
      appBar: AppBar(
        title: Text(_isEdit ? "تعديل قياس بدلة" : "قياس بدلة جديد"),
        backgroundColor: MeasurementStyles.primaryColor,
        centerTitle: true,
      ),
      body: Directionality(
        textDirection: TextDirection.rtl,
        child: Form(
          key: _formKey,
          child: Stack(
            children: [
              ListView(
                padding: const EdgeInsets.all(16),
                children: [
                  Card(
                    elevation: 4,
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        children: [
                          // رقم الفاتورة المميز
                          MeasurementStyles.specialInvoiceField(billController),

                          // بيانات العميل
                          MeasurementStyles.sectionTitle("بيانات العميل"),
                          MeasurementStyles.buildInput("اسم العميل", nameController),
                          MeasurementStyles.buildDoubleInput(
                            "رقم الهاتف",
                            "نوع القماش",
                            phoneController,
                            fabricController
                          ),
                          MeasurementStyles.buildDoubleInput(
                            "الكمية",
                            "تاريخ الاستلام",
                            quantityController,
                            dateController
                          ),
                          MeasurementStyles.buildDoubleInput(
                            "المبلغ",
                            "المدفوع",
                            priceController,
                            paidController
                          ),
                          MeasurementStyles.buildInput("المتبقي", remainController),

                          // مقاسات القميص
                          MeasurementStyles.sectionTitle("مقاسات القميص"),
                          buildMapInputs(shirt),

                          // مقاسات البنطلون
                          MeasurementStyles.sectionTitle("مقاسات البنطلون"),
                          buildMapInputs(pants),

                          // مقاسات الكوت
                          MeasurementStyles.sectionTitle("مقاسات الكوت"),
                          buildMapInputs(coat),

                          // مقاسات اليلق
                          MeasurementStyles.sectionTitle("مقاسات اليلق"),
                          buildMapInputs(vest),

                          // الملاحظات
                          MeasurementStyles.sectionTitle("ملاحظات"),
                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: 8),
                            child: TextFormField(
                              controller: noteController,
                              maxLines: 3,
                              textAlign: TextAlign.right,
                              textDirection: TextDirection.rtl,
                              keyboardType: TextInputType.text, // لوحة مفاتيح نصوص للملاحظات
                              decoration: MeasurementStyles.inputDecoration("ملاحظة"),
                            ),
                          ),
                          MeasurementStyles.buildInput("موعد التسليم", deliveryDateController),

                          const SizedBox(height: 20),
                          MeasurementStyles.saveButton(
                            _isEdit ? "تحديث القياس" : "حفظ القياس",
                            _saveMeasurement
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
              if (_isLoading)
                Container(
                  color: Colors.black.withOpacity(0.3),
                  child: const Center(
                    child: CircularProgressIndicator(),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
