// ملف اختبار شامل للنظام المالي المتكامل
import 'package:flutter/material.dart';
import 'screens/financial/financial_main_screen.dart';

class TestCompleteFinancialSystem extends StatelessWidget {
  const TestCompleteFinancialSystem({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'النظام المالي المتكامل - اختبار شامل',
      theme: ThemeData(
        primarySwatch: Colors.indigo,
        fontFamily: 'Cairo',
      ),
      home: const TestFinancialSystemHome(),
    );
  }
}

class TestFinancialSystemHome extends StatelessWidget {
  const TestFinancialSystemHome({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار النظام المالي المتكامل'),
        backgroundColor: Colors.indigo[600],
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'اختبار النظام المالي المتكامل',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            const Text(
              'جميع الميزات المتاحة:',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            _buildFeatureItem('✅ الإحصائيات المالية المحسنة (منقولة للنظام المالي)'),
            _buildFeatureItem('✅ إدارة الرصيد المرحل'),
            _buildFeatureItem('✅ نظام الغرامات مع التحديث الفوري'),
            _buildFeatureItem('✅ نظام الأعمال الإضافية مع التحديث الفوري'),
            _buildFeatureItem('✅ معالج التصفية الشهرية'),
            _buildFeatureItem('✅ كارد الحالة المالية التفاعلي'),
            _buildFeatureItem('✅ نظام التصدير والطباعة'),
            _buildFeatureItem('✅ ربط مع التطبيق الرئيسي'),
            const SizedBox(height: 30),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const FinancialMainScreen(
                      tailorId: 1,
                      tailorName: 'خياط تجريبي - النظام المتكامل',
                    ),
                  ),
                );
              },
              icon: const Icon(Icons.account_balance),
              label: const Text('دخول النظام المالي المتكامل'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.indigo[600],
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
            const SizedBox(height: 20),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.green[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.green[200]!),
              ),
              child: const Column(
                children: [
                  Icon(Icons.check_circle, color: Colors.green, size: 32),
                  SizedBox(height: 8),
                  Text(
                    'النظام المالي مكتمل 100%',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    'جميع الميزات تعمل بالتحديث الفوري',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.green,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureItem(String feature) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              feature,
              style: const TextStyle(fontSize: 16),
            ),
          ),
        ],
      ),
    );
  }
}

void main() {
  runApp(const TestCompleteFinancialSystem());
}
