import 'dart:io';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:path_provider/path_provider.dart';
import 'package:intl/intl.dart';
import '../utils/date_utils.dart';

class PDFService {

  /// إنشاء PDF للمعامل
  static Future<File> generateWorkshopPDF(Map<String, dynamic> workshop) async {
    final pdf = pw.Document();

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // العنوان الرئيسي
              pw.Container(
                width: double.infinity,
                padding: const pw.EdgeInsets.all(20),
                decoration: pw.BoxDecoration(
                  color: PdfColor.fromHex('#E3F2FD'),
                  borderRadius: pw.BorderRadius.circular(10),
                ),
                child: pw.Column(
                  children: [
                    pw.Text(
                      'تفاصيل المعمل',
                      style: pw.TextStyle(
                        fontSize: 24,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                    pw.SizedBox(height: 10),
                    pw.Text(
                      'تاريخ التصدير: ${AppDateUtils.getCurrentDateWithDay()}',
                      style: const pw.TextStyle(fontSize: 12),
                    ),
                  ],
                ),
              ),

              pw.SizedBox(height: 20),

              // معلومات المعمل
              pw.Container(
                padding: const pw.EdgeInsets.all(15),
                decoration: pw.BoxDecoration(
                  border: pw.Border.all(color: PdfColor.fromHex('#E0E0E0')),
                  borderRadius: pw.BorderRadius.circular(8),
                ),
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    _buildInfoRow('اسم المعمل:', workshop['name'] ?? ''),
                    _buildInfoRow('رقم هاتف المالك:', workshop['ownerPhone'] ?? ''),
                    _buildInfoRow('نوع العمل:', workshop['workType'] ?? ''),
                    _buildInfoRow('السعر لكل قطعة:', '${workshop['pricePerPiece'] ?? 0} ريال'),
                    if (workshop['isQassas'] == 1) ...[
                      _buildInfoRow('يدعم القصة:', 'نعم'),
                      _buildInfoRow('سعر القصة:', '${workshop['qassasPrice'] ?? 0} ريال'),
                    ],
                    if (workshop['otherTypeName']?.isNotEmpty == true) ...[
                      _buildInfoRow('نوع عمل آخر:', workshop['otherTypeName']),
                      _buildInfoRow('سعر العمل الآخر:', '${workshop['otherTypePrice'] ?? 0} ريال'),
                    ],
                    if (workshop['dayName'] != null)
                      _buildInfoRow('يوم الإضافة:', workshop['dayName']),
                    _buildInfoRow('تاريخ الإنشاء:', _formatDate(workshop['createdAt'])),
                  ],
                ),
              ),

              pw.Spacer(),

              // تذييل
              pw.Container(
                width: double.infinity,
                padding: const pw.EdgeInsets.all(10),
                decoration: pw.BoxDecoration(
                  color: PdfColor.fromHex('#F5F5F5'),
                  borderRadius: pw.BorderRadius.circular(5),
                ),
                child: pw.Text(
                  'تم إنشاء هذا التقرير بواسطة تطبيق إدارة الخياطين',
                  style: const pw.TextStyle(fontSize: 10),
                  textAlign: pw.TextAlign.center,
                ),
              ),
            ],
          );
        },
      ),
    );

    return _savePDF(pdf, 'معمل_${workshop['name']}_${DateTime.now().millisecondsSinceEpoch}');
  }



  /// إنشاء PDF لجميع المعامل (بسيط)
  static Future<File> generateWorkshopsPDF(List<Map<String, dynamic>> workshops) async {
    final pdf = pw.Document();

    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        build: (pw.Context context) {
          return [
            // العنوان
            pw.Container(
              width: double.infinity,
              padding: const pw.EdgeInsets.all(20),
              decoration: pw.BoxDecoration(
                color: PdfColor.fromHex('#E3F2FD'),
                borderRadius: pw.BorderRadius.circular(10),
              ),
              child: pw.Column(
                children: [
                  pw.Text(
                    'تقرير جميع المعامل',
                    style: pw.TextStyle(
                      fontSize: 24,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                  pw.SizedBox(height: 10),
                  pw.Text(
                    'تاريخ التصدير: ${DateTime.now().toString().split(' ')[0]}',
                    style: const pw.TextStyle(fontSize: 12),
                  ),
                  pw.Text(
                    'عدد المعامل: ${workshops.length}',
                    style: const pw.TextStyle(fontSize: 12),
                  ),
                ],
              ),
            ),

            pw.SizedBox(height: 20),

            // قائمة المعامل
            ...workshops.map((workshop) => pw.Container(
              margin: const pw.EdgeInsets.only(bottom: 15),
              padding: const pw.EdgeInsets.all(15),
              decoration: pw.BoxDecoration(
                border: pw.Border.all(color: PdfColor.fromHex('#E0E0E0')),
                borderRadius: pw.BorderRadius.circular(8),
              ),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    'معمل: ${workshop['name'] ?? ''}',
                    style: pw.TextStyle(
                      fontSize: 16,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                  pw.SizedBox(height: 10),
                  _buildInfoRow('رقم هاتف المالك:', workshop['ownerPhone'] ?? ''),
                  _buildInfoRow('نوع العمل:', workshop['workType'] ?? ''),
                  _buildInfoRow('السعر لكل قطعة:', '${workshop['pricePerPiece'] ?? 0} ريال'),
                  _buildInfoRow('تاريخ الإنشاء:', _formatDate(workshop['createdAt'])),
                ],
              ),
            )),
          ];
        },
      ),
    );

    return _savePDF(pdf, 'جميع_المعامل_${DateTime.now().millisecondsSinceEpoch}');
  }

  /// إنشاء PDF للأعمال
  static Future<File> generateWorksPDF(List<Map<String, dynamic>> works, {String? title}) async {
    final pdf = pw.Document();

    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        build: (pw.Context context) {
          return [
            // العنوان
            pw.Container(
              width: double.infinity,
              padding: const pw.EdgeInsets.all(20),
              decoration: pw.BoxDecoration(
                color: PdfColor.fromHex('#E8F5E8'),
                borderRadius: pw.BorderRadius.circular(10),
              ),
              child: pw.Column(
                children: [
                  pw.Text(
                    title ?? 'تقرير الأعمال',
                    style: pw.TextStyle(
                      fontSize: 24,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                  pw.SizedBox(height: 10),
                  pw.Text(
                    'تاريخ التصدير: ${AppDateUtils.getCurrentDateWithDay()}',
                    style: const pw.TextStyle(fontSize: 12),
                  ),
                  pw.Text(
                    'عدد الأعمال: ${works.length}',
                    style: const pw.TextStyle(fontSize: 12),
                  ),
                ],
              ),
            ),

            pw.SizedBox(height: 20),

            // جدول الأعمال
            pw.Table(
              border: pw.TableBorder.all(color: PdfColor.fromHex('#E0E0E0')),
              columnWidths: {
                0: const pw.FlexColumnWidth(2),
                1: const pw.FlexColumnWidth(1),
                2: const pw.FlexColumnWidth(1),
                3: const pw.FlexColumnWidth(1),
                4: const pw.FlexColumnWidth(1),
                5: const pw.FlexColumnWidth(2),
              },
              children: [
                // رأس الجدول
                pw.TableRow(
                  decoration: pw.BoxDecoration(color: PdfColor.fromHex('#F5F5F5')),
                  children: [
                    _buildTableCell('اسم المعمل', isHeader: true),
                    _buildTableCell('عدد القطع', isHeader: true),
                    _buildTableCell('المصاريف', isHeader: true),
                    _buildTableCell('السعر', isHeader: true),
                    _buildTableCell('الإجمالي', isHeader: true),
                    _buildTableCell('التاريخ', isHeader: true),
                  ],
                ),
                // بيانات الأعمال
                ...works.map((work) => pw.TableRow(
                  children: [
                    _buildTableCell(work['workshopName'] ?? ''),
                    _buildTableCell('${work['pieceCount'] ?? 0}'),
                    _buildTableCell('${work['dailyExpenses'] ?? 0}'),
                    _buildTableCell('${work['pricePerPiece'] ?? 0}'),
                    _buildTableCell('${work['totalPrice'] ?? 0}'),
                    _buildTableCell(_formatDate(work['createdAt'])),
                  ],
                )),
              ],
            ),

            pw.SizedBox(height: 20),

            // الإحصائيات
            _buildSummarySection(works),
          ];
        },
      ),
    );

    return _savePDF(pdf, 'الأعمال_${DateTime.now().millisecondsSinceEpoch}');
  }

  /// إنشاء PDF للمقاسات
  static Future<File> generateMeasurementsPDF(List<Map<String, dynamic>> measurements, String type) async {
    final pdf = pw.Document();

    String typeTitle = '';
    switch (type) {
      case 'clothing':
        typeTitle = 'مقاسات الثياب';
        break;
      case 'suits':
        typeTitle = 'مقاسات البدل';
        break;
      case 'women':
        typeTitle = 'مقاسات النساء';
        break;
    }

    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        build: (pw.Context context) {
          return [
            // العنوان
            pw.Container(
              width: double.infinity,
              padding: const pw.EdgeInsets.all(20),
              decoration: pw.BoxDecoration(
                color: PdfColor.fromHex('#F3E5F5'),
                borderRadius: pw.BorderRadius.circular(10),
              ),
              child: pw.Column(
                children: [
                  pw.Text(
                    typeTitle,
                    style: pw.TextStyle(
                      fontSize: 24,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                  pw.SizedBox(height: 10),
                  pw.Text(
                    'تاريخ التصدير: ${AppDateUtils.getCurrentDateWithDay()}',
                    style: const pw.TextStyle(fontSize: 12),
                  ),
                  pw.Text(
                    'عدد المقاسات: ${measurements.length}',
                    style: const pw.TextStyle(fontSize: 12),
                  ),
                ],
              ),
            ),

            pw.SizedBox(height: 20),

            // قائمة المقاسات
            ...measurements.map((measurement) => pw.Container(
              margin: const pw.EdgeInsets.only(bottom: 15),
              padding: const pw.EdgeInsets.all(15),
              decoration: pw.BoxDecoration(
                border: pw.Border.all(color: PdfColors.grey300),
                borderRadius: pw.BorderRadius.circular(8),
              ),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    'مقاس: ${measurement['customerName'] ?? ''}',
                    style: pw.TextStyle(
                      fontSize: 16,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                  pw.SizedBox(height: 10),
                  _buildInfoRow('رقم الهاتف:', measurement['phoneNumber'] ?? ''),
                  _buildInfoRow('رقم الفاتورة:', measurement['billNumber'] ?? ''),
                  _buildInfoRow('نوع القماش:', measurement['fabricType'] ?? ''),
                  _buildInfoRow('الكمية:', '${measurement['quantity'] ?? 0}'),
                  _buildInfoRow('المبلغ:', '${measurement['price'] ?? 0} ريال'),
                  _buildInfoRow('المدفوع:', '${measurement['paid'] ?? 0} ريال'),
                  _buildInfoRow('المتبقي:', '${measurement['remaining'] ?? 0} ريال'),
                  if (measurement['dayName'] != null)
                    _buildInfoRow('يوم الإضافة:', measurement['dayName']),
                  _buildInfoRow('تاريخ الإنشاء:', _formatDate(measurement['createdAt'])),
                ],
              ),
            )),
          ];
        },
      ),
    );

    return _savePDF(pdf, '${typeTitle}_${DateTime.now().millisecondsSinceEpoch}');
  }

  /// إنشاء PDF للفواتير
  static Future<File> generateInvoicesPDF(List<Map<String, dynamic>> invoices) async {
    final pdf = pw.Document();

    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        build: (pw.Context context) {
          return [
            // العنوان
            pw.Container(
              width: double.infinity,
              padding: const pw.EdgeInsets.all(20),
              decoration: pw.BoxDecoration(
                color: PdfColor.fromHex('#FFF3E0'),
                borderRadius: pw.BorderRadius.circular(10),
              ),
              child: pw.Column(
                children: [
                  pw.Text(
                    'تقرير الفواتير',
                    style: pw.TextStyle(
                      fontSize: 24,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                  pw.SizedBox(height: 10),
                  pw.Text(
                    'تاريخ التصدير: ${AppDateUtils.getCurrentDateWithDay()}',
                    style: const pw.TextStyle(fontSize: 12),
                  ),
                  pw.Text(
                    'عدد الفواتير: ${invoices.length}',
                    style: const pw.TextStyle(fontSize: 12),
                  ),
                ],
              ),
            ),

            pw.SizedBox(height: 20),

            // جدول الفواتير
            pw.Table(
              border: pw.TableBorder.all(color: PdfColor.fromHex('#E0E0E0')),
              columnWidths: {
                0: const pw.FlexColumnWidth(2),
                1: const pw.FlexColumnWidth(2),
                2: const pw.FlexColumnWidth(1),
                3: const pw.FlexColumnWidth(1),
                4: const pw.FlexColumnWidth(1),
                5: const pw.FlexColumnWidth(2),
              },
              children: [
                // رأس الجدول
                pw.TableRow(
                  decoration: pw.BoxDecoration(color: PdfColor.fromHex('#F5F5F5')),
                  children: [
                    _buildTableCell('اسم المحل', isHeader: true),
                    _buildTableCell('اسم العميل', isHeader: true),
                    _buildTableCell('رقم الفاتورة', isHeader: true),
                    _buildTableCell('عدد الثياب', isHeader: true),
                    _buildTableCell('الحالة', isHeader: true),
                    _buildTableCell('التاريخ', isHeader: true),
                  ],
                ),
                // بيانات الفواتير
                ...invoices.map((invoice) => pw.TableRow(
                  children: [
                    _buildTableCell(invoice['shopName'] ?? ''),
                    _buildTableCell(invoice['customerName'] ?? ''),
                    _buildTableCell(invoice['invoiceNumber'] ?? ''),
                    _buildTableCell('${invoice['clothesCount'] ?? 0}'),
                    _buildTableCell(invoice['isReady'] == 1 ? 'جاهزة' : 'غير جاهزة'),
                    _buildTableCell(_formatDate(invoice['createdAt'])),
                  ],
                )),
              ],
            ),
          ];
        },
      ),
    );

    return _savePDF(pdf, 'الفواتير_${DateTime.now().millisecondsSinceEpoch}');
  }

  /// إنشاء PDF للإحصائيات
  static Future<File> generateStatisticsPDF(Map<String, dynamic> stats, {String? title}) async {
    final pdf = pw.Document();

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // العنوان
              pw.Container(
                width: double.infinity,
                padding: const pw.EdgeInsets.all(20),
                decoration: pw.BoxDecoration(
                  color: PdfColor.fromHex('#E0F2F1'),
                  borderRadius: pw.BorderRadius.circular(10),
                ),
                child: pw.Column(
                  children: [
                    pw.Text(
                      title ?? 'تقرير الإحصائيات',
                      style: pw.TextStyle(
                        fontSize: 24,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                    pw.SizedBox(height: 10),
                    pw.Text(
                      'تاريخ التصدير: ${AppDateUtils.getCurrentDateWithDay()}',
                      style: const pw.TextStyle(fontSize: 12),
                    ),
                  ],
                ),
              ),

              pw.SizedBox(height: 30),

              // الإحصائيات
              pw.Container(
                padding: const pw.EdgeInsets.all(20),
                decoration: pw.BoxDecoration(
                  border: pw.Border.all(color: PdfColors.grey300),
                  borderRadius: pw.BorderRadius.circular(8),
                ),
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text(
                      'الإحصائيات العامة',
                      style: pw.TextStyle(
                        fontSize: 18,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                    pw.SizedBox(height: 15),
                    _buildInfoRow('إجمالي الأرباح:', '${stats['totalEarnings'] ?? 0} ريال'),
                    _buildInfoRow('إجمالي المصاريف:', '${stats['totalExpenses'] ?? 0} ريال'),
                    _buildInfoRow('صافي الربح:', '${stats['netProfit'] ?? 0} ريال'),
                    _buildInfoRow('عدد الأعمال:', '${stats['totalWorks'] ?? 0}'),
                    _buildInfoRow('عدد المعامل:', '${stats['totalWorkshops'] ?? 0}'),
                    _buildInfoRow('عدد الفواتير:', '${stats['totalInvoices'] ?? 0}'),
                    _buildInfoRow('عدد المقاسات:', '${stats['totalMeasurements'] ?? 0}'),
                  ],
                ),
              ),

              pw.Spacer(),

              // تذييل
              pw.Container(
                width: double.infinity,
                padding: const pw.EdgeInsets.all(10),
                decoration: pw.BoxDecoration(
                  color: PdfColors.grey100,
                  borderRadius: pw.BorderRadius.circular(5),
                ),
                child: pw.Text(
                  'تم إنشاء هذا التقرير بواسطة تطبيق إدارة الخياطين',
                  style: const pw.TextStyle(fontSize: 10),
                  textAlign: pw.TextAlign.center,
                ),
              ),
            ],
          );
        },
      ),
    );

    return _savePDF(pdf, 'الإحصائيات_${DateTime.now().millisecondsSinceEpoch}');
  }

  /// إنشاء PDF للخياطين
  static Future<File> generateTailorsPDF(List<Map<String, dynamic>> tailors) async {
    final pdf = pw.Document();

    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        build: (pw.Context context) {
          return [
            // العنوان
            pw.Container(
              width: double.infinity,
              padding: const pw.EdgeInsets.all(20),
              decoration: pw.BoxDecoration(
                color: PdfColor.fromHex('#E3F2FD'),
                borderRadius: pw.BorderRadius.circular(10),
              ),
              child: pw.Column(
                children: [
                  pw.Text(
                    'تقرير الخياطين',
                    style: pw.TextStyle(
                      fontSize: 24,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                  pw.SizedBox(height: 10),
                  pw.Text(
                    'تاريخ التصدير: ${DateTime.now().toString().split(' ')[0]}',
                    style: const pw.TextStyle(fontSize: 12),
                  ),
                  pw.Text(
                    'عدد الخياطين: ${tailors.length}',
                    style: const pw.TextStyle(fontSize: 12),
                  ),
                ],
              ),
            ),

            pw.SizedBox(height: 20),

            // قائمة الخياطين
            ...tailors.map((tailor) => pw.Container(
              margin: const pw.EdgeInsets.only(bottom: 15),
              padding: const pw.EdgeInsets.all(15),
              decoration: pw.BoxDecoration(
                border: pw.Border.all(color: PdfColor.fromHex('#E0E0E0')),
                borderRadius: pw.BorderRadius.circular(8),
              ),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    'خياط: ${tailor['name'] ?? ''}',
                    style: pw.TextStyle(
                      fontSize: 16,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                  pw.SizedBox(height: 10),
                  _buildInfoRow('رقم الهاتف:', tailor['phone'] ?? ''),
                  _buildInfoRow('التخصص:', tailor['specialty'] ?? ''),
                  _buildInfoRow('عدد المعامل:', '${tailor['workshopsCount'] ?? 0}'),
                  _buildInfoRow('تاريخ الإضافة:', _formatDate(tailor['createdAt'])),
                ],
              ),
            )),
          ];
        },
      ),
    );

    return _savePDF(pdf, 'تقرير_الخياطين_${DateTime.now().millisecondsSinceEpoch}');
  }

  /// إنشاء PDF لخياط واحد
  static Future<File> generateTailorPDF(Map<String, dynamic> tailor) async {
    final pdf = pw.Document();

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // العنوان
              pw.Container(
                width: double.infinity,
                padding: const pw.EdgeInsets.all(20),
                decoration: pw.BoxDecoration(
                  color: PdfColor.fromHex('#E3F2FD'),
                  borderRadius: pw.BorderRadius.circular(10),
                ),
                child: pw.Column(
                  children: [
                    pw.Text(
                      'بيانات الخياط',
                      style: pw.TextStyle(
                        fontSize: 24,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                    pw.SizedBox(height: 10),
                    pw.Text(
                      'تاريخ التصدير: ${DateTime.now().toString().split(' ')[0]}',
                      style: const pw.TextStyle(fontSize: 12),
                    ),
                  ],
                ),
              ),

              pw.SizedBox(height: 30),

              // بيانات الخياط
              pw.Container(
                padding: const pw.EdgeInsets.all(20),
                decoration: pw.BoxDecoration(
                  border: pw.Border.all(color: PdfColor.fromHex('#E0E0E0')),
                  borderRadius: pw.BorderRadius.circular(8),
                ),
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text(
                      'الاسم: ${tailor['name'] ?? ''}',
                      style: pw.TextStyle(
                        fontSize: 18,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                    pw.SizedBox(height: 15),
                    _buildInfoRow('رقم الهاتف:', tailor['phone'] ?? ''),
                    _buildInfoRow('التخصص:', tailor['specialty'] ?? ''),
                    _buildInfoRow('عدد المعامل:', '${tailor['workshopsCount'] ?? 0}'),
                    _buildInfoRow('تاريخ الإضافة:', _formatDate(tailor['createdAt'])),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );

    return _savePDF(pdf, 'خياط_${tailor['name']}_${DateTime.now().millisecondsSinceEpoch}');
  }

  // دوال مساعدة
  static pw.Widget _buildInfoRow(String label, String value) {
    return pw.Padding(
      padding: const pw.EdgeInsets.symmetric(vertical: 3),
      child: pw.Row(
        children: [
          pw.Expanded(
            flex: 2,
            child: pw.Text(
              label,
              style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
            ),
          ),
          pw.Expanded(
            flex: 3,
            child: pw.Text(value),
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildTableCell(String text, {bool isHeader = false}) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          fontWeight: isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
          fontSize: isHeader ? 12 : 10,
        ),
        textAlign: pw.TextAlign.center,
      ),
    );
  }

  static pw.Widget _buildSummarySection(List<Map<String, dynamic>> works) {
    double totalEarnings = 0;
    double totalExpenses = 0;

    for (var work in works) {
      totalEarnings += (work['totalPrice'] as num?)?.toDouble() ?? 0;
      totalExpenses += (work['dailyExpenses'] as num?)?.toDouble() ?? 0;
    }

    double netProfit = totalEarnings - totalExpenses;

    return pw.Container(
      padding: const pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        color: PdfColor.fromHex('#E3F2FD'),
        border: pw.Border.all(color: PdfColor.fromHex('#BBDEFB')),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'ملخص الإحصائيات',
            style: pw.TextStyle(
              fontSize: 16,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
          pw.SizedBox(height: 10),
          _buildInfoRow('إجمالي الأرباح:', '$totalEarnings ريال'),
          _buildInfoRow('إجمالي المصاريف:', '$totalExpenses ريال'),
          _buildInfoRow('صافي الربح:', '$netProfit ريال'),
          _buildInfoRow('عدد الأعمال:', '${works.length}'),
        ],
      ),
    );
  }

  static String _formatDate(String? dateString) {
    if (dateString == null) return 'غير محدد';
    try {
      final date = DateTime.parse(dateString);
      return DateFormat('dd/MM/yyyy').format(date);
    } catch (e) {
      return 'غير محدد';
    }
  }

  static Future<File> _savePDF(pw.Document pdf, String fileName) async {
    final output = await getTemporaryDirectory();
    final file = File('${output.path}/$fileName.pdf');
    await file.writeAsBytes(await pdf.save());
    return file;
  }
}
