



// lib/screens/add_work_screen.dart

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:databasflutter/models/db_helper.dart';
import '../utils/number_utils.dart';
import '../utils/date_utils.dart';

class AddWorkScreen extends StatefulWidget {
  final int workshopId;
  final bool isQassas;
  final bool hasOtherTypes;
  final Map<String, dynamic>? existingWork;

  const AddWorkScreen({
    required this.workshopId,
    required this.isQassas,
    required this.hasOtherTypes,
    this.existingWork,
    super.key,
  });

  @override
  State<AddWorkScreen> createState() => _AddWorkScreenState();
}

class _AddWorkScreenState extends State<AddWorkScreen> {
  final _quantityController = TextEditingController();
  final _cutQuantityController = TextEditingController();
  final _otherCountController = TextEditingController();
  final _expenseController = TextEditingController();
  final _notesController = TextEditingController();
  final _dateController = TextEditingController();

  String _otherTypeName = '';
  String _workType = '';
  double _pricePerPiece = 0.0;

  bool get isEdit => widget.existingWork != null;

  // دالة التحقق من صحة البيانات
  bool _validateRequiredFields() {
    // الحقول المطلوبة
    final quantity = _quantityController.text.trim();
    final expense = _expenseController.text.trim();
    final cutQuantity = widget.isQassas ? _cutQuantityController.text.trim() : '';
    final otherCount = widget.hasOtherTypes ? _otherCountController.text.trim() : '';

    // التحقق من وجود قيمة في حقل واحد على الأقل
    bool hasQuantity = quantity.isNotEmpty && NumberUtils.parseInteger(quantity) > 0;
    bool hasExpense = expense.isNotEmpty && NumberUtils.parseDouble(expense) > 0;
    bool hasCutQuantity = widget.isQassas && cutQuantity.isNotEmpty && NumberUtils.parseInteger(cutQuantity) > 0;
    bool hasOtherCount = widget.hasOtherTypes && otherCount.isNotEmpty && NumberUtils.parseInteger(otherCount) > 0;

    // إذا لم يتم ملء أي حقل
    if (!hasQuantity && !hasExpense && !hasCutQuantity && !hasOtherCount) {
      _showValidationError();
      return false;
    }

    return true;
  }

  // دالة عرض رسالة الخطأ
  void _showValidationError() {
    List<String> requiredFields = ['عدد القطع', 'المصروف اليومي'];

    if (widget.isQassas) {
      requiredFields.add('عدد القطع المقصوصة');
    }

    if (widget.hasOtherTypes) {
      requiredFields.add('عدد الأنواع الأخرى من الثياب');
    }

    String message = 'يرجى ملء واحد من الحقول التالية:\n\n';
    for (int i = 0; i < requiredFields.length; i++) {
      message += '• ${requiredFields[i]}';
      if (i < requiredFields.length - 1) {
        message += '\n';
      }
    }

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Icon(Icons.warning, color: Colors.orange[600]),
              const SizedBox(width: 12),
              const Text('تنبيه'),
            ],
          ),
          content: Text(
            message,
            style: const TextStyle(fontSize: 16),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('فهمت'),
            ),
          ],
        );
      },
    );
  }

  @override
  void initState() {
    super.initState();
    _loadWorkshopDefaults();
    // جلب اسم النوع الآخر من قاعدة البيانات
    DBHelper.getWorkshopById(widget.workshopId).then((w) {
      setState(() {
        _otherTypeName = w?['otherTypeName'] as String? ?? '';
      });
    });

    // إذا في وضع تعديل، عبئ الحقول
    if (isEdit) {
      final w = widget.existingWork!;
      _quantityController.text = NumberUtils.formatForInput(w['quantity']);
      _cutQuantityController.text = NumberUtils.formatForInput(w['cutQuantity']);
      _otherCountController.text = NumberUtils.formatForInput(w['otherCount']);
      _expenseController.text = NumberUtils.formatForInput(w['expense']);
      _notesController.text = w['notes'] as String? ?? '';
      _dateController.text = w['date'] as String? ?? '';
      _workType = w['workType'] as String? ?? '';
      _pricePerPiece = (w['pricePerPiece'] as num? ?? 0).toDouble();
    } else {
      // القيمة الافتراضية للتاريخ اليوم
      _dateController.text = DateFormat('yyyy-MM-dd').format(DateTime.now());
    }
  }

  Future<void> _loadWorkshopDefaults() async {
    final workshop = await DBHelper.getWorkshopById(widget.workshopId);
    if (!isEdit && workshop != null) {
      setState(() {
        _workType = workshop['workType'] as String? ?? '';
        _pricePerPiece = (workshop['pricePerPiece'] as num? ?? 0).toDouble();
      });
    }
  }

  Future<void> _saveWork() async {
    // التحقق من صحة البيانات - يجب ملء حقل واحد على الأقل
    if (!_validateRequiredFields()) {
      return;
    }

    final quantity = NumberUtils.parseInteger(_quantityController.text);
    final cutQuantity = widget.isQassas
        ? NumberUtils.parseInteger(_cutQuantityController.text)
        : 0;
    final otherCount = widget.hasOtherTypes
        ? NumberUtils.parseInteger(_otherCountController.text)
        : 0;
    final expense = NumberUtils.parseDouble(_expenseController.text);
    final date = _dateController.text.trim();

    final data = <String, dynamic>{
      'workshopId': widget.workshopId,
      'workType': _workType,
      'pricePerPiece': _pricePerPiece,
      'quantity': quantity,
      'cutQuantity': cutQuantity,
      'otherCount': otherCount,
      'expense': expense,
      'notes': _notesController.text.trim(),
      'date': date,
      'dayName': AppDateUtils.getCurrentDayName(), // إضافة اسم اليوم
      'updatedAt': DateTime.now().toIso8601String(),
      'isSynced': 0,
    };

    if (isEdit) {
      final rawId = widget.existingWork!['id'];
      final id = rawId is int ? rawId : int.tryParse(rawId.toString());
      if (id != null) {
        // تعديل العمل بالمعرف والبيانات
        await DBHelper.updateWork(id, data);
        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(AppDateUtils.getWorkUpdatedMessage()),
                ),
              ],
            ),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      } else {
        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('معرف العمل غير صالح')),
        );
        return;
      }
    } else {
      // إضافة createdAt عند الحفظ الأولي
      data['createdAt'] = DateTime.now().toIso8601String();
      await DBHelper.insertWork(data);
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.check_circle, color: Colors.white),
              const SizedBox(width: 8),
              Expanded(
                child: Text(AppDateUtils.getWorkAddedMessage()),
              ),
            ],
          ),
          backgroundColor: Colors.green,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );
    }

    if (mounted) Navigator.pop(context, true);
  }

  @override
  void dispose() {
    _quantityController.dispose();
    _cutQuantityController.dispose();
    _otherCountController.dispose();
    _expenseController.dispose();
    _notesController.dispose();
    _dateController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(isEdit ? 'تعديل عمل' : 'إضافة عمل'),
        backgroundColor: Colors.teal,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            _buildReadOnlyField('نوع الشغل', _workType),
            _buildReadOnlyField(
                'سعر القطعة', NumberUtils.formatIntegerDisplay(_pricePerPiece)),
            _buildInputField(
              'عدد $_workType',
              'مثلاً: 10',
              _quantityController,
              TextInputType.number,
            ),
            if (widget.isQassas)
              _buildInputField(
                'عدد القصّ (اختياري)',
                'كم قطعة قصصت؟',
                _cutQuantityController,
                TextInputType.number,
              ),
            if (widget.hasOtherTypes)
              _buildInputField(
                'عدد $_otherTypeName (اختياري)',
                'كم قطعة من $_otherTypeName؟',
                _otherCountController,
                TextInputType.number,
              ),
            _buildInputField(
              'المصروفات (اختياري)',
              'مثلاً: 500',
              _expenseController,
              TextInputType.number,
            ),
            _buildInputField(
              'ملاحظات (اختياري)',
              'اكتب ملاحظاتك هنا',
              _notesController,
              TextInputType.text,
              maxLines: 3,
            ),
            _buildInputField(
              'التاريخ',
              'اختر التاريخ',
              _dateController,
              TextInputType.datetime,
              isDate: true,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _saveWork,
              icon: const Icon(Icons.save),
              label: Text(isEdit ? 'تحديث' : 'حفظ'),
              style: ElevatedButton.styleFrom(
                minimumSize: const Size(double.infinity, 50),
                backgroundColor: Colors.teal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInputField(
    String label,
    String hint,
    TextEditingController controller,
    TextInputType keyboardType, {
    bool isDate = false,
    int maxLines = 1,
  }) {
    // تحديد ما إذا كان الحقل رقمي
    final isNumeric = keyboardType == TextInputType.number;

    return Card(
      elevation: 4,
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: TextFormField(
        controller: controller,
        readOnly: isDate,
        maxLines: maxLines,
        onTap: isDate
            ? () async {
                final picked = await showDatePicker(
                  context: context,
                  initialDate:
                      DateTime.tryParse(_dateController.text) ?? DateTime.now(),
                  firstDate: DateTime(2020),
                  lastDate: DateTime(2100),
                );
                if (picked != null) {
                  _dateController.text =
                      DateFormat('yyyy-MM-dd').format(picked);
                }
              }
            : null,
        keyboardType: isNumeric ? TextInputType.number : keyboardType,
        inputFormatters: isNumeric ? NumberUtils.integerInputFormatters : null,
        decoration: InputDecoration(
          labelText: label,
          hintText: hint,
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      ),
    );
  }

  Widget _buildReadOnlyField(String label, String value) {
    return Card(
      elevation: 4,
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: ListTile(
        title: Text(label, style: TextStyle(color: Colors.grey[700])),
        subtitle: Text(value,
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
      ),
    );
  }
}