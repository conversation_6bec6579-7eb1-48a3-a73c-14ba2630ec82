import 'dart:io';
import 'package:printing/printing.dart';
import 'package:share_plus/share_plus.dart';

class PrintingService {
  /// طباعة ملف PDF عبر الطابعة العادية
  static Future<void> printPDF(File pdfFile) async {
    try {
      final bytes = await pdfFile.readAsBytes();
      await Printing.layoutPdf(
        onLayout: (format) async => bytes,
      );
    } catch (e) {
      throw Exception('فشل في طباعة الملف: $e');
    }
  }
  
  /// مشاركة ملف PDF
  static Future<void> sharePDF(File pdfFile, String title) async {
    try {
      await Share.shareXFiles(
        [XFile(pdfFile.path)],
        text: title,
        subject: title,
      );
    } catch (e) {
      throw Exception('فشل في مشاركة الملف: $e');
    }
  }
  
  /// إرسال عبر واتساب
  static Future<void> shareViaWhatsApp(File pdfFile, String title) async {
    try {
      await Share.shareXFiles(
        [XFile(pdfFile.path)],
        text: title,
        subject: title,
      );
    } catch (e) {
      throw Exception('فشل في إرسال الملف عبر واتساب: $e');
    }
  }
  
  /// تحويل محتوى إلى نص للطباعة
  static String formatContentForPrinting(Map<String, dynamic> data, String type) {
    StringBuffer buffer = StringBuffer();
    
    switch (type) {
      case 'workshop':
        buffer.writeln('=== تفاصيل المعمل ===');
        buffer.writeln('');
        buffer.writeln('اسم المعمل: ${data['name'] ?? ''}');
        buffer.writeln('رقم هاتف المالك: ${data['ownerPhone'] ?? ''}');
        buffer.writeln('نوع العمل: ${data['workType'] ?? ''}');
        buffer.writeln('السعر لكل قطعة: ${data['pricePerPiece'] ?? 0} ريال');
        if (data['isQassas'] == 1) {
          buffer.writeln('يدعم القصة: نعم');
          buffer.writeln('سعر القصة: ${data['qassasPrice'] ?? 0} ريال');
        }
        if (data['dayName'] != null) {
          buffer.writeln('يوم الإضافة: ${data['dayName']}');
        }
        break;
        
      case 'work':
        buffer.writeln('=== تفاصيل العمل ===');
        buffer.writeln('');
        buffer.writeln('اسم المعمل: ${data['workshopName'] ?? ''}');
        buffer.writeln('عدد القطع: ${data['pieceCount'] ?? 0}');
        buffer.writeln('المصاريف اليومية: ${data['dailyExpenses'] ?? 0} ريال');
        buffer.writeln('السعر لكل قطعة: ${data['pricePerPiece'] ?? 0} ريال');
        buffer.writeln('الإجمالي: ${data['totalPrice'] ?? 0} ريال');
        if (data['dayName'] != null) {
          buffer.writeln('يوم الإضافة: ${data['dayName']}');
        }
        break;
        
      case 'invoice':
        buffer.writeln('=== فاتورة ===');
        buffer.writeln('');
        buffer.writeln('اسم المحل: ${data['shopName'] ?? ''}');
        buffer.writeln('رقم الفاتورة: ${data['invoiceNumber'] ?? ''}');
        buffer.writeln('اسم العميل: ${data['customerName'] ?? ''}');
        buffer.writeln('عدد الثياب: ${data['clothesCount'] ?? 0}');
        buffer.writeln('الحالة: ${data['isReady'] == 1 ? 'جاهزة' : 'غير جاهزة'}');
        if (data['dayName'] != null) {
          buffer.writeln('يوم الإضافة: ${data['dayName']}');
        }
        break;
        
      case 'measurement':
        buffer.writeln('=== مقاس ===');
        buffer.writeln('');
        buffer.writeln('اسم العميل: ${data['customerName'] ?? ''}');
        buffer.writeln('رقم الهاتف: ${data['phoneNumber'] ?? ''}');
        buffer.writeln('رقم الفاتورة: ${data['billNumber'] ?? ''}');
        buffer.writeln('نوع القماش: ${data['fabricType'] ?? ''}');
        buffer.writeln('الكمية: ${data['quantity'] ?? 0}');
        buffer.writeln('المبلغ: ${data['price'] ?? 0} ريال');
        buffer.writeln('المدفوع: ${data['paid'] ?? 0} ريال');
        buffer.writeln('المتبقي: ${data['remaining'] ?? 0} ريال');
        if (data['dayName'] != null) {
          buffer.writeln('يوم الإضافة: ${data['dayName']}');
        }
        break;
    }
    
    buffer.writeln('');
    buffer.writeln('تاريخ الطباعة: ${DateTime.now().toString().split('.')[0]}');
    buffer.writeln('');
    buffer.writeln('تم إنشاء هذا التقرير بواسطة');
    buffer.writeln('تطبيق إدارة الخياطين');
    
    return buffer.toString();
  }
}
