// ملف اختبار لنظام التصدير والطباعة المحسن
import 'package:flutter/material.dart';
import 'widgets/export_print_buttons.dart';

class TestPDFExport extends StatelessWidget {
  const TestPDFExport({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'اختبار نظام التصدير والطباعة',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        fontFamily: 'Cairo',
      ),
      home: const TestPDFExportScreen(),
    );
  }
}

class TestPDFExportScreen extends StatelessWidget {
  const TestPDFExportScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار التصدير والطباعة'),
        backgroundColor: Colors.blue[600],
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Icon(
              Icons.picture_as_pdf,
              size: 80,
              color: Colors.blue,
            ),
            const SizedBox(height: 20),
            const Text(
              'اختبار نظام التصدير والطباعة المحسن',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            const Text(
              'الميزات المحسنة:',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.green[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.green[200]!),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '✅ الإصلاحات المطبقة:',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    '• إصلاح مشكلة "نوع البيانات غير مدعوم"',
                    style: TextStyle(fontSize: 14, color: Colors.green),
                  ),
                  Text(
                    '• إصلاح مشكلة النص يظهر كمربعات سوداء',
                    style: TextStyle(fontSize: 14, color: Colors.green),
                  ),
                  Text(
                    '• استخدام خدمة PDF مبسطة وموثوقة',
                    style: TextStyle(fontSize: 14, color: Colors.green),
                  ),
                  Text(
                    '• معالجة أفضل للأخطاء',
                    style: TextStyle(fontSize: 14, color: Colors.green),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 30),
            
            // اختبار تصدير معمل
            _buildTestCard(
              'اختبار تصدير معمل',
              'تصدير بيانات معمل تجريبي',
              Icons.business,
              Colors.indigo,
              () => _testWorkshopExport(context),
            ),
            
            const SizedBox(height: 16),
            
            // اختبار تصدير أعمال
            _buildTestCard(
              'اختبار تصدير أعمال',
              'تصدير قائمة أعمال تجريبية',
              Icons.work,
              Colors.green,
              () => _testWorksExport(context),
            ),
            
            const SizedBox(height: 16),
            
            // اختبار تصدير إحصائيات
            _buildTestCard(
              'اختبار تصدير إحصائيات',
              'تصدير إحصائيات تجريبية',
              Icons.analytics,
              Colors.orange,
              () => _testStatisticsExport(context),
            ),
            
            const SizedBox(height: 30),
            
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: const Column(
                children: [
                  Icon(Icons.info, color: Colors.blue, size: 32),
                  SizedBox(height: 8),
                  Text(
                    'ملاحظة',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    'النظام الآن يستخدم خدمة PDF مبسطة تحل مشاكل التصدير والطباعة',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.blue,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTestCard(String title, String description, IconData icon, Color color, VoidCallback onTap) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: color.withOpacity(0.3)),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(icon, color: color, size: 24),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              Icon(Icons.arrow_forward_ios, color: color, size: 16),
            ],
          ),
        ),
      ),
    );
  }

  void _testWorkshopExport(BuildContext context) {
    final testWorkshop = {
      'id': 1,
      'name': 'معمل الاختبار',
      'ownerPhone': '0501234567',
      'workType': 'ثياب',
      'pricePerPiece': 50,
      'isQassas': 1,
      'qassasPrice': 25,
      'otherTypeName': 'بدل',
      'otherTypePrice': 75,
      'dayName': 'الأحد',
      'createdAt': DateTime.now().toIso8601String(),
    };

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختبار تصدير معمل'),
        content: ExportPrintButtons(
          data: testWorkshop,
          type: 'workshop',
          title: 'معمل الاختبار',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _testWorksExport(BuildContext context) {
    final testWorks = [
      {
        'id': 1,
        'workshopName': 'معمل الاختبار 1',
        'pieceCount': 10,
        'dailyExpenses': 20,
        'pricePerPiece': 50,
        'totalPrice': 500,
        'createdAt': DateTime.now().toIso8601String(),
      },
      {
        'id': 2,
        'workshopName': 'معمل الاختبار 2',
        'pieceCount': 15,
        'dailyExpenses': 30,
        'pricePerPiece': 60,
        'totalPrice': 900,
        'createdAt': DateTime.now().subtract(const Duration(days: 1)).toIso8601String(),
      },
    ];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختبار تصدير أعمال'),
        content: ExportPrintButtons(
          data: testWorks,
          type: 'works',
          title: 'تقرير الأعمال التجريبي',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _testStatisticsExport(BuildContext context) {
    final testStats = {
      'totalEarnings': 5000,
      'totalExpenses': 1500,
      'netProfit': 3500,
      'totalWorks': 25,
      'totalWorkshops': 5,
      'totalInvoices': 12,
      'totalMeasurements': 8,
    };

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختبار تصدير إحصائيات'),
        content: ExportPrintButtons(
          data: testStats,
          type: 'statistics',
          title: 'إحصائيات تجريبية',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }
}

void main() {
  runApp(const TestPDFExport());
}
