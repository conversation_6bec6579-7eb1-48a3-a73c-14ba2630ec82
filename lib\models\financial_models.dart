// lib/models/financial_models.dart

/// نموذج الرصيد المرحل من الشهر الأول
class InitialBalance {
  final int? id;
  final int tailorId;
  final double debtFromFirstMonth;
  final double extraFromFirstMonth;
  final String createdAt;
  final String? updatedAt;

  InitialBalance({
    this.id,
    required this.tailorId,
    required this.debtFromFirstMonth,
    required this.extraFromFirstMonth,
    required this.createdAt,
    this.updatedAt,
  });

  factory InitialBalance.fromMap(Map<String, dynamic> map) {
    return InitialBalance(
      id: map['id'],
      tailorId: map['tailorId'],
      debtFromFirstMonth: (map['debtFromFirstMonth'] as num).toDouble(),
      extraFromFirstMonth: (map['extraFromFirstMonth'] as num).toDouble(),
      createdAt: map['createdAt'],
      updatedAt: map['updatedAt'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'tailorId': tailorId,
      'debtFromFirstMonth': debtFromFirstMonth,
      'extraFromFirstMonth': extraFromFirstMonth,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
    };
  }

  /// حساب الرصيد الصافي (الزائد - المتأخر)
  double get netBalance => extraFromFirstMonth - debtFromFirstMonth;

  /// هل هناك رصيد متأخر؟
  bool get hasDebt => debtFromFirstMonth > 0;

  /// هل هناك رصيد زائد؟
  bool get hasExtra => extraFromFirstMonth > 0;
}

/// نموذج الغرامة
class Penalty {
  final int? id;
  final int tailorId;
  final String penaltyDescription;
  final double penaltyAmount;
  final String penaltyDate;
  final String createdAt;
  final String? updatedAt;

  Penalty({
    this.id,
    required this.tailorId,
    required this.penaltyDescription,
    required this.penaltyAmount,
    required this.penaltyDate,
    required this.createdAt,
    this.updatedAt,
  });

  factory Penalty.fromMap(Map<String, dynamic> map) {
    return Penalty(
      id: map['id'],
      tailorId: map['tailorId'],
      penaltyDescription: map['penaltyDescription'],
      penaltyAmount: (map['penaltyAmount'] as num).toDouble(),
      penaltyDate: map['penaltyDate'],
      createdAt: map['createdAt'],
      updatedAt: map['updatedAt'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'tailorId': tailorId,
      'penaltyDescription': penaltyDescription,
      'penaltyAmount': penaltyAmount,
      'penaltyDate': penaltyDate,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
    };
  }

  /// تحويل التاريخ إلى DateTime
  DateTime get dateTime => DateTime.parse(penaltyDate);

  /// تنسيق التاريخ للعرض
  String get formattedDate {
    final date = dateTime;
    return '${date.day}/${date.month}/${date.year}';
  }
}

/// نموذج العمل الإضافي
class ExtraWork {
  final int? id;
  final int tailorId;
  final String extraWorkDescription;
  final double extraWorkAmount;
  final String extraWorkDate;
  final String createdAt;
  final String? updatedAt;

  ExtraWork({
    this.id,
    required this.tailorId,
    required this.extraWorkDescription,
    required this.extraWorkAmount,
    required this.extraWorkDate,
    required this.createdAt,
    this.updatedAt,
  });

  factory ExtraWork.fromMap(Map<String, dynamic> map) {
    return ExtraWork(
      id: map['id'],
      tailorId: map['tailorId'],
      extraWorkDescription: map['extraWorkDescription'],
      extraWorkAmount: (map['extraWorkAmount'] as num).toDouble(),
      extraWorkDate: map['extraWorkDate'],
      createdAt: map['createdAt'],
      updatedAt: map['updatedAt'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'tailorId': tailorId,
      'extraWorkDescription': extraWorkDescription,
      'extraWorkAmount': extraWorkAmount,
      'extraWorkDate': extraWorkDate,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
    };
  }

  /// تحويل التاريخ إلى DateTime
  DateTime get dateTime => DateTime.parse(extraWorkDate);

  /// تنسيق التاريخ للعرض
  String get formattedDate {
    final date = dateTime;
    return '${date.day}/${date.month}/${date.year}';
  }
}

/// نموذج التصفية الشهرية
class MonthlySettlement {
  final int? id;
  final int tailorId;
  final int month;
  final int year;
  final double totalIncome;
  final double totalExpenses;
  final double totalPenalties;
  final double totalExtraWork;
  final double previousBalance;
  final double finalBalance;
  final String settlementDate;
  final String? notes;
  final String createdAt;
  final String? updatedAt;

  MonthlySettlement({
    this.id,
    required this.tailorId,
    required this.month,
    required this.year,
    required this.totalIncome,
    required this.totalExpenses,
    required this.totalPenalties,
    required this.totalExtraWork,
    required this.previousBalance,
    required this.finalBalance,
    required this.settlementDate,
    this.notes,
    required this.createdAt,
    this.updatedAt,
  });

  factory MonthlySettlement.fromMap(Map<String, dynamic> map) {
    return MonthlySettlement(
      id: map['id'],
      tailorId: map['tailorId'],
      month: map['month'],
      year: map['year'],
      totalIncome: (map['totalIncome'] as num).toDouble(),
      totalExpenses: (map['totalExpenses'] as num).toDouble(),
      totalPenalties: (map['totalPenalties'] as num).toDouble(),
      totalExtraWork: (map['totalExtraWork'] as num).toDouble(),
      previousBalance: (map['previousBalance'] as num).toDouble(),
      finalBalance: (map['finalBalance'] as num).toDouble(),
      settlementDate: map['settlementDate'],
      notes: map['notes'],
      createdAt: map['createdAt'],
      updatedAt: map['updatedAt'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'tailorId': tailorId,
      'month': month,
      'year': year,
      'totalIncome': totalIncome,
      'totalExpenses': totalExpenses,
      'totalPenalties': totalPenalties,
      'totalExtraWork': totalExtraWork,
      'previousBalance': previousBalance,
      'finalBalance': finalBalance,
      'settlementDate': settlementDate,
      'notes': notes,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
    };
  }

  /// اسم الشهر بالعربية
  String get monthName {
    const months = [
      '', 'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
    return months[month];
  }

  /// تنسيق الشهر والسنة للعرض
  String get formattedMonthYear => '$monthName $year';

  /// حساب الصافي قبل الرصيد السابق
  double get netBeforePrevious => totalIncome - totalExpenses - totalPenalties + totalExtraWork;

  /// هل التصفية بالموجب أم بالسالب؟
  bool get isPositive => finalBalance >= 0;

  /// تحويل تاريخ التصفية إلى DateTime
  DateTime get settlementDateTime => DateTime.parse(settlementDate);

  /// تنسيق تاريخ التصفية للعرض
  String get formattedSettlementDate {
    final date = settlementDateTime;
    return '${date.day}/${date.month}/${date.year}';
  }
}

/// نموذج ملخص مالي شامل لخياط
class TailorFinancialSummary {
  final int tailorId;
  final String tailorName;
  final InitialBalance? initialBalance;
  final List<Penalty> penalties;
  final List<ExtraWork> extraWork;
  final List<MonthlySettlement> settlements;
  final double currentBalance;

  TailorFinancialSummary({
    required this.tailorId,
    required this.tailorName,
    this.initialBalance,
    required this.penalties,
    required this.extraWork,
    required this.settlements,
    required this.currentBalance,
  });

  /// إجمالي الغرامات
  double get totalPenalties => penalties.fold(0.0, (sum, penalty) => sum + penalty.penaltyAmount);

  /// إجمالي الأعمال الإضافية
  double get totalExtraWork => extraWork.fold(0.0, (sum, work) => sum + work.extraWorkAmount);

  /// الرصيد المرحل من الشهر الأول
  double get initialNetBalance => initialBalance?.netBalance ?? 0.0;

  /// هل هناك مبالغ متأخرة؟
  bool get hasOutstandingDebt => currentBalance < 0;

  /// هل هناك مبالغ زائدة؟
  bool get hasExtraCredit => currentBalance > 0;

  /// آخر تصفية شهرية
  MonthlySettlement? get lastSettlement => settlements.isNotEmpty ? settlements.first : null;
}
