// lib/screens/add_workshop_screen.dart

import 'package:flutter/material.dart';
import 'package:databasflutter/models/db_helper.dart';
import '../utils/number_utils.dart';
import '../utils/date_utils.dart';

class AddWorkshopScreen extends StatefulWidget {
  final Map<String, dynamic>? workshopToEdit;
  const AddWorkshopScreen({this.workshopToEdit, super.key});

  @override
  _AddWorkshopScreenState createState() => _AddWorkshopScreenState();
}

class _AddWorkshopScreenState extends State<AddWorkshopScreen> {
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _typeController = TextEditingController();
  final _priceController = TextEditingController();
  final _qassasPriceController = TextEditingController();
  final _otherTypeNameController = TextEditingController();
  final _otherTypePriceController = TextEditingController();

  bool _isQassas = false;
  bool _hasOtherTypes = false;
  bool _isSaving = false; // Add a flag to track saving status

  bool get isEdit => widget.workshopToEdit != null;

  @override
  void initState() {
    super.initState();
    if (isEdit) {
      final w = widget.workshopToEdit!;
      _nameController.text = w['name'] as String? ?? '';
      _phoneController.text = w['ownerPhone'] as String? ?? '';
      _typeController.text = w['workType'] as String? ?? '';
      _priceController.text = NumberUtils.formatForInput(w['pricePerPiece']);
      _isQassas = (w['isQassas'] ?? 0) == 1;
      _qassasPriceController.text = NumberUtils.formatForInput(w['qassasPrice']);
      _hasOtherTypes = (w['otherTypeName'] as String? ?? '').isNotEmpty;
      _otherTypeNameController.text = w['otherTypeName'] as String? ?? '';
      _otherTypePriceController.text = NumberUtils.formatForInput(w['otherTypePrice']);
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _typeController.dispose();
    _priceController.dispose();
    _qassasPriceController.dispose();
    _otherTypeNameController.dispose();
    _otherTypePriceController.dispose();
    super.dispose();
  }

  Future<void> _saveWorkshop() async {
    setState(() {
      _isSaving = true; // Set the saving flag to true
    });
    final name = _nameController.text.trim();
    final phone = _phoneController.text.trim();
    final type = _typeController.text.trim();
    final price = NumberUtils.parseInteger(_priceController.text.trim());
    final qassasPrice = _isQassas
        ? NumberUtils.parseInteger(_qassasPriceController.text.trim())
        : 0;

    if (name.isEmpty || phone.isEmpty || type.isEmpty || price <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('الرجاء تعبئة الحقول الأساسية')),
      );
      setState(() {
        _isSaving = false; // Set the saving flag to false
      });
      return;
    }
    if (_isQassas && qassasPrice < 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('الرجاء إدخال سعر القصّة صحيحًا')),
      );
      setState(() {
        _isSaving = false; // Set the saving flag to false
      });
      return;
    }

    final data = <String, dynamic>{
      'name': name,
      'ownerPhone': phone,
      'workType': type,
      'pricePerPiece': price,
      'isQassas': _isQassas ? 1 : 0,
      'qassasPrice': _isQassas ? qassasPrice : 0,
      'otherTypeName': _hasOtherTypes ? _otherTypeNameController.text.trim() : '',
      'otherTypePrice': _hasOtherTypes
          ? NumberUtils.parseInteger(_otherTypePriceController.text.trim())
          : 0,
      'dayName': AppDateUtils.getCurrentDayName(), // إضافة اسم اليوم
      'createdAt': DateTime.now().toIso8601String(),
    };

    try {
      if (isEdit) {
        final idRaw = widget.workshopToEdit!['id'];
        final id = idRaw is int ? idRaw : int.tryParse(idRaw.toString());
        if (id != null) {
          // تحديث المعمل مع تحديث أسعار جميع الأعمال
          await DBHelper.updateWorkshopWithPriceUpdate(id, data);

          // الحصول على عدد الأعمال التي تم تحديثها
          final worksCount = await DBHelper.getWorksCountForWorkshop(id);

          if (!mounted) return;
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.white),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text('${AppDateUtils.getWorkshopUpdatedMessage()} وتحديث $worksCount عمل بالسعر الجديد'),
                  ),
                ],
              ),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
              duration: const Duration(seconds: 3),
            ),
          );
        } else {
          if (!mounted) return;
          ScaffoldMessenger.of(context)
              .showSnackBar(const SnackBar(content: Text('رقم المعمل غير صحيح')));
          return;
        }
      } else {
        await DBHelper.insertWorkshop(data);
        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(AppDateUtils.getWorkshopAddedMessage()),
                ),
              ],
            ),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }

      if (mounted) {
        Navigator.pop(context);
      }
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context)
          .showSnackBar(SnackBar(content: Text('خطأ: $e')));
    } finally {
      setState(() {
        _isSaving = false; // Set the saving flag to false
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: !_isSaving,
      onPopInvoked: (didPop) {
        if (!didPop && !_isSaving) {
          // العودة إلى الصفحة السابقة
          Navigator.pop(context);
        }
      },
      child: Scaffold(
        appBar: AppBar(
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: _isSaving ? null : () {
              Navigator.pop(context);
            },
          ),
          title: Text(isEdit ? 'تعديل المعمل' : 'إضافة معمل'),
          backgroundColor: Colors.teal,
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              _buildInputCard('اسم المعمل', 'مثلاً: معمل النجوم', Icons.factory, _nameController),
              _buildInputCard('رقم صاحب المعمل', 'مثلاً: 777123456', Icons.phone,
                  _phoneController, TextInputType.phone),
              _buildInputCard('نوع الشغل', 'مثلاً: ثياب', Icons.work, _typeController),
              _buildInputCard('سعر الثوب', 'مثلاً: 1000', Icons.monetization_on,
                  _priceController, TextInputType.number),
              const SizedBox(height: 16),
              SwitchListTile(
                title: const Text('هل تشتغل قصّاص؟'),
                value: _isQassas,
                onChanged: (v) => setState(() => _isQassas = v),
              ),
              if (_isQassas)
                _buildInputCard('سعر القصّة', 'مثلاً: 300', Icons.cut,
                    _qassasPriceController, TextInputType.number),
              const SizedBox(height: 16),
              SwitchListTile(
                title: const Text('أنواع أخرى من الثياب؟'),
                value: _hasOtherTypes,
                onChanged: (v) => setState(() => _hasOtherTypes = v),
              ),
              if (_hasOtherTypes) ...[
                _buildInputCard('اسم النوع الآخر', 'مثلاً: فساتين', Icons.label,
                    _otherTypeNameController),
                _buildInputCard('سعر النوع الآخر', 'مثلاً: 750',
                    Icons.monetization_on, _otherTypePriceController, TextInputType.number),
              ],
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: _saveWorkshop,
                icon: const Icon(Icons.save),
                label: Text(isEdit ? 'تحديث' : 'حفظ'),
                style: ElevatedButton.styleFrom(
                    minimumSize: const Size(double.infinity, 50),
                    backgroundColor: Colors.teal),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInputCard(
    String label,
    String hint,
    IconData icon,
    TextEditingController controller, [
    TextInputType keyboard = TextInputType.text,
  ]) {
    // تحديد ما إذا كان الحقل رقمي
    final isNumeric = keyboard == TextInputType.number;

    return Card(
      elevation: 4,
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: TextFormField(
          controller: controller,
          keyboardType: keyboard,
          inputFormatters: isNumeric ? NumberUtils.integerInputFormatters : null,
          decoration: InputDecoration(
            labelText: label,
            hintText: hint,
            prefixIcon: Icon(icon),
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(10)),
          ),
        ),
      ),
    );
  }
}
