
import 'package:flutter/material.dart';
import '../utils/number_utils.dart';

/// توفير أنماط وألوان مشتركة لجميع شاشات المقاسات
class MeasurementStyles {
  // الألوان الرئيسية - نمط حديث
  static const Color primaryColor = Color(0xFF2563EB); // أزرق حديث
  static const Color primaryLightColor = Color(0xFF3B82F6);
  static const Color secondaryColor = Color(0xFFF1F5F9); // رمادي فاتح
  static const Color backgroundColor = Color(0xFFFAFAFA); // خلفية نظيفة
  static const Color surfaceColor = Colors.white;
  static const Color goldColor = Color(0xFFD4AF37);
  static const Color goldLightColor = Color(0xFFF0E68C);
  static const Color textColor = Color(0xFF1E293B);
  static const Color textSecondaryColor = Color(0xFF64748B);
  static const Color whiteColor = Colors.white;
  static const Color successColor = Color(0xFF10B981);
  static const Color warningColor = Color(0xFFF59E0B);
  static const Color errorColor = Color(0xFFEF4444);
  static const Color cardShadowColor = Color(0x0F000000);

  // أنماط النصوص - تصميم حديث
  static const TextStyle sectionTitleStyle = TextStyle(
    fontSize: 20,
    fontWeight: FontWeight.w600,
    color: primaryColor,
    letterSpacing: 0.5,
  );

  static const TextStyle normalTextStyle = TextStyle(
    fontSize: 16,
    color: textColor,
    fontWeight: FontWeight.w400,
  );

  static const TextStyle buttonTextStyle = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w600,
    color: whiteColor,
    letterSpacing: 0.5,
  );

  static const TextStyle invoiceNumberStyle = TextStyle(
    fontSize: 22,
    fontWeight: FontWeight.w700,
    color: goldColor,
    letterSpacing: 0.5,
  );

  static const TextStyle cardTitleStyle = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.w600,
    color: textColor,
  );

  static const TextStyle cardSubtitleStyle = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w400,
    color: textSecondaryColor,
  );

  // زخرفة الإدخال الحديثة - Material 3
  static InputDecoration inputDecoration(String label, {IconData? icon}) {
    return InputDecoration(
      labelText: label,
      labelStyle: const TextStyle(
        color: textSecondaryColor,
        fontSize: 16,
        fontWeight: FontWeight.w400,
      ),
      floatingLabelStyle: const TextStyle(
        color: primaryColor,
        fontSize: 14,
        fontWeight: FontWeight.w500,
      ),
      prefixIcon: icon != null
        ? Icon(icon, color: textSecondaryColor, size: 20)
        : null,
      filled: true,
      fillColor: surfaceColor,
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(16),
        borderSide: const BorderSide(color: Color(0xFFE2E8F0), width: 1),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(16),
        borderSide: const BorderSide(color: Color(0xFFE2E8F0), width: 1),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(16),
        borderSide: const BorderSide(color: primaryColor, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(16),
        borderSide: const BorderSide(color: errorColor, width: 1),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(16),
        borderSide: const BorderSide(color: errorColor, width: 2),
      ),
    );
  }

  // ويدجت عنوان القسم - تصميم حديث
  static Widget sectionTitle(String title, {IconData? icon}) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 20, horizontal: 4),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: secondaryColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFE2E8F0), width: 1),
      ),
      child: Row(
        children: [
          if (icon != null) ...[
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: primaryColor,
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
          ],
          Expanded(
            child: Text(
              title,
              style: sectionTitleStyle,
              textAlign: TextAlign.start,
            ),
          ),
        ],
      ),
    );
  }

  // ويدجت رقم الفاتورة المميز - تصميم حديث
  static Widget specialInvoiceField(TextEditingController controller) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          colors: [
            primaryColor.withOpacity(0.05),
            primaryLightColor.withOpacity(0.1),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        border: Border.all(
          color: primaryColor.withOpacity(0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: primaryColor.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 20),
            decoration: const BoxDecoration(
              color: primaryColor,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: const Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.receipt_long, color: whiteColor, size: 20),
                SizedBox(width: 8),
                Text(
                  "رقم الفاتورة",
                  style: TextStyle(
                    color: whiteColor,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    letterSpacing: 0.5,
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(20),
            child: TextFormField(
              controller: controller,
              textAlign: TextAlign.center,
              textDirection: TextDirection.rtl,
              keyboardType: TextInputType.number, // لوحة مفاتيح أرقام لرقم الفاتورة
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: primaryColor,
                letterSpacing: 1,
              ),
              decoration: const InputDecoration(
                hintText: "أدخل رقم الفاتورة",
                hintStyle: TextStyle(
                  color: textSecondaryColor,
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                ),
                border: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(vertical: 8),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'الرجاء إدخال رقم الفاتورة';
                }
                return null;
              },
            ),
          ),
        ],
      ),
    );
  }

  // ويدجت حقل إدخال محسن مع أيقونة وتحديد نوع لوحة المفاتيح تلقائياً
  static Widget buildInput(
    String label,
    TextEditingController controller,
    {IconData? icon, TextInputType? keyboardType}
  ) {
    // تحديد نوع لوحة المفاتيح تلقائياً بناءً على نوع الحقل
    TextInputType inputType = keyboardType ?? _getKeyboardType(label);

    // تحديد ما إذا كان الحقل رقمي
    final isNumeric = inputType == TextInputType.number;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: TextFormField(
        controller: controller,
        textAlign: TextAlign.center,
        textDirection: TextDirection.rtl,
        keyboardType: inputType,
        inputFormatters: isNumeric ? NumberUtils.integerInputFormatters : null,
        style: normalTextStyle,
        decoration: inputDecoration(label, icon: icon),
      ),
    );
  }

  // دالة لتحديد نوع لوحة المفاتيح بناءً على تسمية الحقل
  static TextInputType _getKeyboardType(String label) {
    // قائمة الحقول التي تحتاج لوحة مفاتيح أرقام
    final numberFields = [
      // المقاسات والأبعاد
      'الطول', 'العرض', 'الوسع', 'الكتف', 'الصدر', 'الخصر', 'الورك',
      'طول الكم', 'طول الموديل', 'طول الامام', 'طول الظهر', 'الطول الكامل',
      'محيط الصدر', 'محيط الرقبة', 'محيط الذراع', 'محيط المعصم',
      'عرض الظهر', 'عرض الصدر', 'عرض مقعد الكم', 'عرض المعصم',
      'ياقه الظهر', 'نزول الصدر', 'نزول الورك', 'نزول الظهر',
      'ارتفاع الكبك', 'طول الكبك', 'تنزيله اليد', 'وسط اليد', 'الخطوة',
      'الساق فتحه الرجل', 'الرقبه', 'وسع الصدر',

      // المبالغ والأرقام المالية
      'المبلغ', 'المدفوع', 'المتبقي', 'السعر', 'التكلفة',

      // الكميات والأعداد
      'الكمية', 'العدد', 'العمر', 'الوزن',

      // أرقام الهواتف
      'رقم الهاتف', 'الهاتف', 'الجوال', 'التليفون',

      // أرقام الفواتير والمعرفات
      'رقم الفاتورة', 'الفاتورة', 'المعرف', 'الرقم التسلسلي',
    ];

    // التحقق من وجود أي من الكلمات المفتاحية في تسمية الحقل
    String lowerLabel = label.toLowerCase();
    for (String numberField in numberFields) {
      if (lowerLabel.contains(numberField.toLowerCase())) {
        return TextInputType.number;
      }
    }

    // إذا لم يتطابق مع أي حقل رقمي، استخدم لوحة مفاتيح النصوص
    return TextInputType.text;
  }

  // ويدجت للمدخلات المزدوجة في صف واحد
  static Widget buildDoubleInput(
    String label1,
    String label2,
    TextEditingController controller1,
    TextEditingController controller2,
    {IconData? icon1, IconData? icon2, TextInputType? keyboardType1, TextInputType? keyboardType2}
  ) {
    return Row(
      children: [
        Expanded(
          child: buildInput(label1, controller1, icon: icon1, keyboardType: keyboardType1),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: buildInput(label2, controller2, icon: icon2, keyboardType: keyboardType2),
        ),
      ],
    );
  }

  // ويدجت زر الحفظ - تصميم حديث
  static Widget saveButton(String text, VoidCallback onPressed, {bool isLoading = false}) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(vertical: 16),
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryColor,
          foregroundColor: whiteColor,
          padding: const EdgeInsets.symmetric(vertical: 18),
          textStyle: buttonTextStyle,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          elevation: 4,
          shadowColor: primaryColor.withOpacity(0.3),
        ),
        child: isLoading
          ? const SizedBox(
              height: 20,
              width: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(whiteColor),
              ),
            )
          : Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.save_rounded, size: 20),
                const SizedBox(width: 8),
                Text(text),
              ],
            ),
      ),
    );
  }

  // ويدجت كارد حديث
  static Widget modernCard({
    required Widget child,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
  }) {
    return Container(
      margin: margin ?? const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
      decoration: BoxDecoration(
        color: surfaceColor,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          const BoxShadow(
            color: cardShadowColor,
            blurRadius: 10,
            offset: Offset(0, 4),
          ),
        ],
        border: Border.all(
          color: const Color(0xFFE2E8F0),
          width: 1,
        ),
      ),
      child: Padding(
        padding: padding ?? const EdgeInsets.all(20),
        child: child,
      ),
    );
  }

  // ويدجت قائمة منسدلة محسنة
  static Widget buildDropdown<T>({
    required String label,
    required T? value,
    required List<T> items,
    required Function(T?) onChanged,
    IconData? icon,
  }) {
    // التحقق من أن القيمة موجودة في القائمة
    T? validValue = value;
    if (value != null && !items.contains(value)) {
      validValue = null; // إعادة تعيين إلى null إذا لم تكن موجودة
    }

    // إزالة القيم المكررة
    final uniqueItems = items.toSet().toList();

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: DropdownButtonFormField<T>(
        value: validValue,
        decoration: inputDecoration(label, icon: icon),
        style: normalTextStyle,
        icon: const Icon(Icons.keyboard_arrow_down_rounded, color: textSecondaryColor),
        isExpanded: true,
        items: uniqueItems.map((T item) {
          return DropdownMenuItem<T>(
            value: item,
            child: Text(
              item.toString(),
              textAlign: TextAlign.center,
              textDirection: TextDirection.rtl,
            ),
          );
        }).toList(),
        onChanged: onChanged,
      ),
    );
  }
}
