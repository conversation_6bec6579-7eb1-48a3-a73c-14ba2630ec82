import 'dart:io';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:path_provider/path_provider.dart';
import 'package:intl/intl.dart';
import '../utils/date_utils.dart';

class FallbackPDFService {
  
  /// إنشاء PDF للمعامل (بدون خطوط خارجية)
  static Future<File> generateWorkshopPDF(Map<String, dynamic> workshop) async {
    try {
      final pdf = pw.Document();

      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          textDirection: pw.TextDirection.rtl,
          build: (pw.Context context) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // العنوان الرئيسي
                pw.Container(
                  width: double.infinity,
                  padding: const pw.EdgeInsets.all(20),
                  decoration: pw.BoxDecoration(
                    color: PdfColor.fromHex('#E3F2FD'),
                    borderRadius: pw.BorderRadius.circular(10),
                  ),
                  child: pw.Column(
                    children: [
                      pw.Text(
                        'Workshop Details - تفاصيل المعمل',
                        style: pw.TextStyle(
                          fontSize: 24,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                      pw.SizedBox(height: 10),
                      pw.Text(
                        'Export Date - تاريخ التصدير: ${_getCurrentDate()}',
                        style: const pw.TextStyle(fontSize: 12),
                      ),
                    ],
                  ),
                ),

                pw.SizedBox(height: 20),

                // معلومات المعمل
                pw.Container(
                  padding: const pw.EdgeInsets.all(15),
                  decoration: pw.BoxDecoration(
                    border: pw.Border.all(color: PdfColor.fromHex('#E0E0E0')),
                    borderRadius: pw.BorderRadius.circular(8),
                  ),
                  child: pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      _buildFallbackInfoRow('Workshop Name - اسم المعمل:', workshop['name'] ?? ''),
                      _buildFallbackInfoRow('Owner Phone - رقم هاتف المالك:', workshop['ownerPhone'] ?? ''),
                      _buildFallbackInfoRow('Work Type - نوع العمل:', workshop['workType'] ?? ''),
                      _buildFallbackInfoRow('Price Per Piece - السعر لكل قطعة:', '${workshop['pricePerPiece'] ?? 0} SAR'),
                      if (workshop['isQassas'] == 1) ...[
                        _buildFallbackInfoRow('Supports Cutting - يدعم القصة:', 'Yes - نعم'),
                        _buildFallbackInfoRow('Cutting Price - سعر القصة:', '${workshop['qassasPrice'] ?? 0} SAR'),
                      ],
                      if (workshop['otherTypeName']?.isNotEmpty == true) ...[
                        _buildFallbackInfoRow('Other Work Type - نوع عمل آخر:', workshop['otherTypeName']),
                        _buildFallbackInfoRow('Other Work Price - سعر العمل الآخر:', '${workshop['otherTypePrice'] ?? 0} SAR'),
                      ],
                      if (workshop['dayName'] != null)
                        _buildFallbackInfoRow('Day Added - يوم الإضافة:', workshop['dayName']),
                      _buildFallbackInfoRow('Created Date - تاريخ الإنشاء:', _formatFallbackDate(workshop['createdAt'])),
                    ],
                  ),
                ),

                pw.Spacer(),

                // تذييل
                pw.Container(
                  width: double.infinity,
                  padding: const pw.EdgeInsets.all(10),
                  decoration: pw.BoxDecoration(
                    color: PdfColor.fromHex('#F5F5F5'),
                    borderRadius: pw.BorderRadius.circular(5),
                  ),
                  child: pw.Text(
                    'Generated by Tailors Management App - تم إنشاء هذا التقرير بواسطة تطبيق إدارة الخياطين',
                    style: const pw.TextStyle(fontSize: 10),
                    textAlign: pw.TextAlign.center,
                  ),
                ),
              ],
            );
          },
        ),
      );

      return await _saveFallbackPDF(pdf, 'workshop_${workshop['name']}_${DateTime.now().millisecondsSinceEpoch}');
    } catch (e) {
      throw Exception('Failed to create PDF file: ${e.toString()}');
    }
  }

  /// إنشاء PDF للأعمال
  static Future<File> generateWorksPDF(List<Map<String, dynamic>> works, {String? title}) async {
    try {
      final pdf = pw.Document();

      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          textDirection: pw.TextDirection.rtl,
          build: (pw.Context context) {
            return [
              // العنوان
              pw.Container(
                width: double.infinity,
                padding: const pw.EdgeInsets.all(20),
                decoration: pw.BoxDecoration(
                  color: PdfColor.fromHex('#E8F5E8'),
                  borderRadius: pw.BorderRadius.circular(10),
                ),
                child: pw.Column(
                  children: [
                    pw.Text(
                      title ?? 'Works Report - تقرير الأعمال',
                      style: pw.TextStyle(
                        fontSize: 24,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                    pw.SizedBox(height: 10),
                    pw.Text(
                      'Export Date - تاريخ التصدير: ${_getCurrentDate()}',
                      style: const pw.TextStyle(fontSize: 12),
                    ),
                    pw.Text(
                      'Total Works - عدد الأعمال: ${works.length}',
                      style: const pw.TextStyle(fontSize: 12),
                    ),
                  ],
                ),
              ),

              pw.SizedBox(height: 20),

              // قائمة الأعمال
              ...works.map((work) => pw.Container(
                margin: const pw.EdgeInsets.only(bottom: 15),
                padding: const pw.EdgeInsets.all(15),
                decoration: pw.BoxDecoration(
                  border: pw.Border.all(color: PdfColor.fromHex('#E0E0E0')),
                  borderRadius: pw.BorderRadius.circular(8),
                ),
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text(
                      'Work - عمل: ${work['workshopName'] ?? ''}',
                      style: pw.TextStyle(
                        fontSize: 16,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                    pw.SizedBox(height: 10),
                    _buildFallbackInfoRow('Piece Count - عدد القطع:', '${work['pieceCount'] ?? 0}'),
                    _buildFallbackInfoRow('Daily Expenses - المصاريف اليومية:', '${work['dailyExpenses'] ?? 0} SAR'),
                    _buildFallbackInfoRow('Price Per Piece - السعر لكل قطعة:', '${work['pricePerPiece'] ?? 0} SAR'),
                    _buildFallbackInfoRow('Total - الإجمالي:', '${work['totalPrice'] ?? 0} SAR'),
                    _buildFallbackInfoRow('Created Date - تاريخ الإنشاء:', _formatFallbackDate(work['createdAt'])),
                  ],
                ),
              )),
            ];
          },
        ),
      );

      return await _saveFallbackPDF(pdf, 'works_${DateTime.now().millisecondsSinceEpoch}');
    } catch (e) {
      throw Exception('Failed to create PDF file: ${e.toString()}');
    }
  }

  /// إنشاء PDF للمقاسات
  static Future<File> generateMeasurementsPDF(List<Map<String, dynamic>> measurements, String type) async {
    try {
      final pdf = pw.Document();

      String typeTitle = '';
      switch (type) {
        case 'clothing':
          typeTitle = 'Clothing Measurements - مقاسات الثياب';
          break;
        case 'suits':
          typeTitle = 'Suits Measurements - مقاسات البدل';
          break;
        case 'women':
          typeTitle = 'Women Measurements - مقاسات النساء';
          break;
        default:
          typeTitle = 'Measurements - المقاسات';
      }

      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          textDirection: pw.TextDirection.rtl,
          build: (pw.Context context) {
            return [
              // العنوان
              pw.Container(
                width: double.infinity,
                padding: const pw.EdgeInsets.all(20),
                decoration: pw.BoxDecoration(
                  color: PdfColor.fromHex('#F3E5F5'),
                  borderRadius: pw.BorderRadius.circular(10),
                ),
                child: pw.Column(
                  children: [
                    pw.Text(
                      typeTitle,
                      style: pw.TextStyle(
                        fontSize: 24,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                    pw.SizedBox(height: 10),
                    pw.Text(
                      'Export Date - تاريخ التصدير: ${_getCurrentDate()}',
                      style: const pw.TextStyle(fontSize: 12),
                    ),
                    pw.Text(
                      'Total Measurements - عدد المقاسات: ${measurements.length}',
                      style: const pw.TextStyle(fontSize: 12),
                    ),
                  ],
                ),
              ),

              pw.SizedBox(height: 20),

              // قائمة المقاسات
              ...measurements.map((measurement) => pw.Container(
                margin: const pw.EdgeInsets.only(bottom: 15),
                padding: const pw.EdgeInsets.all(15),
                decoration: pw.BoxDecoration(
                  border: pw.Border.all(color: PdfColors.grey300),
                  borderRadius: pw.BorderRadius.circular(8),
                ),
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text(
                      'Measurement - مقاس: ${measurement['customerName'] ?? ''}',
                      style: pw.TextStyle(
                        fontSize: 16,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                    pw.SizedBox(height: 10),
                    _buildFallbackInfoRow('Phone Number - رقم الهاتف:', measurement['phoneNumber'] ?? ''),
                    _buildFallbackInfoRow('Bill Number - رقم الفاتورة:', measurement['billNumber'] ?? ''),
                    _buildFallbackInfoRow('Fabric Type - نوع القماش:', measurement['fabricType'] ?? ''),
                    _buildFallbackInfoRow('Quantity - الكمية:', '${measurement['quantity'] ?? 0}'),
                    _buildFallbackInfoRow('Amount - المبلغ:', '${measurement['price'] ?? 0} SAR'),
                    _buildFallbackInfoRow('Paid - المدفوع:', '${measurement['paid'] ?? 0} SAR'),
                    _buildFallbackInfoRow('Remaining - المتبقي:', '${measurement['remaining'] ?? 0} SAR'),
                    if (measurement['dayName'] != null)
                      _buildFallbackInfoRow('Day Added - يوم الإضافة:', measurement['dayName']),
                    _buildFallbackInfoRow('Created Date - تاريخ الإنشاء:', _formatFallbackDate(measurement['createdAt'])),
                  ],
                ),
              )),
            ];
          },
        ),
      );

      return await _saveFallbackPDF(pdf, '${typeTitle}_${DateTime.now().millisecondsSinceEpoch}');
    } catch (e) {
      throw Exception('Failed to create PDF file: ${e.toString()}');
    }
  }

  /// إنشاء PDF للفواتير
  static Future<File> generateInvoicesPDF(List<Map<String, dynamic>> invoices) async {
    try {
      final pdf = pw.Document();

      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          textDirection: pw.TextDirection.rtl,
          build: (pw.Context context) {
            return [
              // العنوان
              pw.Container(
                width: double.infinity,
                padding: const pw.EdgeInsets.all(20),
                decoration: pw.BoxDecoration(
                  color: PdfColor.fromHex('#FFF3E0'),
                  borderRadius: pw.BorderRadius.circular(10),
                ),
                child: pw.Column(
                  children: [
                    pw.Text(
                      'Invoices Report - تقرير الفواتير',
                      style: pw.TextStyle(
                        fontSize: 24,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                    pw.SizedBox(height: 10),
                    pw.Text(
                      'Export Date - تاريخ التصدير: ${_getCurrentDate()}',
                      style: const pw.TextStyle(fontSize: 12),
                    ),
                    pw.Text(
                      'Total Invoices - عدد الفواتير: ${invoices.length}',
                      style: const pw.TextStyle(fontSize: 12),
                    ),
                  ],
                ),
              ),

              pw.SizedBox(height: 20),

              // قائمة الفواتير
              ...invoices.map((invoice) => pw.Container(
                margin: const pw.EdgeInsets.only(bottom: 15),
                padding: const pw.EdgeInsets.all(15),
                decoration: pw.BoxDecoration(
                  border: pw.Border.all(color: PdfColor.fromHex('#E0E0E0')),
                  borderRadius: pw.BorderRadius.circular(8),
                ),
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text(
                      'Invoice - فاتورة: ${invoice['invoiceNumber'] ?? ''}',
                      style: pw.TextStyle(
                        fontSize: 16,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                    pw.SizedBox(height: 10),
                    _buildFallbackInfoRow('Shop Name - اسم المحل:', invoice['shopName'] ?? ''),
                    _buildFallbackInfoRow('Customer Name - اسم العميل:', invoice['customerName'] ?? ''),
                    _buildFallbackInfoRow('Clothes Count - عدد الثياب:', '${invoice['clothesCount'] ?? 0}'),
                    _buildFallbackInfoRow('Status - الحالة:', invoice['isReady'] == 1 ? 'Ready - جاهزة' : 'Not Ready - غير جاهزة'),
                    _buildFallbackInfoRow('Created Date - تاريخ الإنشاء:', _formatFallbackDate(invoice['createdAt'])),
                  ],
                ),
              )),
            ];
          },
        ),
      );

      return await _saveFallbackPDF(pdf, 'invoices_${DateTime.now().millisecondsSinceEpoch}');
    } catch (e) {
      throw Exception('Failed to create PDF file: ${e.toString()}');
    }
  }

  /// إنشاء PDF للإحصائيات
  static Future<File> generateStatisticsPDF(Map<String, dynamic> stats, {String? title}) async {
    try {
      final pdf = pw.Document();

      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          textDirection: pw.TextDirection.rtl,
          build: (pw.Context context) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // العنوان
                pw.Container(
                  width: double.infinity,
                  padding: const pw.EdgeInsets.all(20),
                  decoration: pw.BoxDecoration(
                    color: PdfColor.fromHex('#E1F5FE'),
                    borderRadius: pw.BorderRadius.circular(10),
                  ),
                  child: pw.Column(
                    children: [
                      pw.Text(
                        title ?? 'Statistics Report - تقرير الإحصائيات',
                        style: pw.TextStyle(
                          fontSize: 24,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                      pw.SizedBox(height: 10),
                      pw.Text(
                        'Export Date - تاريخ التصدير: ${_getCurrentDate()}',
                        style: const pw.TextStyle(fontSize: 12),
                      ),
                    ],
                  ),
                ),

                pw.SizedBox(height: 20),

                // الإحصائيات
                pw.Container(
                  padding: const pw.EdgeInsets.all(15),
                  decoration: pw.BoxDecoration(
                    border: pw.Border.all(color: PdfColor.fromHex('#E0E0E0')),
                    borderRadius: pw.BorderRadius.circular(8),
                  ),
                  child: pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text(
                        'Financial Statistics - الإحصائيات المالية',
                        style: pw.TextStyle(
                          fontSize: 18,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                      pw.SizedBox(height: 15),
                      _buildFallbackInfoRow('Total Earnings - إجمالي الأرباح:', '${stats['totalEarnings'] ?? 0} SAR'),
                      _buildFallbackInfoRow('Total Expenses - إجمالي المصاريف:', '${stats['totalExpenses'] ?? 0} SAR'),
                      _buildFallbackInfoRow('Net Profit - صافي الربح:', '${stats['netProfit'] ?? 0} SAR'),
                      _buildFallbackInfoRow('Total Works - عدد الأعمال:', '${stats['totalWorks'] ?? 0}'),
                      _buildFallbackInfoRow('Total Workshops - عدد المعامل:', '${stats['totalWorkshops'] ?? 0}'),
                      _buildFallbackInfoRow('Total Invoices - عدد الفواتير:', '${stats['totalInvoices'] ?? 0}'),
                      _buildFallbackInfoRow('Total Measurements - عدد المقاسات:', '${stats['totalMeasurements'] ?? 0}'),
                    ],
                  ),
                ),

                pw.Spacer(),

                // تذييل
                pw.Container(
                  width: double.infinity,
                  padding: const pw.EdgeInsets.all(10),
                  decoration: pw.BoxDecoration(
                    color: PdfColor.fromHex('#F5F5F5'),
                    borderRadius: pw.BorderRadius.circular(5),
                  ),
                  child: pw.Text(
                    'Generated by Tailors Management App - تم إنشاء هذا التقرير بواسطة تطبيق إدارة الخياطين',
                    style: const pw.TextStyle(fontSize: 10),
                    textAlign: pw.TextAlign.center,
                  ),
                ),
              ],
            );
          },
        ),
      );

      return await _saveFallbackPDF(pdf, 'statistics_${DateTime.now().millisecondsSinceEpoch}');
    } catch (e) {
      throw Exception('Failed to create PDF file: ${e.toString()}');
    }
  }

  // دوال مساعدة
  static pw.Widget _buildFallbackInfoRow(String label, String value) {
    return pw.Padding(
      padding: const pw.EdgeInsets.symmetric(vertical: 3),
      child: pw.Row(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.SizedBox(
            width: 180,
            child: pw.Text(
              label,
              style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
            ),
          ),
          pw.Expanded(
            child: pw.Text(value),
          ),
        ],
      ),
    );
  }

  static String _formatFallbackDate(String? dateString) {
    if (dateString == null) return 'Not specified - غير محدد';
    try {
      final date = DateTime.parse(dateString);
      return DateFormat('yyyy/MM/dd').format(date);
    } catch (e) {
      return 'Not specified - غير محدد';
    }
  }

  static String _getCurrentDate() {
    return DateFormat('yyyy/MM/dd').format(DateTime.now());
  }

  static Future<File> _saveFallbackPDF(pw.Document pdf, String fileName) async {
    try {
      final output = await getTemporaryDirectory();
      final file = File('${output.path}/$fileName.pdf');
      await file.writeAsBytes(await pdf.save());
      return file;
    } catch (e) {
      throw Exception('Failed to save PDF file: ${e.toString()}');
    }
  }
}
