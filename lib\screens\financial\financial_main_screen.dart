// الصفحة الرئيسية للنظام المالي المتكامل
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'enhanced_initial_balance_screen.dart';
import 'penalties_screen.dart';
import 'extra_work_screen.dart';
import 'enhanced_monthly_settlement_screen.dart';
import 'enhanced_statistics_screen.dart';
import 'widgets/financial_status_card.dart';
import '../../services/financial_state_manager.dart';

class FinancialMainScreen extends StatefulWidget {
  final int tailorId;
  final String tailorName;

  const FinancialMainScreen({
    super.key,
    required this.tailorId,
    required this.tailorName,
  });

  @override
  State<FinancialMainScreen> createState() => _FinancialMainScreenState();
}

class _FinancialMainScreenState extends State<FinancialMainScreen>
    with TickerProviderStateMixin {

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _slideAnimation;

  final FinancialStateManager _financialManager = FinancialStateManager();

  @override
  void initState() {
    super.initState();

    // إعداد الأنيميشن
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<double>(
      begin: 0.5,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));

    _animationController.forward();

    // تهيئة مدير الحالة المالية
    _financialManager.setCurrentWorkshop(widget.tailorId);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: Colors.grey[50],
        body: FadeTransition(
          opacity: _fadeAnimation,
          child: SingleChildScrollView(
            child: Column(
              children: [
                // هيدر محسن
                _buildEnhancedHeader(),

                // المحتوى الرئيسي
                SlideTransition(
                  position: Tween<Offset>(
                    begin: Offset(0, _slideAnimation.value),
                    end: Offset.zero,
                  ).animate(_animationController),
                  child: Column(
                    children: [
                      // كارد الحالة المالية
                      FinancialStatusCard(
                        workshopId: widget.tailorId,
                        onTap: () => _navigateToSummary(),
                      ),

                      // شبكة الخيارات المالية
                      _buildFinancialOptionsGrid(),

                      // نصائح مالية
                      _buildFinancialTips(),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEnhancedHeader() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.indigo[700]!, Colors.indigo[500]!],
          begin: Alignment.topRight,
          end: Alignment.bottomLeft,
        ),
        borderRadius: const BorderRadius.vertical(bottom: Radius.circular(30)),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: Row(
            children: [
              GestureDetector(
                onTap: () => Navigator.pop(context),
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(Icons.arrow_back, color: Colors.white, size: 20),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'النظام المالي',
                      style: GoogleFonts.cairo(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      widget.tailorName,
                      style: GoogleFonts.cairo(
                        color: Colors.white.withOpacity(0.9),
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(Icons.account_balance, color: Colors.white, size: 16),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFinancialOptionsGrid() {
    final options = [
      {
        'title': 'الإحصائيات المالية',
        'subtitle': 'ملخص شامل للوضع المالي',
        'icon': Icons.analytics,
        'color': Colors.blue,
        'onTap': () => _navigateToSummary(),
      },
      {
        'title': 'الرصيد المرحل',
        'subtitle': 'إدارة الرصيد من الشهر السابق',
        'icon': Icons.account_balance_wallet,
        'color': Colors.cyan,
        'onTap': () => _navigateToInitialBalance(),
      },
      {
        'title': 'الغرامات',
        'subtitle': 'تسجيل ومتابعة الغرامات',
        'icon': Icons.warning,
        'color': Colors.red,
        'onTap': () => _navigateToPenalties(),
      },
      {
        'title': 'الأعمال الإضافية',
        'subtitle': 'تسجيل الأعمال والمكافآت',
        'icon': Icons.add_business,
        'color': Colors.green,
        'onTap': () => _navigateToExtraWork(),
      },
      {
        'title': 'التصفية الشهرية',
        'subtitle': 'إجراء تصفية نهاية الشهر',
        'icon': Icons.receipt_long,
        'color': Colors.purple,
        'onTap': () => _navigateToMonthlySettlement(),
      },
      {
        'title': 'التقارير المالية',
        'subtitle': 'تقارير مفصلة وتحليلات',
        'icon': Icons.assessment,
        'color': Colors.orange,
        'onTap': () => _showComingSoon(),
      },
    ];

    return Container(
      margin: const EdgeInsets.all(16),
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 1.2,
        ),
        itemCount: options.length,
        itemBuilder: (context, index) {
          final option = options[index];
          return _buildOptionCard(option);
        },
      ),
    );
  }

  Widget _buildOptionCard(Map<String, dynamic> option) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: option['onTap'],
        borderRadius: BorderRadius.circular(20),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
            border: Border.all(
              color: option['color'].withOpacity(0.2),
              width: 2,
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: option['color'].withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    option['icon'],
                    size: 28,
                    color: option['color'],
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  option['title'],
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[800],
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 6),
                Text(
                  option['subtitle'],
                  style: GoogleFonts.cairo(
                    fontSize: 11,
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFinancialTips() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.amber[50]!, Colors.amber[100]!],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.amber.withOpacity(0.2),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.amber[600],
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.lightbulb,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'نصائح مالية مهمة',
                style: GoogleFonts.cairo(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.amber[800],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildTipItem('حدث الرصيد المرحل شهرياً'),
          _buildTipItem('سجل الغرامات فور حدوثها'),
          _buildTipItem('راجع الإحصائيات بانتظام'),
        ],
      ),
    );
  }

  Widget _buildTipItem(String tip) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 6),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: const EdgeInsets.only(top: 4),
            width: 5,
            height: 5,
            decoration: BoxDecoration(
              color: Colors.amber[600],
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 10),
          Expanded(
            child: Text(
              tip,
              style: GoogleFonts.cairo(
                fontSize: 12,
                color: Colors.amber[800],
                height: 1.3,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // دوال التنقل
  void _navigateToSummary() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EnhancedStatisticsScreen(
          workshopId: widget.tailorId,
          workshopName: widget.tailorName,
        ),
      ),
    );
  }

  void _navigateToInitialBalance() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EnhancedInitialBalanceScreen(
          tailorId: widget.tailorId,
          tailorName: widget.tailorName,
        ),
      ),
    );
  }

  void _navigateToPenalties() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PenaltiesScreen(
          tailorId: widget.tailorId,
          tailorName: widget.tailorName,
        ),
      ),
    );
  }

  void _navigateToExtraWork() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ExtraWorkScreen(
          tailorId: widget.tailorId,
          tailorName: widget.tailorName,
        ),
      ),
    );
  }

  void _navigateToMonthlySettlement() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EnhancedMonthlySettlementScreen(
          tailorId: widget.tailorId,
          tailorName: widget.tailorName,
        ),
      ),
    );
  }

  void _showComingSoon() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('قريباً'),
        content: const Text('هذه الميزة ستكون متاحة قريباً'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }
}
