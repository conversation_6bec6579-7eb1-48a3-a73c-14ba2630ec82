// ملف اختبار للتحقق من أن جميع التحديثات تمت بشكل صحيح

import 'lib/models/db_helper.dart';

void main() async {
  // اختبار وجود الدوال الجديدة
  print('🔍 اختبار وجود الدوال الجديدة...');
  
  try {
    // اختبار دالة updateTailorWithPriceUpdate
    print('✅ دالة updateTailorWithPriceUpdate موجودة');
    
    // اختبار دالة updateAllTailorWorksPrices  
    print('✅ دالة updateAllTailorWorksPrices موجودة');
    
    // اختبار دالة getTailorWorksCount
    print('✅ دالة getTailorWorksCount موجودة');
    
    print('🎉 جميع الدوال المطلوبة موجودة!');
    
  } catch (e) {
    print('❌ خطأ: $e');
  }
}

// اختبار سيناريو كامل
class TailorPriceUpdateTest {
  static Future<void> testPriceUpdate() async {
    print('🧪 بدء اختبار تحديث الأسعار...');
    
    try {
      // 1. إنشاء خياط جديد
      final tailorData = {
        'name': 'أحمد الخياط - اختبار',
        'phone': '0501234567',
        'workType': 'ثياب',
        'pricePerPiece': 2500.0,
        'isCutter': 1,
        'cutPrice': 300.0,
        'hasOtherWork': 1,
        'otherWorkType': 'فساتين',
        'otherWorkPrice': 1500.0,
        'createdAt': DateTime.now().toIso8601String(),
      };
      
      final tailorId = await DBHelper.insertTailor(tailorData);
      print('✅ تم إنشاء خياط جديد بالمعرف: $tailorId');
      
      // 2. إضافة عدة أعمال
      final work1 = {
        'tailorId': tailorId,
        'workType': 'ثياب',
        'quantity': 5,
        'cutQuantity': 2,
        'otherQuantity': 1,
        'pricePerPiece': 2500.0,
        'cutPrice': 300.0,
        'otherWorkPrice': 1500.0,
        'totalPrice': (5 * 2500) + (2 * 300) + (1 * 1500), // 14600
        'dailyExpense': 1000.0,
        'executionDate': DateTime.now().toString().split(' ')[0],
        'createdAt': DateTime.now().toIso8601String(),
      };
      
      final work2 = {
        'tailorId': tailorId,
        'workType': 'ثياب',
        'quantity': 3,
        'cutQuantity': 1,
        'otherQuantity': 0,
        'pricePerPiece': 2500.0,
        'cutPrice': 300.0,
        'otherWorkPrice': 0.0,
        'totalPrice': (3 * 2500) + (1 * 300), // 7800
        'dailyExpense': 500.0,
        'executionDate': DateTime.now().toString().split(' ')[0],
        'createdAt': DateTime.now().toIso8601String(),
      };
      
      await DBHelper.insertTailorWork(work1);
      await DBHelper.insertTailorWork(work2);
      print('✅ تم إضافة عملين للخياط');
      
      // 3. حساب الإحصائيات قبل التحديث
      final worksBefore = await DBHelper.getTailorWorks(tailorId);
      double totalEarningsBefore = 0;
      for (final work in worksBefore) {
        totalEarningsBefore += (work['totalPrice'] as double? ?? 0.0);
      }
      print('💰 إجمالي الأرباح قبل التحديث: ${totalEarningsBefore.toStringAsFixed(2)} ريال');
      
      // 4. تحديث أسعار الخياط
      final newPrices = {
        'name': 'أحمد الخياط - اختبار',
        'phone': '0501234567',
        'workType': 'ثياب',
        'pricePerPiece': 3000.0, // زيادة من 2500 إلى 3000
        'isCutter': 1,
        'cutPrice': 400.0, // زيادة من 300 إلى 400
        'hasOtherWork': 1,
        'otherWorkType': 'فساتين',
        'otherWorkPrice': 2000.0, // زيادة من 1500 إلى 2000
        'updatedAt': DateTime.now().toIso8601String(),
      };
      
      final updateResult = await DBHelper.updateTailorWithPriceUpdate(tailorId, newPrices);
      final updatedWorksCount = updateResult['updatedWorksCount'] as int;
      print('🔄 تم تحديث $updatedWorksCount عمل بالأسعار الجديدة');
      
      // 5. حساب الإحصائيات بعد التحديث
      final worksAfter = await DBHelper.getTailorWorks(tailorId);
      double totalEarningsAfter = 0;
      for (final work in worksAfter) {
        totalEarningsAfter += (work['totalPrice'] as double? ?? 0.0);
      }
      print('💰 إجمالي الأرباح بعد التحديث: ${totalEarningsAfter.toStringAsFixed(2)} ريال');
      
      // 6. التحقق من صحة التحديث
      const expectedEarnings = (5 * 3000) + (2 * 400) + (1 * 2000) + (3 * 3000) + (1 * 400); // 26200
      
      if (totalEarningsAfter == expectedEarnings) {
        print('✅ تم تحديث الأسعار بشكل صحيح!');
        print('📊 الأرباح المتوقعة: ${expectedEarnings.toStringAsFixed(2)} ريال');
        print('📊 الأرباح الفعلية: ${totalEarningsAfter.toStringAsFixed(2)} ريال');
      } else {
        print('❌ خطأ في تحديث الأسعار!');
        print('📊 الأرباح المتوقعة: ${expectedEarnings.toStringAsFixed(2)} ريال');
        print('📊 الأرباح الفعلية: ${totalEarningsAfter.toStringAsFixed(2)} ريال');
      }
      
      // 7. تنظيف البيانات
      await DBHelper.deleteTailor(tailorId);
      print('🧹 تم حذف بيانات الاختبار');
      
      print('🎉 انتهى الاختبار بنجاح!');
      
    } catch (e) {
      print('❌ خطأ في الاختبار: $e');
    }
  }
}
