# Implementation Plan for SyncService Error and Black Screen Fix

## 1. SyncService Error Fix

The error message indicates that `SyncService.syncStatusStream` is being referenced in `tailor_home_screen.dart`, but this property doesn't exist in the current SyncService implementation.

### Step 1: Add StreamController to SyncService

Open `lib/services/sync_service.dart` and add the following code:

```dart
// Add at the top with other imports (if not already there)
import 'dart:async';

// Add inside the SyncService class, right after the other static variables
static final StreamController<String> _syncStatusController = 
    StreamController<String>.broadcast();
    
static Stream<String> get syncStatusStream => _syncStatusController.stream;
```

### Step 2: Update SyncService methods to emit status messages

Still in `lib/services/sync_service.dart`, modify the existing methods:

```dart
// Update the syncRemoteToLocal method
static Future<void> syncRemoteToLocal() async {
  _syncStatusController.add("جاري المزامنة من السحابة...");
  print("بدأ جلب البيانات من Firebase");
  // Rest of the existing method...
  
  // Add at the end of the method before the closing brace
  _syncStatusController.add("تمت المزامنة من السحابة بنجاح");
}

// Update the syncLocalDataToFirebase method
static Future<void> syncLocalDataToFirebase() async {
  _syncStatusController.add("جاري رفع البيانات للسحابة...");
  // Rest of the existing method...
  
  // Add at the end of the method before the closing brace
  _syncStatusController.add("تم رفع البيانات للسحابة بنجاح");
}

// Update the dispose method
static void dispose() {
  _connSub?.cancel();
  _connSub = null;
  _syncStatusController.close(); // Add this line
}
```

### Step 3: Update TailorHomeScreen to properly handle the subscription

In `lib/screens/tailor_home_screen.dart`, find the State class and add/modify:

```dart
// Add these variables to your State class if not already there
StreamSubscription? _syncSub;
String _syncMessage = '';

// In your initState method, add:
@override
void initState() {
  super.initState();
  _fetchWorkshops();
  
  final user = FirebaseAuth.instance.currentUser;
  if (user != null) {
    _syncSub = SyncService.syncStatusStream.listen((msg) {
      setState(() => _syncMessage = msg);
    });
  }
}

// Make sure to cancel the subscription in dispose:
@override
void dispose() {
  _syncSub?.cancel();
  super.dispose();
}
```

Optionally, you can display the sync message in the UI, for example in the AppBar:

```dart
appBar: AppBar(
  title: Text('المعامل'),
  backgroundColor: Colors.teal,
  bottom: _syncMessage.isNotEmpty 
    ? PreferredSize(
        preferredSize: Size.fromHeight(30),
        child: Container(
          padding: EdgeInsets.all(5),
          color: Colors.amber.shade100,
          alignment: Alignment.center,
          child: Text(_syncMessage, style: TextStyle(color: Colors.black87)),
        ),
      ) 
    : null,
),
```

## 2. Black Screen Issue Fix

The black screen issue is likely caused by one of these problems:

1. **Remote Image Loading Failure**: The app is trying to load images from remote URLs (like Unsplash) which might be failing
2. **Network Image Error Handling**: No fallback when network images fail to load
3. **UI Rendering Issue**: Stack widget may be causing layout issues

### Step 1: Create an assets folder and add local images

1. Create an `assets/images` folder in your project root if it doesn't exist
2. Add a background image (e.g., `background.jpg`) to this folder

### Step 2: Update pubspec.yaml to include assets

Add the following to your `pubspec.yaml` file under the `flutter` section:

```yaml
flutter:
  assets:
    - assets/images/
```

### Step 3: Update role_selection_screen.dart to use local images

Replace remote image loading with local assets:

```dart
// Replace this:
Image.network(
  'https://images.unsplash.com/photo-1520975910311-c19669e9d1ea?auto=format&fit=crop&w=800&q=80',
  fit: BoxFit.cover,
  color: Colors.black.withOpacity(0.4),
  colorBlendMode: BlendMode.darken,
),

// With this:
Image.asset(
  'assets/images/background.jpg',
  fit: BoxFit.cover,
  color: Colors.black.withOpacity(0.4),
  colorBlendMode: BlendMode.darken,
),
```

### Step 4: Add error handling for network images

If you still need to use network images in some places, add proper error handling:

```dart
Image.network(
  'https://example.com/image.jpg',
  fit: BoxFit.cover,
  loadingBuilder: (context, child, loadingProgress) {
    if (loadingProgress == null) return child;
    return Center(child: CircularProgressIndicator());
  },
  errorBuilder: (context, error, stackTrace) {
    return Container(
      color: Colors.grey[300],
      child: Center(
        child: Icon(Icons.error, color: Colors.red),
      ),
    );
  },
)
```

### Step 5: Simplify UI structure to diagnose rendering issues

If you're still experiencing a black screen, try simplifying the UI structure:

```dart
body: SafeArea(
  child: Column(
    mainAxisAlignment: MainAxisAlignment.center,
    children: [
      Text(
        'مرحبًا بك في تطبيق خياطك',
        style: TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.bold,
          color: Colors.black,
        ),
        textAlign: TextAlign.center,
      ),
      SizedBox(height: 20),
      Text(
        'اختر نوع حسابك للبدء',
        style: TextStyle(fontSize: 18, color: Colors.grey[700]),
        textAlign: TextAlign.center,
      ),
      SizedBox(height: 40),
      Padding(
        padding: const EdgeInsets.symmetric(horizontal: 32.0),
        child: ElevatedButton.icon(
          icon: Icon(Icons.cut),
          label: Text('هل أنت خياط؟'),
          style: ElevatedButton.styleFrom(
            minimumSize: Size(double.infinity, 50),
            backgroundColor: Colors.teal,
          ),
          onPressed: () => _selectRole('tailor'),
        ),
      ),
      SizedBox(height: 20),
      Padding(
        padding: const EdgeInsets.symmetric(horizontal: 32.0),
        child: ElevatedButton.icon(
          icon: Icon(Icons.admin_panel_settings),
          label: Text('هل أنت مدير الخياطين'),
          style: ElevatedButton.styleFrom(
            minimumSize: Size(double.infinity, 50),
            backgroundColor: Colors.deepPurple.shade700,
          ),
          onPressed: () => _selectRole('manager'),
        ),
      ),
    ],
  ),
)
```

## Implementation Notes

1. After making these changes, run `flutter pub get` to update dependencies if you've added assets to pubspec.yaml
2. Test the app thoroughly to ensure both issues are resolved
3. If black screen persists, enable Flutter debug painting to check for layout issues:
   ```dart
   import 'package:flutter/rendering.dart';
   
   void main() {
     debugPaintSizeEnabled = true;
     runApp(MyApp());
   }
   ```

## Next Steps

If you'd like to implement these changes, I recommend switching to "Code" mode which can edit .dart files directly:

```
<switch_mode>
<mode_slug>code</mode_slug>
<reason>Need to make code changes to fix the SyncService error and black screen issue</reason>
</switch_mode>