// ملف اختبار للحل الكامل لجميع مشاكل Overflow
import 'package:flutter/material.dart';
import 'screens/financial/financial_main_screen.dart';
import 'screens/financial/enhanced_statistics_screen.dart';

class TestCompleteOverflowSolution extends StatelessWidget {
  const TestCompleteOverflowSolution({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'الحل الكامل لمشاكل Overflow',
      theme: ThemeData(
        primarySwatch: Colors.green,
        fontFamily: 'Cairo',
      ),
      home: const TestCompleteSolutionScreen(),
    );
  }
}

class TestCompleteSolutionScreen extends StatelessWidget {
  const TestCompleteSolutionScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الحل الكامل لـ Overflow'),
        backgroundColor: Colors.green[600],
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Icon(
              Icons.verified_user,
              size: 80,
              color: Colors.green,
            ),
            const SizedBox(height: 20),
            const Text(
              'تم حل جميع مشاكل Overflow!',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.green,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            const Text(
              'الصفحات المحسنة:',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            
            // كارد الصفحة الرئيسية
            _buildPageCard(
              'الصفحة الرئيسية للنظام المالي',
              'تم تغيير الهيكل من Column إلى Row',
              Icons.account_balance,
              Colors.indigo,
              () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const FinancialMainScreen(
                    tailorId: 1,
                    tailorName: 'خياط تجريبي - بدون Overflow',
                  ),
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // كارد صفحة الإحصائيات
            _buildPageCard(
              'صفحة الإحصائيات المالية',
              'تم تغيير الهيكل من Column إلى Row',
              Icons.analytics,
              Colors.teal,
              () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const EnhancedStatisticsScreen(
                    workshopId: 1,
                    workshopName: 'معمل تجريبي - بدون Overflow',
                  ),
                ),
              ),
            ),
            
            const SizedBox(height: 30),
            
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.green[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.green[200]!),
              ),
              child: const Column(
                children: [
                  Icon(Icons.check_circle, color: Colors.green, size: 32),
                  SizedBox(height: 8),
                  Text(
                    'النظام مستقر 100%',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    'جميع الصفحات تعمل بسلاسة على جميع الأجهزة',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.green,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 20),
            
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '🔧 الحل المطبق:',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    '• إزالة الارتفاع الثابت من الهيدر',
                    style: TextStyle(fontSize: 14, color: Colors.blue),
                  ),
                  Text(
                    '• تغيير التخطيط من عمودي إلى أفقي',
                    style: TextStyle(fontSize: 14, color: Colors.blue),
                  ),
                  Text(
                    '• استخدام mainAxisSize.min',
                    style: TextStyle(fontSize: 14, color: Colors.blue),
                  ),
                  Text(
                    '• تحسين استغلال المساحة',
                    style: TextStyle(fontSize: 14, color: Colors.blue),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPageCard(String title, String description, IconData icon, Color color, VoidCallback onTap) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: color.withOpacity(0.3)),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(icon, color: color, size: 24),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              Icon(Icons.arrow_forward_ios, color: color, size: 16),
            ],
          ),
        ),
      ),
    );
  }
}

void main() {
  runApp(const TestCompleteOverflowSolution());
}
