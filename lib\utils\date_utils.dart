import 'package:intl/intl.dart';

class AppDateUtils {
  // قائمة أسماء الأشهر باللغة العربية
  static const List<String> _arabicMonthNames = [
    'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
    'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
  ];

  /// الحصول على اسم اليوم الحالي باللغة العربية
  static String getCurrentDayName() {
    final now = DateTime.now();

    switch (now.weekday) {
      case 1: return 'الإثنين';
      case 2: return 'الثلاثاء';
      case 3: return 'الأربعاء';
      case 4: return 'الخميس';
      case 5: return 'الجمعة';
      case 6: return 'السبت';
      case 7: return 'الأحد';
      default: return 'غير معروف';
    }
  }

  /// الحصول على اسم يوم معين باللغة العربية
  static String getDayName(DateTime date) {
    switch (date.weekday) {
      case 1: return 'الإثنين';
      case 2: return 'الثلاثاء';
      case 3: return 'الأربعاء';
      case 4: return 'الخميس';
      case 5: return 'الجمعة';
      case 6: return 'السبت';
      case 7: return 'الأحد';
      default: return 'غير معروف';
    }
  }

  /// الحصول على التاريخ الحالي مع اسم اليوم
  static String getCurrentDateWithDay() {
    final now = DateTime.now();
    final dayName = getCurrentDayName();
    final formattedDate = DateFormat('yyyy/MM/dd').format(now);
    return '$dayName - $formattedDate';
  }

  /// الحصول على تاريخ معين مع اسم اليوم
  static String getDateWithDay(DateTime date) {
    final dayName = getDayName(date);
    final formattedDate = DateFormat('yyyy/MM/dd').format(date);
    return '$dayName - $formattedDate';
  }

  /// الحصول على التاريخ الحالي بصيغة ISO
  static String getCurrentDateISO() {
    return DateTime.now().toIso8601String();
  }

  /// تحويل تاريخ ISO إلى تاريخ مع اسم اليوم
  static String formatISODateWithDay(String? isoDate) {
    if (isoDate == null || isoDate.isEmpty) return 'غير محدد';

    try {
      final date = DateTime.parse(isoDate);
      return getDateWithDay(date);
    } catch (e) {
      return 'تاريخ غير صحيح';
    }
  }

  /// الحصول على التاريخ بصيغة عربية كاملة
  static String getFullArabicDate(DateTime date) {
    final dayName = getDayName(date);
    final monthName = _arabicMonthNames[date.month - 1];
    return '$dayName، ${date.day} $monthName ${date.year}';
  }

  /// الحصول على التاريخ الحالي بصيغة عربية كاملة
  static String getCurrentFullArabicDate() {
    return getFullArabicDate(DateTime.now());
  }

  /// رسالة نجاح العملية مع اسم اليوم
  static String getSuccessMessage(String operation) {
    final dayName = getCurrentDayName();
    final message = 'تمت $operation يوم $dayName';
    return message;
  }

  /// رسائل نجاح مخصصة للعمليات المختلفة
  static String getWorkAddedMessage() {
    return getSuccessMessage('إضافة العمل');
  }

  static String getWorkshopAddedMessage() {
    return getSuccessMessage('إضافة المعمل');
  }

  static String getInvoiceAddedMessage() {
    return getSuccessMessage('إضافة الفاتورة');
  }

  static String getMeasurementAddedMessage() {
    return getSuccessMessage('إضافة المقاس');
  }

  static String getTailorAddedMessage() {
    return getSuccessMessage('إضافة الخياط');
  }

  static String getWorkUpdatedMessage() {
    return getSuccessMessage('تحديث العمل');
  }

  static String getWorkshopUpdatedMessage() {
    return getSuccessMessage('تحديث المعمل');
  }

  static String getInvoiceUpdatedMessage() {
    return getSuccessMessage('تحديث الفاتورة');
  }

  static String getMeasurementUpdatedMessage() {
    return getSuccessMessage('تحديث المقاس');
  }

  static String getTailorUpdatedMessage() {
    return getSuccessMessage('تحديث الخياط');
  }

  /// تحويل وقت إلى نص عربي
  static String formatTimeArabic(DateTime dateTime) {
    final hour = dateTime.hour;
    final minute = dateTime.minute;
    final period = hour >= 12 ? 'مساءً' : 'صباحاً';
    final displayHour = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour);

    return '${displayHour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')} $period';
  }

  /// الحصول على التاريخ والوقت الحالي بصيغة عربية
  static String getCurrentDateTimeArabic() {
    final now = DateTime.now();
    final date = getFullArabicDate(now);
    final time = formatTimeArabic(now);
    return '$date - $time';
  }

  /// التحقق من أن التاريخ هو اليوم
  static bool isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year &&
           date.month == now.month &&
           date.day == now.day;
  }

  /// التحقق من أن التاريخ هو أمس
  static bool isYesterday(DateTime date) {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return date.year == yesterday.year &&
           date.month == yesterday.month &&
           date.day == yesterday.day;
  }

  /// الحصول على نص وصفي للتاريخ (اليوم، أمس، إلخ)
  static String getRelativeDateText(DateTime date) {
    if (isToday(date)) {
      return 'اليوم - ${getDayName(date)}';
    } else if (isYesterday(date)) {
      return 'أمس - ${getDayName(date)}';
    } else {
      final difference = DateTime.now().difference(date).inDays;
      if (difference < 7) {
        return 'منذ $difference أيام - ${getDayName(date)}';
      } else {
        return getDateWithDay(date);
      }
    }
  }

  /// تحويل تاريخ ISO إلى نص وصفي
  static String formatISODateRelative(String? isoDate) {
    if (isoDate == null || isoDate.isEmpty) return 'غير محدد';

    try {
      final date = DateTime.parse(isoDate);
      return getRelativeDateText(date);
    } catch (e) {
      return 'تاريخ غير صحيح';
    }
  }
}
