import 'package:flutter/material.dart';
import 'measurement_styles.dart';
import '../models/db_helper.dart';
import '../screens/measurement_detail_options_screen.dart';
import '../utils/number_utils.dart';
import '../utils/date_utils.dart';

class ClothingMeasurementsScreen extends StatefulWidget {
  final int? measurementId;
  final int? workshopId;

  const ClothingMeasurementsScreen({super.key, this.measurementId, this.workshopId});

  @override
  State<ClothingMeasurementsScreen> createState() => _ClothingMeasurementsScreenState();
}

class _ClothingMeasurementsScreenState extends State<ClothingMeasurementsScreen> {
  bool _isLoading = false;
  bool _isEdit = false;
  final _formKey = GlobalKey<FormState>();
  final billController = TextEditingController();
  final nameController = TextEditingController();
  final phoneController = TextEditingController();
  final quantityController = TextEditingController();
  final dateController = TextEditingController();
  final priceController = TextEditingController();
  final paidController = TextEditingController();
  final remainController = TextEditingController();
  final noteController = TextEditingController();
  final deliveryDateController = TextEditingController();

  final Map<String, TextEditingController> measurements = {
    'الطول': TextEditingController(),
    'الكتف': TextEditingController(),
    'طول الكم': TextEditingController(),
    'وسع الصدر': TextEditingController(),
    'الرقبه': TextEditingController(),
    'تنزيله اليد': TextEditingController(),
    'وسط اليد': TextEditingController(),
    'طول الكبك': TextEditingController(),
    'ارتفاع الكبك': TextEditingController(),
    'الخطوة': TextEditingController(),
  };

  String? selectedFabric;
  final fabricController = TextEditingController();
  List<String> fabricOptions = ['قطن', 'صوف', 'كشمير'];

  String? selectedNeck;
  String? selectedZipper;
  String? selectedHand;
  String? selectedPocket;
  String? selectedStyle;
  String? selectedButtons;
  String? selectedCuff;

  // قوائم الخيارات من قاعدة البيانات
  Map<String, List<String>> _detailOptions = {
    'neck': [],
    'zipper': [],
    'hand': [],
    'pocket': [],
    'style': [],
    'button': [],
    'cuff': [],
  };

  @override
  void initState() {
    super.initState();
    _loadDetailOptions();
    _isEdit = widget.measurementId != null;
    if (_isEdit) {
      _loadMeasurement();
    }
  }

  Future<void> _loadDetailOptions() async {
    try {
      final options = await DBHelper.getAllDetailOptions();
      setState(() {
        _detailOptions = options;
      });
    } catch (e) {
      // في حالة الخطأ، استخدم قيم افتراضية
      setState(() {
        _detailOptions = {
          'neck': ['عادية', 'مفتوحة', 'مغلقة', 'بياقة'],
          'zipper': ['بدون', 'عادي', 'مخفي', 'جانبي'],
          'hand': ['عادية', 'ضيقة', 'واسعة', 'مطاطية'],
          'pocket': ['بدون', 'جيب واحد', 'جيبين', 'ثلاثة جيوب'],
          'style': ['عادي', 'رسمي', 'رياضي', 'عصري'],
          'button': ['بدون', 'زر عادي', 'زر معدني', 'زر خشبي'],
          'cuff': ['بدون', 'كبك عادي', 'كبك فرنسي', 'كبك مربع'],
        };
      });
    }
  }

  Future<void> _loadMeasurement() async {
    setState(() => _isLoading = true);
    try {
      final measurement = await DBHelper.getClothingMeasurementById(widget.measurementId!);
      if (measurement != null) {
        // ملء النموذج بالبيانات المسترجعة
        billController.text = measurement['billNumber'] ?? '';
        nameController.text = measurement['customerName'] ?? '';
        phoneController.text = measurement['phoneNumber'] ?? '';
        fabricController.text = measurement['fabricType'] ?? '';
        quantityController.text = measurement['quantity']?.toString() ?? '';
        dateController.text = measurement['receivedDate'] ?? '';
        priceController.text = measurement['price']?.toString() ?? '';
        paidController.text = measurement['paid']?.toString() ?? '';
        remainController.text = measurement['remaining']?.toString() ?? '';
        deliveryDateController.text = measurement['deliveryDate'] ?? '';
        noteController.text = measurement['notes'] ?? '';

        // ملء قيم المقاسات
        measurements['الطول']!.text = measurement['height'] ?? '';
        measurements['الكتف']!.text = measurement['shoulder'] ?? '';
        measurements['طول الكم']!.text = measurement['sleeveLength'] ?? '';
        measurements['وسع الصدر']!.text = measurement['chest'] ?? '';
        measurements['الرقبه']!.text = measurement['neck'] ?? '';
        measurements['تنزيله اليد']!.text = measurement['handDrop'] ?? '';
        measurements['وسط اليد']!.text = measurement['middleHand'] ?? '';
        measurements['طول الكبك']!.text = measurement['cuffLength'] ?? '';
        measurements['ارتفاع الكبك']!.text = measurement['cuffHeight'] ?? '';
        measurements['الخطوة']!.text = measurement['step'] ?? '';

        // ملء القوائم المنسدلة
        setState(() {
          selectedNeck = measurement['neckType'];
          selectedZipper = measurement['zipperType'];
          selectedHand = measurement['handType'];
          selectedPocket = measurement['pocketType'];
          selectedStyle = measurement['styleType'];
          selectedButtons = measurement['buttonType'];
          selectedCuff = measurement['cuffType'];
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('حدث خطأ أثناء تحميل البيانات: ${e.toString()}')),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _saveMeasurement() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);
    try {
      // حساب المتبقي إذا لم يكن مدخلاً
      if (priceController.text.isNotEmpty && paidController.text.isNotEmpty) {
        final price = NumberUtils.parseDouble(priceController.text);
        final paid = NumberUtils.parseDouble(paidController.text);
        remainController.text = NumberUtils.formatForInput(price - paid);
      }

      // جمع البيانات من النموذج
      final data = {
        'workshopId': widget.workshopId, // ربط المقاس بالمعمل
        'billNumber': billController.text,
        'customerName': nameController.text,
        'phoneNumber': phoneController.text,
        'fabricType': fabricController.text,
        'quantity': quantityController.text.isNotEmpty ? int.parse(quantityController.text) : null,
        'receivedDate': dateController.text,
        'price': priceController.text.isNotEmpty ? double.parse(priceController.text) : null,
        'paid': paidController.text.isNotEmpty ? double.parse(paidController.text) : null,
        'remaining': remainController.text.isNotEmpty ? double.parse(remainController.text) : null,
        'deliveryDate': deliveryDateController.text,
        'notes': noteController.text,

        // بيانات المقاسات
        'height': measurements['الطول']!.text,
        'shoulder': measurements['الكتف']!.text,
        'sleeveLength': measurements['طول الكم']!.text,
        'chest': measurements['وسع الصدر']!.text,
        'neck': measurements['الرقبه']!.text,
        'handDrop': measurements['تنزيله اليد']!.text,
        'middleHand': measurements['وسط اليد']!.text,
        'cuffLength': measurements['طول الكبك']!.text,
        'cuffHeight': measurements['ارتفاع الكبك']!.text,
        'step': measurements['الخطوة']!.text,

        // بيانات الخيارات
        'neckType': selectedNeck,
        'zipperType': selectedZipper,
        'handType': selectedHand,
        'pocketType': selectedPocket,
        'styleType': selectedStyle,
        'buttonType': selectedButtons,
        'cuffType': selectedCuff,

        // إضافة اسم اليوم
        'dayName': AppDateUtils.getCurrentDayName(),
        'createdAt': DateTime.now().toIso8601String(),
      };

      if (_isEdit) {
        await DBHelper.updateClothingMeasurement(widget.measurementId!, data);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.white),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(AppDateUtils.getMeasurementUpdatedMessage()),
                  ),
                ],
              ),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          );
        }
      } else {
        await DBHelper.insertClothingMeasurement(data);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.white),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(AppDateUtils.getMeasurementAddedMessage()),
                  ),
                ],
              ),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          );
        }
      }

      if (!mounted) return;
      Navigator.pop(context, true); // إرجاع true للإشارة إلى نجاح العملية
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('حدث خطأ أثناء الحفظ: ${e.toString()}')),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  // بناء قائمة منسدلة محسنة
  Widget buildDropdownField(
    String label,
    String? value,
    List<String> items,
    Function(String?) onChanged
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: DropdownButtonFormField<String>(
        value: value,
        decoration: MeasurementStyles.inputDecoration(label),
        style: const TextStyle(
          color: Colors.black87,
          fontSize: 16,
        ),
        icon: const Icon(Icons.arrow_drop_down, color: MeasurementStyles.primaryColor),
        isExpanded: true,
        alignment: AlignmentDirectional.center,
        items: items.map((String item) {
          return DropdownMenuItem<String>(
            value: item,
            child: Text(
              item,
              textAlign: TextAlign.center,
              textDirection: TextDirection.rtl,
            ),
          );
        }).toList(),
        onChanged: onChanged,
      ),
    );
  }

  // بناء صف مع ثلاثة حقول إدخال
  Widget buildTripleInput(String label1, String label2, String label3) {
    return Row(
      children: [
        Expanded(child: MeasurementStyles.buildInput(label1, measurements[label1]!)),
        const SizedBox(width: 10),
        Expanded(child: MeasurementStyles.buildInput(label2, measurements[label2]!)),
        const SizedBox(width: 10),
        Expanded(child: MeasurementStyles.buildInput(label3, measurements[label3]!)),
      ],
    );
  }

  // بناء صف مع حقلين
  Widget buildDoubleInput(
    String label1,
    String label2,
    TextEditingController controller1,
    TextEditingController controller2
  ) {
    return Row(
      children: [
        Expanded(child: MeasurementStyles.buildInput(label1, controller1)),
        const SizedBox(width: 10),
        Expanded(child: MeasurementStyles.buildInput(label2, controller2)),
      ],
    );
  }

  Widget _buildDetailDropdown(String label, String type, String? value, Function(String?) onChanged, IconData icon) {
    final options = _detailOptions[type] ?? [];

    return Row(
      children: [
        Expanded(
          child: MeasurementStyles.buildDropdown<String>(
            label: label,
            value: value,
            items: options,
            onChanged: onChanged,
            icon: icon,
          ),
        ),
        const SizedBox(width: 8),
        IconButton(
          onPressed: () => _openDetailOptionsScreen(),
          icon: Icon(Icons.settings, color: Colors.purple[600]),
          tooltip: 'إدارة الخيارات',
        ),
      ],
    );
  }

  Future<void> _openDetailOptionsScreen() async {
    await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => const MeasurementDetailOptionsScreen(),
      ),
    );

    // تحديث فوري للخيارات عند العودة
    await _loadDetailOptions();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: MeasurementStyles.backgroundColor,
      appBar: AppBar(
        title: Text(_isEdit ? "تعديل قياس ثوب" : "قياس ثوب جديد"),
        backgroundColor: MeasurementStyles.primaryColor,
        centerTitle: true,
      ),
      body: Directionality(
        textDirection: TextDirection.rtl,
        child: Form(
          key: _formKey,
          child: Stack(
            children: [
              ListView(
                padding: const EdgeInsets.all(16),
                children: [
                  MeasurementStyles.modernCard(
                    margin: const EdgeInsets.symmetric(vertical: 8),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                          // رقم الفاتورة المميز
                          MeasurementStyles.specialInvoiceField(billController),

                          // بيانات العميل
                          MeasurementStyles.sectionTitle("بيانات العميل", icon: Icons.person_rounded),
                          MeasurementStyles.buildInput("اسم العميل", nameController, icon: Icons.person_outline_rounded),
                          MeasurementStyles.buildDoubleInput(
                            "رقم الهاتف", "الكمية",
                            phoneController, quantityController,
                            icon1: Icons.phone_rounded, icon2: Icons.numbers_rounded
                          ),
                          MeasurementStyles.buildDoubleInput(
                            "تاريخ الاستلام", "نوع القماش",
                            dateController, fabricController,
                            icon1: Icons.calendar_today_rounded, icon2: Icons.texture_rounded
                          ),
                          MeasurementStyles.buildDoubleInput(
                            "المبلغ", "المدفوع",
                            priceController, paidController,
                            icon1: Icons.attach_money_rounded, icon2: Icons.payment_rounded
                          ),
                          MeasurementStyles.buildInput("المتبقي", remainController, icon: Icons.account_balance_wallet_rounded),

                          // القياسات
                          MeasurementStyles.sectionTitle("القياسات", icon: Icons.straighten_rounded),
                          buildTripleInput('الطول', 'الكتف', 'طول الكم'),
                          const SizedBox(height: 10),
                          buildTripleInput('وسع الصدر', 'الرقبه', 'تنزيله اليد'),
                          const SizedBox(height: 10),
                          buildTripleInput('وسط اليد', 'طول الكبك', 'ارتفاع الكبك'),
                          const SizedBox(height: 10),
                          MeasurementStyles.buildInput('الخطوة', measurements['الخطوة']!, icon: Icons.height_rounded),

                          // الخيارات
                          MeasurementStyles.sectionTitle("خيارات التفصيل", icon: Icons.tune_rounded),
                          Row(
                            children: [
                              Expanded(
                                child: _buildDetailDropdown('الرقبة', 'neck', selectedNeck, (value) => setState(() => selectedNeck = value), Icons.accessibility_new_rounded),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: _buildDetailDropdown('الجبزور', 'zipper', selectedZipper, (value) => setState(() => selectedZipper = value), Icons.vertical_align_center_rounded),
                              ),
                            ],
                          ),
                          const SizedBox(height: 10),
                          Row(
                            children: [
                              Expanded(
                                child: _buildDetailDropdown('اليد', 'hand', selectedHand, (value) => setState(() => selectedHand = value), Icons.back_hand_rounded),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: _buildDetailDropdown('الجيوب', 'pocket', selectedPocket, (value) => setState(() => selectedPocket = value), Icons.inventory_2_rounded),
                              ),
                            ],
                          ),
                          const SizedBox(height: 10),
                          Row(
                            children: [
                              Expanded(
                                child: _buildDetailDropdown('نوع التفصيل', 'style', selectedStyle, (value) => setState(() => selectedStyle = value), Icons.style_rounded),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: _buildDetailDropdown('الزرارات', 'button', selectedButtons, (value) => setState(() => selectedButtons = value), Icons.radio_button_checked_rounded),
                              ),
                            ],
                          ),
                          const SizedBox(height: 10),
                          Row(
                            children: [
                              Expanded(
                                child: _buildDetailDropdown('نوع الكبك', 'cuff', selectedCuff, (value) => setState(() => selectedCuff = value), Icons.watch),
                              ),
                              const SizedBox(width: 16),
                              const Expanded(child: SizedBox()), // مساحة فارغة
                            ],
                          ),

                          // الملاحظات
                          MeasurementStyles.sectionTitle("ملاحظات", icon: Icons.note_add_rounded),
                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: 8),
                            child: TextFormField(
                              controller: noteController,
                              maxLines: 3,
                              textAlign: TextAlign.right,
                              textDirection: TextDirection.rtl,
                              keyboardType: TextInputType.text, // لوحة مفاتيح نصوص للملاحظات
                              style: MeasurementStyles.normalTextStyle,
                              decoration: MeasurementStyles.inputDecoration("ملاحظة", icon: Icons.edit_note_rounded),
                            ),
                          ),
                          MeasurementStyles.buildInput("موعد التسليم", deliveryDateController, icon: Icons.event_available_rounded),

                          const SizedBox(height: 20),
                          MeasurementStyles.saveButton(
                            _isEdit ? "تحديث القياس" : "حفظ القياس",
                            _saveMeasurement,
                            isLoading: _isLoading
                          ),
                      ],
                    ),
                  ),
                ],
              ),
              if (_isLoading)
                Container(
                  color: Colors.black.withOpacity(0.3),
                  child: const Center(
                    child: CircularProgressIndicator(),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
