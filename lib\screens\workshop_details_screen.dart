
import 'package:flutter/material.dart';
import 'package:databasflutter/models/db_helper.dart';
import 'package:databasflutter/screens/add_work_screen.dart';
import 'package:databasflutter/screens/workshop_summary_screen.dart';

import 'add_invoice_screen.dart';
import 'financial/financial_management_screen.dart';
import 'work_info_screen.dart';
import 'workshop_measurements_screen.dart';
import '../widgets/export_print_buttons.dart';

class WorkshopDetailsScreen extends StatefulWidget {
  final Map<String, dynamic> workshop;
  const WorkshopDetailsScreen({required this.workshop, super.key});

  @override
  State<WorkshopDetailsScreen> createState() => _WorkshopDetailsScreenState();
}

class _WorkshopDetailsScreenState extends State<WorkshopDetailsScreen> with RouteAware {
  List<Map<String, dynamic>> _works = [];
  Map<String, dynamic>? _currentWorkshop;
  bool _isLoading = true;
  static final RouteObserver<PageRoute> routeObserver = RouteObserver<PageRoute>();

  @override
  void initState() {
    super.initState();
    _loadWorks();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    routeObserver.subscribe(this, ModalRoute.of(context)! as PageRoute);
  }

  @override
  void dispose() {
    routeObserver.unsubscribe(this);
    super.dispose();
  }

  @override
  void didPopNext() {
    // يتم استدعاء هذه الدالة عند العودة إلى هذه الشاشة
    super.didPopNext();
    _loadWorks(); // حدث البيانات
  }

  // دالة لتحديث البيانات عند العودة إلى الشاشة
  void refreshData() {
    _loadWorks();
  }

  void _showEditWorkshopDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text("تعديل بيانات المعمل"),
          content: const Text("سيتم تنفيذ مربع حوار التعديل لاحقًا"),
          actions: [
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () {},
            ),
          ],
        );
      },
    );
  }

  void _loadWorks() async {
    // تحديث بيانات المعمل من قاعدة البيانات
    final updatedWorkshop = await DBHelper.getWorkshopById(widget.workshop['id']);
    if (updatedWorkshop != null) {
      _currentWorkshop = updatedWorkshop;
    }

    final works = await DBHelper.getWorksForWorkshop(widget.workshop['id']);
    if (mounted) {
      setState(() {
        _works = works;
        _isLoading = false;
      });
    }
  }

  Future<void> _deleteWork(int workId) async {
    await DBHelper.deleteWork(workId);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم حذف العمل')),
    );
    _loadWorks();
  }

  void _confirmDelete(int workId) {
    showDialog(
      context: context,
      builder: (ctx) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل أنت متأكد من حذف هذا العمل؟'),
        actions: [
          TextButton(
              onPressed: () => Navigator.of(ctx).pop(),
              child: const Text('إلغاء')),
          TextButton(
            onPressed: () {
              Navigator.of(ctx).pop();
              _deleteWork(workId);
            },
            child: const Text('حذف', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _navigateToEditWork(Map<String, dynamic> work) async {
    final isQassas = widget.workshop['isQassas'] == 1;
    final hasOtherTypes =
        (widget.workshop['otherTypeName'] as String? ?? '').isNotEmpty;

    await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => AddWorkScreen(
          workshopId: widget.workshop['id'],
          isQassas: isQassas,
          hasOtherTypes: hasOtherTypes,
          existingWork: work,
        ),
      ),
    );
    _loadWorks();
  }

  @override
  Widget build(BuildContext context) {
    final workshop = widget.workshop;
    final isQassas = workshop['isQassas'] == 1;
    final hasOtherTypes =
        (workshop['otherTypeName'] as String? ?? '').isNotEmpty;
    final otherTypeName = workshop['otherTypeName'] ?? '';

    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: const Color(0xFFF5F5F5),
        appBar: AppBar(
          title: Text(
            'تفاصيل المعمل: ${workshop['name']}',
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          backgroundColor: Colors.teal.shade700,
          actions: [
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () => _showEditWorkshopDialog(),
            ),
          ],
        ),
        body: SafeArea(
          child: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _works.isEmpty
                  ? const Center(
                      child: Text('لا توجد أعمال بعد',
                          style: TextStyle(color: Colors.grey)))
                  : ListView.separated(
                      padding: const EdgeInsets.only(
                          top: 12, left: 12, right: 12, bottom: 70), // مسافة مناسبة لشريط التنقل الجديد
                      itemCount: _works.length,
                      itemBuilder: (ctx, i) {
                        final w = _works[i];
                        final price = (w['pricePerPiece'] as num).toDouble();
                        final quantity = (w['quantity'] as num).toInt();
                        final total = price * quantity;
                        final otherCount =
                            (w['otherCount'] as num?)?.toInt() ?? 0;
                        final expense = (w['expense'] as num).toDouble();
                        final notes = w['notes'] as String? ?? 'لا توجد ملاحظة';
                        return WorkItem(
                          work: w,
                          price: price,
                          quantity: quantity,
                          total: total,
                          isQassas: isQassas,
                          cutQuantity: (w['cutQuantity'] as num?)?.toInt() ?? 0,
                          hasOtherTypes: hasOtherTypes,
                          otherTypeName: otherTypeName,
                          otherCount: otherCount,
                          expense: expense,
                          notes: notes,
                          navigateToEditWork: _navigateToEditWork,
                          confirmDelete: _confirmDelete,
                        );
                      },
                      separatorBuilder: (BuildContext context, int index) {
                        return const SizedBox(height: 8);
                      },
                    ),
        ),

        // شريط التنقل السفلي مع زر إضافة عمل + 4 أيقونات
        bottomNavigationBar: Container(
          height: 140, // ارتفاع أكبر ليتسع للزر والأيقونات
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(25),
              topRight: Radius.circular(25),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 20,
                offset: const Offset(0, -5),
                spreadRadius: 0,
              ),
            ],
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // زر إضافة عمل جديد في الأعلى
                  Container(
                    width: double.infinity,
                    margin: const EdgeInsets.only(bottom: 8),
                    child: ElevatedButton.icon(
                      onPressed: () async {
                        await Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (_) => AddWorkScreen(
                              workshopId: workshop['id'],
                              isQassas: isQassas,
                              hasOtherTypes: hasOtherTypes,
                            ),
                          ),
                        );
                        _loadWorks();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF10B981),
                        foregroundColor: Colors.white,
                        elevation: 4,
                        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                      ),
                      icon: const Icon(Icons.add_circle_outline, size: 20),
                      label: const Text(
                        'إضافة عمل جديد',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w700,
                          letterSpacing: 0.5,
                        ),
                      ),
                    ),
                  ),

                  // الأزرار الأربعة في الأسفل
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildBottomNavButton(
                        icon: Icons.analytics_outlined,
                        label: 'إحصائيات',
                        color: const Color(0xFFEC4899),
                        onPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (_) => WorkshopSummaryScreen(
                                workshopId: workshop['id'],
                                workshopName: workshop['name'],
                              ),
                            ),
                          );
                        },
                      ),
                      _buildBottomNavButton(
                        icon: Icons.straighten_outlined,
                        label: 'مقاسات',
                        color: const Color(0xFF8B5CF6),
                        onPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (_) => WorkshopMeasurementsScreen(workshop: workshop),
                            ),
                          );
                        },
                      ),
                      _buildBottomNavButton(
                        icon: Icons.receipt_long_outlined,
                        label: 'فواتير',
                        color: const Color(0xFFEF4444),
                        onPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (_) => AddInvoiceScreen(workshop: workshop),
                            ),
                          );
                        },
                      ),
                      _buildBottomNavButton(
                        icon: Icons.account_balance_wallet_outlined,
                        label: 'مالي',
                        color: const Color(0xFF3B82F6),
                        onPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (_) => FinancialManagementScreen(
                                tailorId: workshop['id'],
                                tailorName: workshop['name'],
                              ),
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // دالة لبناء أزرار شريط التنقل السفلي
  Widget _buildBottomNavButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return Expanded(
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: onPressed,
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 2),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: 20,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  label,
                  style: TextStyle(
                    color: color,
                    fontSize: 9,
                    fontWeight: FontWeight.w600,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }


}

class WorkItem extends StatelessWidget {
  final Map<String, dynamic> work;
  final double price;
  final int quantity;
  final double total;
  final bool isQassas;
  final int cutQuantity;
  final bool hasOtherTypes;
  final String otherTypeName;
  final int otherCount;
  final double expense;
  final String notes;
  final Function(Map<String, dynamic>) navigateToEditWork;
  final Function(int) confirmDelete;

  const WorkItem({
    super.key,
    required this.work,
    required this.price,
    required this.quantity,
    required this.total,
    required this.isQassas,
    required this.cutQuantity,
    required this.hasOtherTypes,
    required this.otherTypeName,
    required this.otherCount,
    required this.expense,
    required this.notes,
    required this.navigateToEditWork,
    required this.confirmDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (_) => WorkInfoScreen(work: work),
            ),
          );
        },
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'نوع الشغل: ${work['workType']}',
                    style: const TextStyle(
                        fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  PopupMenuButton<String>(
                    onSelected: (v) {
                      if (v == 'edit') {
                        navigateToEditWork(work);
                      } else if (v == 'delete') confirmDelete(work['id']);
                    },
                    itemBuilder: (_) => [
                      const PopupMenuItem(value: 'edit', child: Text('تعديل')),
                      const PopupMenuItem(value: 'delete', child: Text('حذف')),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 6),
              Text(
                'السعر: ${price.toStringAsFixed(0)} × $quantity = ${total.toStringAsFixed(0)}',
                style: const TextStyle(fontSize: 14),
              ),
              if (isQassas && cutQuantity > 0)
                Text(
                  'عدد المقصوص: $cutQuantity',
                  style: const TextStyle(fontSize: 14),
                ),
              if (hasOtherTypes && otherCount > 0)
                Text(
                  'عدد $otherTypeName: $otherCount',
                  style: const TextStyle(fontSize: 14),
                ),
              const SizedBox(height: 4),
              RichText(
                text: TextSpan(
                  children: [
                    TextSpan(
                      text: 'المصروف: ',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Colors.teal.shade700,
                      ),
                    ),
                    TextSpan(
                      text: expense.toStringAsFixed(0),
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Colors.deepOrange,
                      ),
                    ),
                    TextSpan(
                      text: ' ر.س',
                      style: TextStyle(
                        fontSize: 13,
                        color: Colors.deepOrange.shade200,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 4),
              Text.rich(
                TextSpan(
                  text: 'التاريخ: ',
                  style: TextStyle(fontSize: 12, color: Colors.grey[700]),
                  children: [
                    TextSpan(
                      text: work['date'],
                      style: TextStyle(
                          fontSize: 12,
                          fontStyle: FontStyle.italic,
                          color: Colors.grey[900]),
                    ),
                  ],
                ),
              ),
              if (work['dayName'] != null) ...[
                const SizedBox(height: 4),
                Text.rich(
                  TextSpan(
                    text: 'اليوم: ',
                    style: TextStyle(fontSize: 12, color: Colors.grey[700]),
                    children: [
                      TextSpan(
                        text: work['dayName'],
                        style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            color: Colors.teal[600]),
                      ),
                    ],
                  ),
                ),
              ],
              const SizedBox(height: 4),
              Text.rich(
                TextSpan(
                  text: 'الملاحظة: ',
                  style: TextStyle(fontSize: 12, color: Colors.grey[700]),
                  children: [
                    TextSpan(
                        text: notes,
                        style: TextStyle(
                            fontSize: 12,
                            fontStyle: FontStyle.italic,
                            color: Colors.grey[800])),
                  ],
                ),
              ),

              const SizedBox(height: 12),

              // أزرار التصدير والطباعة
              ExportPrintButtons(
                data: work,
                type: 'works',
                title: 'عمل ${work['workType']} - ${work['date']}',
                showIndividual: true,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
