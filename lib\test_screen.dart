import 'package:flutter/material.dart';
import 'utils/date_utils.dart';

class TestScreen extends StatelessWidget {
  const TestScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار أسماء الأيام'),
        backgroundColor: Colors.teal,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'اليوم الحالي: ${DateTime.now().weekday}',
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 10),
            Text(
              'اسم اليوم: ${AppDateUtils.getCurrentDayName()}',
              style: const TextStyle(fontSize: 18, color: Colors.blue),
            ),
            const SizedBox(height: 20),
            const Text(
              'اختبار الرسائل:',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 10),
            _buildMessageTest('رسالة العمل', AppDateUtils.getWorkAddedMessage()),
            _buildMessageTest('رسالة المعمل', AppDateUtils.getWorkshopAddedMessage()),
            _buildMessageTest('رسالة الفاتورة', AppDateUtils.getInvoiceAddedMessage()),
            _buildMessageTest('رسالة المقاس', AppDateUtils.getMeasurementAddedMessage()),
            _buildMessageTest('رسالة الخياط', AppDateUtils.getTailorAddedMessage()),
            const SizedBox(height: 20),
            const Text(
              'اختبار جميع الأيام:',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 10),
            ...List.generate(7, (index) {
              final testDate = DateTime(2024, 1, index + 1);
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 2),
                child: Text(
                  'اليوم ${index + 1} (${testDate.weekday}): ${AppDateUtils.getDayName(testDate)}',
                  style: const TextStyle(fontSize: 16),
                ),
              );
            }),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Row(
                      children: [
                        const Icon(Icons.check_circle, color: Colors.white),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(AppDateUtils.getWorkAddedMessage()),
                        ),
                      ],
                    ),
                    backgroundColor: Colors.green,
                    behavior: SnackBarBehavior.floating,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                );
              },
              child: const Text('اختبار رسالة النجاح'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMessageTest(String label, String message) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(
              message,
              style: const TextStyle(color: Colors.green),
            ),
          ),
        ],
      ),
    );
  }
}
