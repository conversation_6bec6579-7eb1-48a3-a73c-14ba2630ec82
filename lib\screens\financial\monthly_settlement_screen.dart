// lib/screens/financial/monthly_settlement_screen.dart

import 'package:flutter/material.dart';
import '../../models/db_helper.dart';
import '../../models/financial_models.dart';
import '../../measurements/measurement_styles.dart';
import '../../utils/number_utils.dart';

class MonthlySettlementScreen extends StatefulWidget {
  final int tailorId;
  final String tailorName;

  const MonthlySettlementScreen({
    super.key,
    required this.tailorId,
    required this.tailorName,
  });

  @override
  State<MonthlySettlementScreen> createState() => _MonthlySettlementScreenState();
}

class _MonthlySettlementScreenState extends State<MonthlySettlementScreen> {
  List<MonthlySettlement> _settlements = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadSettlements();
  }

  Future<void> _loadSettlements() async {
    setState(() => _isLoading = true);

    try {
      final settlementsData = await DBHelper.getMonthlySettlementsByTailorId(widget.tailorId);
      _settlements = settlementsData.map((data) => MonthlySettlement.fromMap(data)).toList();
    } catch (e) {
      _showErrorSnackBar('خطأ في تحميل البيانات: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _createNewSettlement() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CreateSettlementScreen(
          tailorId: widget.tailorId,
          tailorName: widget.tailorName,
        ),
      ),
    );

    if (result == true) {
      _loadSettlements();
    }
  }

  Future<void> _viewSettlement(MonthlySettlement settlement) async {
    await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => SettlementDetailsScreen(
          settlement: settlement,
          tailorName: widget.tailorName,
        ),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: MeasurementStyles.errorColor,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: MeasurementStyles.backgroundColor,
      appBar: AppBar(
        title: Text(
          'التصفية الشهرية - ${widget.tailorName}',
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 18,
          ),
        ),
        backgroundColor: MeasurementStyles.primaryColor,
        foregroundColor: MeasurementStyles.whiteColor,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _createNewSettlement,
            icon: const Icon(Icons.add_rounded),
            tooltip: 'تصفية جديدة',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _settlements.isEmpty
              ? _buildEmptyState()
              : ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: _settlements.length,
                  itemBuilder: (context, index) {
                    return _buildSettlementCard(_settlements[index]);
                  },
                ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _createNewSettlement,
        backgroundColor: MeasurementStyles.primaryColor,
        foregroundColor: MeasurementStyles.whiteColor,
        icon: const Icon(Icons.calculate_rounded),
        label: const Text('تصفية جديدة'),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.calculate_outlined,
            size: 80,
            color: MeasurementStyles.textSecondaryColor.withOpacity(0.5),
          ),
          const SizedBox(height: 16),
          const Text(
            'لا توجد تصفيات شهرية',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: MeasurementStyles.textSecondaryColor,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'لم يتم إجراء أي تصفية شهرية لهذا الخياط',
            style: MeasurementStyles.cardSubtitleStyle,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSettlementCard(MonthlySettlement settlement) {
    return MeasurementStyles.modernCard(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () => _viewSettlement(settlement),
        borderRadius: BorderRadius.circular(20),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // رأس البطاقة
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: settlement.isPositive
                          ? MeasurementStyles.successColor.withOpacity(0.1)
                          : MeasurementStyles.errorColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      Icons.calculate_rounded,
                      color: settlement.isPositive
                          ? MeasurementStyles.successColor
                          : MeasurementStyles.errorColor,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          settlement.formattedMonthYear,
                          style: MeasurementStyles.cardTitleStyle,
                        ),
                        Text(
                          'تصفية ${settlement.formattedSettlementDate}',
                          style: MeasurementStyles.cardSubtitleStyle,
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: settlement.isPositive
                          ? MeasurementStyles.successColor
                          : MeasurementStyles.errorColor,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${settlement.finalBalance.abs().toStringAsFixed(2)} ريال',
                      style: const TextStyle(
                        color: MeasurementStyles.whiteColor,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // ملخص التصفية
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: MeasurementStyles.secondaryColor,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  children: [
                    _buildSummaryRow(
                      'الدخل',
                      settlement.totalIncome,
                      MeasurementStyles.successColor,
                    ),
                    const SizedBox(height: 8),
                    _buildSummaryRow(
                      'المصروفات',
                      settlement.totalExpenses,
                      MeasurementStyles.errorColor,
                    ),
                    const SizedBox(height: 8),
                    _buildSummaryRow(
                      'الغرامات',
                      settlement.totalPenalties,
                      MeasurementStyles.warningColor,
                    ),
                    const SizedBox(height: 8),
                    _buildSummaryRow(
                      'الأعمال الإضافية',
                      settlement.totalExtraWork,
                      MeasurementStyles.primaryColor,
                    ),
                    const Divider(height: 20),
                    _buildSummaryRow(
                      'الرصيد النهائي',
                      settlement.finalBalance,
                      settlement.isPositive
                          ? MeasurementStyles.successColor
                          : MeasurementStyles.errorColor,
                      isBold: true,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSummaryRow(String title, double amount, Color color, {bool isBold = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 14,
            fontWeight: isBold ? FontWeight.w600 : FontWeight.w400,
            color: MeasurementStyles.textColor,
          ),
        ),
        Text(
          '${NumberUtils.formatIntegerDisplay(amount)} ريال',
          style: TextStyle(
            fontSize: 14,
            fontWeight: isBold ? FontWeight.w700 : FontWeight.w500,
            color: color,
          ),
        ),
      ],
    );
  }
}

// شاشة إنشاء تصفية جديدة
class CreateSettlementScreen extends StatefulWidget {
  final int tailorId;
  final String tailorName;

  const CreateSettlementScreen({
    super.key,
    required this.tailorId,
    required this.tailorName,
  });

  @override
  State<CreateSettlementScreen> createState() => _CreateSettlementScreenState();
}

class _CreateSettlementScreenState extends State<CreateSettlementScreen> {
  final _formKey = GlobalKey<FormState>();
  final _notesController = TextEditingController();

  int _selectedMonth = DateTime.now().month;
  int _selectedYear = DateTime.now().year;
  bool _isLoading = false;
  bool _isCalculating = false;

  double _totalIncome = 0.0;
  double _totalExpenses = 0.0;
  double _totalPenalties = 0.0;
  double _totalExtraWork = 0.0;
  double _previousBalance = 0.0;
  double _finalBalance = 0.0;

  @override
  void initState() {
    super.initState();
    _calculateSettlement();
  }

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _calculateSettlement() async {
    setState(() => _isCalculating = true);

    try {
      // حساب الدخل والمصروفات من جدول الأعمال
      _totalIncome = await DBHelper.getTotalIncomeForMonth(widget.tailorId, _selectedMonth, _selectedYear);
      _totalExpenses = await DBHelper.getTotalExpensesForMonth(widget.tailorId, _selectedMonth, _selectedYear);

      // حساب الغرامات والأعمال الإضافية
      _totalPenalties = await DBHelper.getTotalPenaltiesForMonth(widget.tailorId, _selectedMonth, _selectedYear);
      _totalExtraWork = await DBHelper.getTotalExtraWorkForMonth(widget.tailorId, _selectedMonth, _selectedYear);

      // حساب الرصيد السابق
      await _calculatePreviousBalance();

      // حساب الرصيد النهائي
      _finalBalance = _previousBalance + _totalIncome - _totalExpenses - _totalPenalties + _totalExtraWork;

    } catch (e) {
      _showErrorSnackBar('خطأ في حساب التصفية: $e');
    } finally {
      setState(() => _isCalculating = false);
    }
  }

  Future<void> _calculatePreviousBalance() async {
    // البحث عن آخر تصفية شهرية
    final previousMonth = _selectedMonth == 1 ? 12 : _selectedMonth - 1;
    final previousYear = _selectedMonth == 1 ? _selectedYear - 1 : _selectedYear;

    final previousSettlement = await DBHelper.getMonthlySettlement(widget.tailorId, previousMonth, previousYear);

    if (previousSettlement != null) {
      _previousBalance = (previousSettlement['finalBalance'] as num).toDouble();
    } else {
      // إذا لم توجد تصفية سابقة، استخدم الرصيد المرحل من الشهر الأول
      final initialBalance = await DBHelper.getInitialBalanceByTailorId(widget.tailorId);
      if (initialBalance != null) {
        final balance = InitialBalance.fromMap(initialBalance);
        _previousBalance = balance.netBalance;
      } else {
        _previousBalance = 0.0;
      }
    }
  }

  Future<void> _saveSettlement() async {
    if (!_formKey.currentState!.validate()) return;

    // التحقق من عدم وجود تصفية لنفس الشهر
    final existingSettlement = await DBHelper.getMonthlySettlement(widget.tailorId, _selectedMonth, _selectedYear);
    if (existingSettlement != null) {
      _showErrorSnackBar('يوجد تصفية لهذا الشهر مسبقاً');
      return;
    }

    setState(() => _isLoading = true);

    try {
      final settlementData = {
        'tailorId': widget.tailorId,
        'month': _selectedMonth,
        'year': _selectedYear,
        'totalIncome': _totalIncome,
        'totalExpenses': _totalExpenses,
        'totalPenalties': _totalPenalties,
        'totalExtraWork': _totalExtraWork,
        'previousBalance': _previousBalance,
        'finalBalance': _finalBalance,
        'settlementDate': DateTime.now().toIso8601String(),
        'notes': _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
      };

      await DBHelper.insertMonthlySettlement(settlementData);
      _showSuccessSnackBar('تم حفظ التصفية الشهرية بنجاح');
      Navigator.pop(context, true);
    } catch (e) {
      _showErrorSnackBar('خطأ في حفظ التصفية: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: MeasurementStyles.successColor,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: MeasurementStyles.errorColor,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: MeasurementStyles.backgroundColor,
      appBar: AppBar(
        title: Text(
          'تصفية جديدة - ${widget.tailorName}',
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 18,
          ),
        ),
        backgroundColor: MeasurementStyles.primaryColor,
        foregroundColor: MeasurementStyles.whiteColor,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              // اختيار الشهر والسنة
              MeasurementStyles.modernCard(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    MeasurementStyles.sectionTitle(
                      'اختيار الشهر والسنة',
                      icon: Icons.calendar_month_rounded,
                    ),

                    const SizedBox(height: 16),

                    Row(
                      children: [
                        Expanded(
                          child: MeasurementStyles.buildDropdown<int>(
                            label: 'الشهر',
                            value: _selectedMonth,
                            items: List.generate(12, (index) => index + 1),
                            onChanged: (value) {
                              setState(() => _selectedMonth = value!);
                              _calculateSettlement();
                            },
                            icon: Icons.calendar_today_rounded,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: MeasurementStyles.buildDropdown<int>(
                            label: 'السنة',
                            value: _selectedYear,
                            items: List.generate(5, (index) => DateTime.now().year - 2 + index),
                            onChanged: (value) {
                              setState(() => _selectedYear = value!);
                              _calculateSettlement();
                            },
                            icon: Icons.date_range_rounded,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // نتائج الحساب
              MeasurementStyles.modernCard(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    MeasurementStyles.sectionTitle(
                      'نتائج التصفية',
                      icon: Icons.calculate_rounded,
                    ),

                    const SizedBox(height: 16),

                    if (_isCalculating)
                      const Center(child: CircularProgressIndicator())
                    else
                      _buildCalculationResults(),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // ملاحظات
              MeasurementStyles.modernCard(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    MeasurementStyles.sectionTitle(
                      'ملاحظات (اختيارية)',
                      icon: Icons.note_add_rounded,
                    ),

                    const SizedBox(height: 16),

                    TextFormField(
                      controller: _notesController,
                      maxLines: 3,
                      keyboardType: TextInputType.text,
                      textAlign: TextAlign.right,
                      style: MeasurementStyles.normalTextStyle,
                      decoration: MeasurementStyles.inputDecoration(
                        'ملاحظات على التصفية',
                        icon: Icons.edit_note_rounded,
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 20),

              // زر الحفظ
              MeasurementStyles.saveButton(
                'حفظ التصفية',
                _saveSettlement,
                isLoading: _isLoading,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCalculationResults() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: MeasurementStyles.secondaryColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          _buildResultRow('الرصيد السابق', _previousBalance, MeasurementStyles.primaryColor),
          const SizedBox(height: 12),
          _buildResultRow('إجمالي الدخل', _totalIncome, MeasurementStyles.successColor),
          const SizedBox(height: 12),
          _buildResultRow('إجمالي المصروفات', _totalExpenses, MeasurementStyles.errorColor),
          const SizedBox(height: 12),
          _buildResultRow('إجمالي الغرامات', _totalPenalties, MeasurementStyles.warningColor),
          const SizedBox(height: 12),
          _buildResultRow('إجمالي الأعمال الإضافية', _totalExtraWork, MeasurementStyles.primaryLightColor),

          const Divider(height: 24),

          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: _finalBalance >= 0
                  ? MeasurementStyles.successColor.withOpacity(0.1)
                  : MeasurementStyles.errorColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'الرصيد النهائي',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w700,
                    color: MeasurementStyles.textColor,
                  ),
                ),
                Text(
                  '${_finalBalance.toStringAsFixed(2)} ريال',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w700,
                    color: _finalBalance >= 0
                        ? MeasurementStyles.successColor
                        : MeasurementStyles.errorColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResultRow(String title, double amount, Color color) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: MeasurementStyles.textColor,
          ),
        ),
        Text(
          '${NumberUtils.formatIntegerDisplay(amount)} ريال',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: color,
          ),
        ),
      ],
    );
  }
}

// شاشة تفاصيل التصفية
class SettlementDetailsScreen extends StatelessWidget {
  final MonthlySettlement settlement;
  final String tailorName;

  const SettlementDetailsScreen({
    super.key,
    required this.settlement,
    required this.tailorName,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: MeasurementStyles.backgroundColor,
      appBar: AppBar(
        title: Text(
          'تفاصيل التصفية - $tailorName',
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 18,
          ),
        ),
        backgroundColor: MeasurementStyles.primaryColor,
        foregroundColor: MeasurementStyles.whiteColor,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // معلومات التصفية
            MeasurementStyles.modernCard(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  MeasurementStyles.sectionTitle(
                    'معلومات التصفية',
                    icon: Icons.info_outline_rounded,
                  ),

                  const SizedBox(height: 16),

                  _buildInfoRow('الشهر والسنة', settlement.formattedMonthYear),
                  const SizedBox(height: 12),
                  _buildInfoRow('تاريخ التصفية', settlement.formattedSettlementDate),
                  const SizedBox(height: 12),
                  _buildInfoRow('الحالة', settlement.isPositive ? 'رصيد موجب' : 'رصيد سالب'),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // تفاصيل الحساب
            MeasurementStyles.modernCard(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  MeasurementStyles.sectionTitle(
                    'تفاصيل الحساب',
                    icon: Icons.calculate_rounded,
                  ),

                  const SizedBox(height: 16),

                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: MeasurementStyles.secondaryColor,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      children: [
                        _buildDetailRow('الرصيد السابق', settlement.previousBalance, MeasurementStyles.primaryColor),
                        const SizedBox(height: 12),
                        _buildDetailRow('إجمالي الدخل', settlement.totalIncome, MeasurementStyles.successColor),
                        const SizedBox(height: 12),
                        _buildDetailRow('إجمالي المصروفات', settlement.totalExpenses, MeasurementStyles.errorColor),
                        const SizedBox(height: 12),
                        _buildDetailRow('إجمالي الغرامات', settlement.totalPenalties, MeasurementStyles.warningColor),
                        const SizedBox(height: 12),
                        _buildDetailRow('إجمالي الأعمال الإضافية', settlement.totalExtraWork, MeasurementStyles.primaryLightColor),

                        const Divider(height: 24),

                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: settlement.isPositive
                                ? MeasurementStyles.successColor.withOpacity(0.1)
                                : MeasurementStyles.errorColor.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              const Text(
                                'الرصيد النهائي',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w700,
                                  color: MeasurementStyles.textColor,
                                ),
                              ),
                              Text(
                                '${settlement.finalBalance.toStringAsFixed(2)} ريال',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w700,
                                  color: settlement.isPositive
                                      ? MeasurementStyles.successColor
                                      : MeasurementStyles.errorColor,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            // الملاحظات إن وجدت
            if (settlement.notes != null && settlement.notes!.isNotEmpty) ...[
              const SizedBox(height: 16),
              MeasurementStyles.modernCard(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    MeasurementStyles.sectionTitle(
                      'الملاحظات',
                      icon: Icons.note_rounded,
                    ),

                    const SizedBox(height: 16),

                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: MeasurementStyles.secondaryColor,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        settlement.notes!,
                        style: MeasurementStyles.normalTextStyle,
                        textAlign: TextAlign.right,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String title, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          title,
          style: MeasurementStyles.normalTextStyle,
        ),
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: MeasurementStyles.primaryColor,
          ),
        ),
      ],
    );
  }

  Widget _buildDetailRow(String title, double amount, Color color) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: MeasurementStyles.textColor,
          ),
        ),
        Text(
          '${NumberUtils.formatIntegerDisplay(amount)} ريال',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: color,
          ),
        ),
      ],
    );
  }
}
