// class WorkModel {
//   final int? id;           // المعرف المحلي في SQLite
//   final int workshopId;    // معرف المعمل المرتبط به
//   final String workType;   // نوع العمل
//   final double pricePerPiece;
//   final int quantity;
//   final int cutQuantity;
//   final double expense;
//   final DateTime date;

//   WorkModel({
//     this.id,
//     required this.workshopId,
//     required this.workType,
//     required this.pricePerPiece,
//     required this.quantity,
//     this.cutQuantity = 0,
//     required this.expense,
//     required this.date,
//   });

//   /// تحويل من خريطة (Map) إلى كائن WorkModel
//   factory WorkModel.fromMap(Map<String, dynamic> map) {
//     return WorkModel(
//       id: map['id'] as int?,
//       workshopId: map['workshopId'] as int,
//       workType: map['workType'] as String,
//       pricePerPiece: (map['pricePerPiece'] as num).toDouble(),
//       quantity: map['quantity'] as int,
//       cutQuantity: map['cutQuantity'] != null
//           ? (map['cutQuantity'] as num).toInt()
//           : 0,
//       expense: (map['expense'] as num).toDouble(),
//       date: DateTime.parse(map['date'] as String),
//     );
//   }

//   /// تحويل من كائن WorkModel إلى خريطة لحفظها في SQLite أو Firebase
//   Map<String, dynamic> toMap() {
//     return {
//       'id': id,
//       'workshopId': workshopId,
//       'workType': workType,
//       'pricePerPiece': pricePerPiece,
//       'quantity': quantity,
//       'cutQuantity': cutQuantity,
//       'expense': expense,
//       'date': date.toIso8601String(),
//     };
//   }

//   /// إنشاء نسخة معدّلة من الكائن (مفيد للتحديث)
//   WorkModel copyWith({
//     int? id,
//     int? workshopId,
//     String? workType,
//     double? pricePerPiece,
//     int? quantity,
//     int? cutQuantity,
//     double? expense,
//     DateTime? date,
//   }) {
//     return WorkModel(
//       id: id ?? this.id,
//       workshopId: workshopId ?? this.workshopId,
//       workType: workType ?? this.workType,
//       pricePerPiece: pricePerPiece ?? this.pricePerPiece,
//       quantity: quantity ?? this.quantity,
//       cutQuantity: cutQuantity ?? this.cutQuantity,
//       expense: expense ?? this.expense,
//       date: date ?? this.date,
//     );
//   }
// }

// class WorkModel {
//   final int? id;           // المعرف المحلي في SQLite
//   final int workshopId;    // معرف المعمل المرتبط به
//   final String workType;   // نوع العمل
//   final double pricePerPiece;
//   final int quantity;
//   final int cutQuantity;
//   final String otherTypeName;  // اسم العمل الإضافي (اختياري)
//   final double otherTypePrice; // سعر العمل الإضافي (اختياري)
//   final double expense;
//   final DateTime date;

//   WorkModel({
//     this.id,
//     required this.workshopId,
//     required this.workType,
//     required this.pricePerPiece,
//     required this.quantity,
//     this.cutQuantity = 0,
//     this.otherTypeName = '',
//     this.otherTypePrice = 0.0,
//     required this.expense,
//     required this.date,
//   });

//   /// تحويل من خريطة (Map) إلى كائن WorkModel
//   factory WorkModel.fromMap(Map<String, dynamic> map) {
//     return WorkModel(
//       id: map['id'] as int?,
//       workshopId: map['workshopId'] as int,
//       workType: map['workType'] as String,
//       pricePerPiece: (map['pricePerPiece'] as num).toDouble(),
//       quantity: map['quantity'] as int,
//       cutQuantity: (map['cutQuantity'] as num?)?.toInt() ?? 0,
//       otherTypeName: map['otherTypeName'] as String? ?? '',
//       otherTypePrice: (map['otherTypePrice'] as num?)?.toDouble() ?? 0.0,
//       expense: (map['expense'] as num).toDouble(),
//       date: DateTime.parse(map['date'] as String),
//     );
//   }

//   /// تحويل من كائن WorkModel إلى خريطة لحفظها في SQLite أو Firebase
//   Map<String, dynamic> toMap() {
//     return {
//       'id': id,
//       'workshopId': workshopId,
//       'workType': workType,
//       'pricePerPiece': pricePerPiece,
//       'quantity': quantity,
//       'cutQuantity': cutQuantity,
//       'otherTypeName': otherTypeName,
//       'otherTypePrice': otherTypePrice,
//       'expense': expense,
//       'date': date.toIso8601String(),
//     };
//   }

//   /// إنشاء نسخة معدّلة من الكائن (مفيد للتحديث)
//   WorkModel copyWith({
//     int? id,
//     int? workshopId,
//     String? workType,
//     double? pricePerPiece,
//     int? quantity,
//     int? cutQuantity,
//     String? otherTypeName,
//     double? otherTypePrice,
//     double? expense,
//     DateTime? date,
//   }) {
//     return WorkModel(
//       id: id ?? this.id,
//       workshopId: workshopId ?? this.workshopId,
//       workType: workType ?? this.workType,
//       pricePerPiece: pricePerPiece ?? this.pricePerPiece,
//       quantity: quantity ?? this.quantity,
//       cutQuantity: cutQuantity ?? this.cutQuantity,
//       otherTypeName: otherTypeName ?? this.otherTypeName,
//       otherTypePrice: otherTypePrice ?? this.otherTypePrice,
//       expense: expense ?? this.expense,
//       date: date ?? this.date,
//     );
//   }
// }

class WorkModel {
  final int? id;
  final int workshopId;
  final String workType;
  final double pricePerPiece;
  final int quantity;
  final int cutQuantity;
  final int otherCount;
  final double expense;
  final String notes;
  final DateTime date;

  WorkModel({
    this.id,
    required this.workshopId,
    required this.workType,
    required this.pricePerPiece,
    required this.quantity,
    this.cutQuantity = 0,
    this.otherCount = 0,
    required this.expense,
    required this.notes,
    required this.date,
  });

  factory WorkModel.fromMap(Map<String, dynamic> map) {
    return WorkModel(
      id: map['id'] as int?,
      workshopId: map['workshopId'] as int,
      workType: map['workType'] as String,
      pricePerPiece: (map['pricePerPiece'] as num).toDouble(),
      quantity: map['quantity'] as int,
      cutQuantity: (map['cutQuantity'] as num?)?.toInt() ?? 0,
      otherCount: (map['otherCount'] as num?)?.toInt() ?? 0,
      expense: (map['expense'] as num).toDouble(),
      notes: map['notes'] as String,
      date: DateTime.parse(map['date'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'workshopId': workshopId,
      'workType': workType,
      'pricePerPiece': pricePerPiece,
      'quantity': quantity,
      'cutQuantity': cutQuantity,
      'otherCount': otherCount,
      'expense': expense,
      'notes': notes,
      'date': date.toIso8601String(),
    };
  }
}
