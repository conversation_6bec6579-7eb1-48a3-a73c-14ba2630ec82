import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../users/login_screen.dart';

class AccountScreen extends StatelessWidget {
  const AccountScreen({super.key});

  /// تسجيل الخروج بالكامل ومسح البيانات المحلية للّقب
  Future<void> _logout(BuildContext context) async {
    // 1. Firebase sign out
    await FirebaseAuth.instance.signOut();
    // 2. GoogleSignIn sign out
    await GoogleSignIn().signOut();
    // 3. مسح دور المستخدم من SharedPreferences
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('userRole');
    // 4. العودة لشاشة تسجيل الدخول وإزالة كافة الشاشات السابقة
    Navigator.of(context).pushAndRemoveUntil(
      MaterialPageRoute(builder: (_) => const LoginScreen()),
      (route) => false,
    );
  }

  /// تبديل الحساب: نفس خطوات الخروج ثم يدخل بحساب جديد
  Future<void> _switchAccount(BuildContext context) async {
    await _logout(context);
    // بعد ذلك سيظهر LoginScreen ويمكنك اختيار حساب جديد
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الحساب'),
        backgroundColor: Colors.teal,
      ),
      body: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ElevatedButton.icon(
              icon: const Icon(Icons.exit_to_app),
              label: const Text('تسجيل الخروج'),
              style: ElevatedButton.styleFrom(
                minimumSize: const Size(double.infinity, 50),
                backgroundColor: Colors.redAccent,
              ),
              onPressed: () => _logout(context),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              icon: const Icon(Icons.switch_account),
              label: const Text('تبديل الحساب'),
              style: ElevatedButton.styleFrom(
                minimumSize: const Size(double.infinity, 50),
                backgroundColor: Colors.blueAccent,
              ),
              onPressed: () => _switchAccount(context),
            ),
          ],
        ),
      ),
    );
  }
}
