// ملف اختبار للتأكد من حل مشاكل Overflow والأخطاء
import 'package:flutter/material.dart';
import 'screens/financial/financial_main_screen.dart';
import 'screens/financial/enhanced_statistics_screen.dart';

class TestOverflowAndErrorsFixed extends StatelessWidget {
  const TestOverflowAndErrorsFixed({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'اختبار حل مشاكل Overflow والأخطاء',
      theme: ThemeData(
        primarySwatch: Colors.green,
        fontFamily: 'Cairo',
      ),
      home: const TestFixedScreen(),
    );
  }
}

class TestFixedScreen extends StatelessWidget {
  const TestFixedScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار الإصلاحات'),
        backgroundColor: Colors.green[600],
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Icon(
              Icons.check_circle,
              size: 80,
              color: Colors.green,
            ),
            const SizedBox(height: 20),
            const Text(
              'تم حل جميع المشاكل!',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.green,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            const Text(
              'المشاكل التي تم حلها:',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            _buildFixedItem('✅ مشكلة Overflow في الصفحة الرئيسية'),
            _buildFixedItem('✅ مشكلة Overflow في صفحة الإحصائيات'),
            _buildFixedItem('✅ تقليل ارتفاع الهيدر من 220 إلى 180'),
            _buildFixedItem('✅ تقليل المسافات من 20 إلى 16'),
            _buildFixedItem('✅ تحسين التخطيط العام'),
            _buildFixedItem('✅ إصلاح اسم الدالة في قاعدة البيانات'),
            const SizedBox(height: 30),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const FinancialMainScreen(
                      tailorId: 1,
                      tailorName: 'خياط تجريبي - بدون Overflow',
                    ),
                  ),
                );
              },
              icon: const Icon(Icons.account_balance),
              label: const Text('اختبار النظام المالي'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green[600],
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const EnhancedStatisticsScreen(
                      workshopId: 1,
                      workshopName: 'معمل تجريبي - بدون Overflow',
                    ),
                  ),
                );
              },
              icon: const Icon(Icons.analytics),
              label: const Text('اختبار صفحة الإحصائيات'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.teal[600],
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
            const SizedBox(height: 20),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: const Column(
                children: [
                  Icon(Icons.info, color: Colors.blue, size: 24),
                  SizedBox(height: 8),
                  Text(
                    'النظام محسن للشاشات الصغيرة',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    'يعمل بسلاسة على جميع أحجام الشاشات',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.blue,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFixedItem(String fix) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              fix,
              style: const TextStyle(fontSize: 16),
            ),
          ),
        ],
      ),
    );
  }
}

void main() {
  runApp(const TestOverflowAndErrorsFixed());
}
