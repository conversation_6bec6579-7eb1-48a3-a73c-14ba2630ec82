// ملف اختبار للتأكد من حل مشكلة Overflow
import 'package:flutter/material.dart';
import 'screens/financial/financial_main_screen.dart';

class TestOverflowFixed extends StatelessWidget {
  const TestOverflowFixed({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'اختبار حل مشكلة Overflow',
      theme: ThemeData(
        primarySwatch: Colors.indigo,
        fontFamily: 'Cairo',
      ),
      home: const TestOverflowScreen(),
    );
  }
}

class TestOverflowScreen extends StatelessWidget {
  const TestOverflowScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار حل مشكلة Overflow'),
        backgroundColor: Colors.indigo[600],
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Icon(
              Icons.check_circle,
              size: 60,
              color: Colors.green,
            ),
            const SizedBox(height: 16),
            const Text(
              'تم حل مشكلة Overflow!',
              style: TextStyle(
                fontSize: 22,
                fontWeight: FontWeight.bold,
                color: Colors.green,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            const Text(
              'التحسينات المطبقة:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),
            _buildFixItem('✅ تقليل المسافات في الشبكة'),
            _buildFixItem('✅ تحسين نسبة العرض للارتفاع'),
            _buildFixItem('✅ تقليل حجم الأيقونات والنصوص'),
            _buildFixItem('✅ تقليل عدد النصائح المالية'),
            _buildFixItem('✅ تحسين التخطيط العام'),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const FinancialMainScreen(
                      tailorId: 1,
                      tailorName: 'خياط تجريبي - بدون Overflow',
                    ),
                  ),
                );
              },
              icon: const Icon(Icons.account_balance),
              label: const Text('اختبار النظام المالي'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.indigo[600],
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 14),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: const Column(
                children: [
                  Icon(Icons.info, color: Colors.blue, size: 24),
                  SizedBox(height: 8),
                  Text(
                    'النظام محسن للشاشات الصغيرة',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    'يعمل بسلاسة على جميع أحجام الشاشات',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.blue,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFixItem(String fix) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 6),
      child: Row(
        children: [
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              fix,
              style: const TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }
}

void main() {
  runApp(const TestOverflowFixed());
}
